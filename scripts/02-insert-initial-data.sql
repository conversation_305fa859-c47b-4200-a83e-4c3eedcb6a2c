-- =====================================================
-- Data Awal untuk Sistem Penyaluran Siswa Magang Jepang
-- =====================================================

-- Insert Users
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2b$10$example_hash', 'Administrator Sistem', 'admin'),
('operator1', '<EMAIL>', '$2b$10$example_hash', 'Operator Utama', 'operator'),
('lpk_admin', '<EMAIL>', '$2b$10$example_hash', 'Admin LPK', 'lpk_admin'),
('viewer1', '<EMAIL>', '$2b$10$example_hash', 'Viewer Sistem', 'viewer');

-- Insert Jenis Dokumen
INSERT INTO jenis_dokumen (nama_dokumen, deskripsi, wajib, kategori, urutan_tampil) VALUES
('KTP', 'Kartu Tanda Penduduk', true, 'personal', 1),
('Kartu Keluarga', 'Kartu Keluarga', true, 'personal', 2),
('Akta Kelahiran', 'Akta Kelahiran', true, 'personal', 3),
('Ijazah Terakhir', 'Ijazah Pendidikan Terakhir', true, 'pendidikan', 4),
('Transkrip Nilai', 'Transkrip Nilai Pendidikan Terakhir', true, 'pendidikan', 5),
('Sertifikat Kesehatan', 'Surat Keterangan Sehat dari Dokter', true, 'kesehatan', 6),
('SKCK', 'Surat Keterangan Catatan Kepolisian', true, 'legal', 7),
('Pas Foto', 'Pas Foto 4x6 Background Putih', true, 'personal', 8),
('Sertifikat Keahlian', 'Sertifikat Keahlian (jika ada)', false, 'pendidikan', 9),
('Surat Pernyataan Orang Tua', 'Surat Pernyataan Persetujuan Orang Tua', true, 'legal', 10);

-- Insert Program Pendidikan
INSERT INTO program_pendidikan (nama_program, deskripsi, durasi_bulan, biaya, status) VALUES
('Program Dasar Bahasa Jepang', 'Pelatihan bahasa Jepang tingkat dasar untuk persiapan magang', 3, 2500000.00, 'aktif'),
('Program Teknik Manufaktur', 'Pelatihan teknik manufaktur dan quality control', 4, 3500000.00, 'aktif'),
('Program Pertanian Modern', 'Pelatihan teknik pertanian modern dan greenhouse', 3, 3000000.00, 'aktif'),
('Program Konstruksi Bangunan', 'Pelatihan konstruksi dan bangunan', 4, 4000000.00, 'aktif'),
('Program Perawatan Lansia', 'Pelatihan perawatan lansia (kaigo)', 6, 5000000.00, 'aktif');

-- Insert LPK Mitra
INSERT INTO lpk_mitra (nama_lpk, alamat_lengkap, kota_kabupaten, provinsi, kontak_person, nomor_telepon, email, tanggal_registrasi, status) VALUES
('LPK Mitra Jaya', 'Jl. Sudirman No. 123, Kelurahan Menteng', 'Jakarta Pusat', 'DKI Jakarta', 'Budi Santoso', '021-12345678', '<EMAIL>', '2023-01-15', 'aktif'),
('LPK Karya Mandiri', 'Jl. Malioboro No. 45, Sosromenduran', 'Yogyakarta', 'DI Yogyakarta', 'Siti Nurhaliza', '0274-567890', '<EMAIL>', '2023-02-20', 'aktif'),
('LPK Harapan Bangsa', 'Jl. Asia Afrika No. 78, Sumur Bandung', 'Bandung', 'Jawa Barat', 'Ahmad Fauzi', '022-87654321', '<EMAIL>', '2023-03-10', 'aktif'),
('LPK Sejahtera', 'Jl. Thamrin No. 56, Medan Kota', 'Medan', 'Sumatera Utara', 'Linda Sari', '061-23456789', '<EMAIL>', '2023-04-05', 'aktif'),
('LPK Maju Bersama', 'Jl. Gajah Mada No. 89, Denpasar Barat', 'Denpasar', 'Bali', 'I Made Wirawan', '0361-345678', '<EMAIL>', '2023-05-12', 'aktif'),
('LPK Nusantara', 'Jl. Diponegoro No. 34, Semarang Tengah', 'Semarang', 'Jawa Tengah', 'Rina Wijaya', '024-76543210', '<EMAIL>', '2023-06-18', 'aktif');

-- Insert Kumiai
INSERT INTO kumiai (nama_kumiai, kode_kumiai, alamat, keterangan, status) VALUES
('Tokyo Manufacturing Association', 'TMA001', '1-2-3 Shibuya, Tokyo 150-0002, Japan', 'Asosiasi manufaktur di wilayah Tokyo', 'aktif'),
('Osaka Industrial Cooperative', 'OIC002', '4-5-6 Namba, Osaka 542-0076, Japan', 'Koperasi industri di wilayah Osaka', 'aktif'),
('Nagoya Technical Union', 'NTU003', '7-8-9 Sakae, Nagoya 460-0008, Japan', 'Serikat teknis di wilayah Nagoya', 'aktif'),
('Kyoto Agriculture Alliance', 'KAA004', '10-11-12 Gion, Kyoto 605-0001, Japan', 'Aliansi pertanian di wilayah Kyoto', 'aktif'),
('Hiroshima Construction Group', 'HCG005', '13-14-15 Hondori, Hiroshima 730-0035, Japan', 'Grup konstruksi di wilayah Hiroshima', 'aktif');

-- Insert Perusahaan Penerima
INSERT INTO perusahaan_penerima (kumiai_id, nama_perusahaan, kode_perusahaan, alamat_lengkap, kota, prefecture, bidang_usaha, kontak_person, telepon, kapasitas_siswa, status) VALUES
(1, 'Toyota Manufacturing Co.', 'TMC001', '1-1 Toyota-cho, Toyota-shi', 'Toyota', 'Aichi', 'Automotive Manufacturing', 'Tanaka Hiroshi', '+81-565-28-2121', 50, 'aktif'),
(1, 'Sony Electronics Ltd.', 'SEL002', '1-7-1 Konan, Minato-ku', 'Tokyo', 'Tokyo', 'Electronics', 'Yamada Kenji', '+81-3-6748-2111', 30, 'aktif'),
(2, 'Panasonic Corporation', 'PAN003', '1006 Oaza Kadoma, Kadoma-shi', 'Kadoma', 'Osaka', 'Electronics', 'Sato Yuki', '+81-6-6908-1121', 40, 'aktif'),
(2, 'Kansai Steel Works', 'KSW004', '2-3-4 Sumiyoshi, Osaka-shi', 'Osaka', 'Osaka', 'Steel Manufacturing', 'Suzuki Takeshi', '+81-6-6123-4567', 25, 'aktif'),
(3, 'Nagoya Precision Tools', 'NPT005', '5-6-7 Marunouchi, Nagoya-shi', 'Nagoya', 'Aichi', 'Precision Manufacturing', 'Watanabe Akira', '+81-52-201-2345', 20, 'aktif'),
(4, 'Kyoto Organic Farm', 'KOF006', '8-9-10 Fushimi, Kyoto-shi', 'Kyoto', 'Kyoto', 'Agriculture', 'Kimura Sachiko', '+81-75-601-1234', 15, 'aktif'),
(5, 'Hiroshima Construction', 'HRC007', '11-12-13 Naka-ku, Hiroshima-shi', 'Hiroshima', 'Hiroshima', 'Construction', 'Nakamura Taro', '+81-82-221-5678', 35, 'aktif');

-- Insert Pengaturan Sistem
INSERT INTO pengaturan_sistem (kunci, nilai, deskripsi, kategori) VALUES
('app_name', 'Sistem Penyaluran Siswa Magang Jepang', 'Nama aplikasi', 'general'),
('app_version', '1.0.0', 'Versi aplikasi', 'general'),
('max_file_upload', '5242880', 'Maksimal ukuran file upload (5MB)', 'upload'),
('allowed_file_types', 'pdf,jpg,jpeg,png,doc,docx', 'Tipe file yang diizinkan', 'upload'),
('email_notification', 'true', 'Aktifkan notifikasi email', 'notification'),
('backup_schedule', 'daily', 'Jadwal backup database', 'system'),
('session_timeout', '3600', 'Timeout session dalam detik', 'security');
