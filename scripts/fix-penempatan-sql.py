#!/usr/bin/env python3
"""
Script to fix penempatan_siswa INSERT statements by adding job_order_id column
"""

import re
import sys

def fix_penempatan_inserts(file_path):
    """Fix INSERT statements for penempatan_siswa table"""
    
    # Job order IDs to cycle through
    job_order_ids = [
        '1786d0bc-e5a2-41b7-8721-06bb6b43c124',  # Factory Worker - Manufacturing
        'cca564b8-65ba-456d-bf42-028ab646317f',  # Technical Assistant
        '8b2755e5-4a81-4451-8418-9da357208117'   # Automotive Parts Assembly
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match penempatan_siswa INSERT statements
        pattern = r"INSERT INTO penempatan_siswa \(id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus\) VALUES \('([^']+)', '([^']+)', '([^']+)', '([^']+)', '([^']+)', '([^']+)', ([^,]+), '([^']+)', '([^']+)', ([^,]+), '([^']+)', ([^,]+), '([^']+)'\);"
        
        def replace_insert(match):
            # Extract all groups
            groups = match.groups()
            id_val = groups[0]
            siswa_id = groups[1]
            perusahaan_id = groups[2]
            kumiai_id = groups[3]
            tanggal_penempatan = groups[4]
            tanggal_keberangkatan = groups[5]
            tanggal_kepulangan = groups[6]
            status_penempatan = groups[7]
            posisi_kerja = groups[8]
            gaji_aktual = groups[9]
            alamat_kerja = groups[10]
            evaluasi_bulanan = groups[11]
            catatan_khusus = groups[12]
            
            # Determine job_order_id based on perusahaan_id
            if perusahaan_id == '6f5daf14-c709-4b71-913c-12f4576694ba':  # Tokyo Manufacturing
                job_order_id = job_order_ids[0]
            elif perusahaan_id == 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc':  # Osaka Technical
                job_order_id = job_order_ids[1]
            elif perusahaan_id == '435ccc1c-7f9d-4997-9413-31c9e4947304':  # Nagoya Automotive
                job_order_id = job_order_ids[2]
            elif perusahaan_id == '7b6db58f-7c6c-4086-9fc3-8a136b20561e':  # Kyoto Electronics
                job_order_id = job_order_ids[0]  # Default to first job order
            else:
                job_order_id = job_order_ids[0]  # Default fallback
            
            # Reconstruct the INSERT statement with job_order_id
            return f"INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('{id_val}', '{siswa_id}', '{job_order_id}', '{perusahaan_id}', '{kumiai_id}', '{tanggal_penempatan}', '{tanggal_keberangkatan}', {tanggal_kepulangan}, '{status_penempatan}', '{posisi_kerja}', {gaji_aktual}, '{alamat_kerja}', {evaluasi_bulanan}, '{catatan_khusus}');"
        
        # Replace all matches
        fixed_content = re.sub(pattern, replace_insert, content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ Successfully fixed penempatan_siswa INSERT statements in {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing file {file_path}: {str(e)}")
        return False

def main():
    file_path = 'scripts/seed-data-full.sql'
    
    print("🔧 Fixing penempatan_siswa INSERT statements...")
    print(f"📁 File: {file_path}")
    
    success = fix_penempatan_inserts(file_path)
    
    if success:
        print("\n✅ All penempatan_siswa INSERT statements have been fixed!")
        print("📋 Changes made:")
        print("   - Added job_order_id column to all INSERT statements")
        print("   - Mapped job_order_id based on perusahaan_id")
        print("   - Maintained all existing data integrity")
        print("\n🚀 You can now run the seed data script safely!")
    else:
        print("\n❌ Failed to fix the INSERT statements")
        sys.exit(1)

if __name__ == "__main__":
    main()
