-- =====================================================
-- SQL Schema untuk Sistem Penyaluran Siswa Magang Jepang
-- =====================================================

-- Tabel Users (Pengguna Sistem)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'operator', 'lpk_admin', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel LPK Mitra (Lembaga Pelatihan Kerja)
CREATE TABLE lpk_mitra (
    id SERIAL PRIMARY KEY,
    nama_lpk VARCHAR(200) NOT NULL,
    alamat_lengkap TEXT NOT NULL,
    kota_kabupaten VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    catatan TEXT NULL,
    status ENUM('aktif', 'nonaktif', 'suspend') DEFAULT 'aktif',
    tanggal_registrasi DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Kumiai (Asosiasi Penyalur dari Jepang)
CREATE TABLE kumiai (
    id SERIAL PRIMARY KEY,
    nama_kumiai VARCHAR(200) NOT NULL,
    kode_kumiai VARCHAR(20) UNIQUE NOT NULL,
    alamat TEXT NULL,
    keterangan TEXT NULL,
    kontak_person VARCHAR(100) NULL,
    email VARCHAR(100) NULL,
    telepon VARCHAR(20) NULL,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Perusahaan Penerima di Jepang
CREATE TABLE perusahaan_penerima (
    id SERIAL PRIMARY KEY,
    kumiai_id INT NOT NULL,
    nama_perusahaan VARCHAR(200) NOT NULL,
    kode_perusahaan VARCHAR(50) NULL,
    alamat_lengkap TEXT NOT NULL,
    kota VARCHAR(100) NOT NULL,
    prefecture VARCHAR(100) NOT NULL,
    kode_pos VARCHAR(10) NULL,
    bidang_usaha VARCHAR(100) NULL,
    kontak_person VARCHAR(100) NULL,
    telepon VARCHAR(20) NULL,
    email VARCHAR(100) NULL,
    kapasitas_siswa INT DEFAULT 0,
    status ENUM('aktif', 'nonaktif', 'penuh') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (kumiai_id) REFERENCES kumiai(id) ON DELETE CASCADE
);

-- Tabel Program Pendidikan/Pelatihan
CREATE TABLE program_pendidikan (
    id SERIAL PRIMARY KEY,
    nama_program VARCHAR(200) NOT NULL,
    deskripsi TEXT NULL,
    durasi_bulan INT NOT NULL,
    biaya DECIMAL(15,2) NULL,
    kurikulum TEXT NULL,
    sertifikat VARCHAR(100) NULL,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Siswa
CREATE TABLE siswa (
    id SERIAL PRIMARY KEY,
    lpk_id INT NOT NULL,
    program_pendidikan_id INT NULL,
    
    -- Data Personal
    nama_lengkap VARCHAR(200) NOT NULL,
    nik VARCHAR(20) UNIQUE NOT NULL,
    tempat_lahir VARCHAR(100) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    agama VARCHAR(50) NOT NULL,
    status_pernikahan ENUM('belum_menikah', 'menikah', 'cerai') DEFAULT 'belum_menikah',
    
    -- Alamat
    alamat_lengkap TEXT NOT NULL,
    kelurahan VARCHAR(100) NOT NULL,
    kecamatan VARCHAR(100) NOT NULL,
    kota_kabupaten VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    kode_pos VARCHAR(10) NULL,
    
    -- Kontak
    nomor_hp VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    
    -- Pendidikan
    pendidikan_terakhir ENUM('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1', 'S2') NOT NULL,
    jurusan VARCHAR(100) NULL,
    tahun_lulus YEAR NULL,
    
    -- Keluarga
    nama_ayah VARCHAR(100) NULL,
    nama_ibu VARCHAR(100) NULL,
    pekerjaan_ayah VARCHAR(100) NULL,
    pekerjaan_ibu VARCHAR(100) NULL,
    kontak_darurat VARCHAR(20) NULL,
    
    -- Status Pendaftaran
    status_pendaftaran ENUM('draft', 'submitted', 'verified', 'approved', 'rejected') DEFAULT 'draft',
    tanggal_daftar DATE NOT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (lpk_id) REFERENCES lpk_mitra(id) ON DELETE CASCADE,
    FOREIGN KEY (program_pendidikan_id) REFERENCES program_pendidikan(id) ON DELETE SET NULL
);

-- Tabel Job Order
CREATE TABLE job_order (
    id SERIAL PRIMARY KEY,
    perusahaan_id INT NOT NULL,
    kumiai_id INT NOT NULL,
    
    -- Detail Pekerjaan
    judul_pekerjaan VARCHAR(200) NOT NULL,
    deskripsi_pekerjaan TEXT NOT NULL,
    posisi VARCHAR(100) NOT NULL,
    bidang_kerja VARCHAR(100) NOT NULL,
    
    -- Persyaratan
    jenis_kelamin ENUM('L', 'P', 'L/P') DEFAULT 'L/P',
    usia_min INT DEFAULT 18,
    usia_max INT DEFAULT 35,
    pendidikan_min ENUM('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1') DEFAULT 'SMA',
    pengalaman_kerja TEXT NULL,
    keahlian_khusus TEXT NULL,
    
    -- Kondisi Kerja
    gaji_pokok DECIMAL(15,2) NOT NULL,
    tunjangan DECIMAL(15,2) DEFAULT 0,
    jam_kerja_per_hari INT DEFAULT 8,
    hari_kerja_per_minggu INT DEFAULT 5,
    overtime_available BOOLEAN DEFAULT false,
    
    -- Fasilitas
    akomodasi TEXT NULL,
    transportasi TEXT NULL,
    asuransi TEXT NULL,
    fasilitas_lain TEXT NULL,
    
    -- Kuota dan Status
    kuota_total INT NOT NULL,
    kuota_terisi INT DEFAULT 0,
    tanggal_buka DATE NOT NULL,
    tanggal_tutup DATE NOT NULL,
    status ENUM('draft', 'open', 'closed', 'cancelled') DEFAULT 'draft',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (perusahaan_id) REFERENCES perusahaan_penerima(id) ON DELETE CASCADE,
    FOREIGN KEY (kumiai_id) REFERENCES kumiai(id) ON DELETE CASCADE
);

-- Tabel Penempatan Siswa
CREATE TABLE penempatan_siswa (
    id SERIAL PRIMARY KEY,
    siswa_id INT NOT NULL,
    job_order_id INT NOT NULL,
    perusahaan_id INT NOT NULL,
    kumiai_id INT NOT NULL,
    
    -- Detail Penempatan
    posisi_kerja VARCHAR(100) NOT NULL,
    gaji_pokok DECIMAL(15,2) NOT NULL,
    tunjangan DECIMAL(15,2) DEFAULT 0,
    
    -- Jadwal Keberangkatan
    tanggal_keberangkatan DATE NULL,
    bulan_keberangkatan INT NULL,
    tahun_keberangkatan YEAR NULL,
    
    -- Jadwal Kepulangan
    tanggal_kepulangan DATE NULL,
    bulan_kepulangan INT NULL,
    tahun_kepulangan YEAR NULL,
    
    -- Status
    status_penempatan ENUM('ditempatkan', 'berangkat', 'aktif', 'selesai', 'dibatalkan') DEFAULT 'ditempatkan',
    
    -- Catatan
    catatan TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (job_order_id) REFERENCES job_order(id) ON DELETE CASCADE,
    FOREIGN KEY (perusahaan_id) REFERENCES perusahaan_penerima(id) ON DELETE CASCADE,
    FOREIGN KEY (kumiai_id) REFERENCES kumiai(id) ON DELETE CASCADE
);

-- Tabel Jenis Dokumen
CREATE TABLE jenis_dokumen (
    id SERIAL PRIMARY KEY,
    nama_dokumen VARCHAR(100) NOT NULL,
    deskripsi TEXT NULL,
    wajib BOOLEAN DEFAULT true,
    kategori ENUM('personal', 'pendidikan', 'kesehatan', 'legal', 'lainnya') DEFAULT 'personal',
    urutan_tampil INT DEFAULT 0,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel Dokumen Siswa
CREATE TABLE dokumen_siswa (
    id SERIAL PRIMARY KEY,
    siswa_id INT NOT NULL,
    jenis_dokumen_id INT NOT NULL,
    
    -- File Info
    nama_file VARCHAR(255) NOT NULL,
    path_file VARCHAR(500) NOT NULL,
    ukuran_file BIGINT NOT NULL,
    tipe_file VARCHAR(50) NOT NULL,
    
    -- Status Verifikasi
    status_verifikasi ENUM('pending', 'approved', 'rejected', 'revision') DEFAULT 'pending',
    catatan_verifikasi TEXT NULL,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    
    -- Upload Info
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    FOREIGN KEY (jenis_dokumen_id) REFERENCES jenis_dokumen(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabel Jadwal Pelatihan
CREATE TABLE jadwal_pelatihan (
    id SERIAL PRIMARY KEY,
    program_pendidikan_id INT NOT NULL,
    nama_pelatihan VARCHAR(200) NOT NULL,
    deskripsi TEXT NULL,
    tanggal_mulai DATE NOT NULL,
    tanggal_selesai DATE NOT NULL,
    jam_mulai TIME NOT NULL,
    jam_selesai TIME NOT NULL,
    tempat VARCHAR(200) NOT NULL,
    instruktur VARCHAR(100) NULL,
    kuota_peserta INT DEFAULT 0,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (program_pendidikan_id) REFERENCES program_pendidikan(id) ON DELETE CASCADE
);

-- Tabel Peserta Pelatihan
CREATE TABLE peserta_pelatihan (
    id SERIAL PRIMARY KEY,
    jadwal_pelatihan_id INT NOT NULL,
    siswa_id INT NOT NULL,
    status_kehadiran ENUM('registered', 'present', 'absent', 'excused') DEFAULT 'registered',
    nilai DECIMAL(5,2) NULL,
    catatan TEXT NULL,
    tanggal_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (jadwal_pelatihan_id) REFERENCES jadwal_pelatihan(id) ON DELETE CASCADE,
    FOREIGN KEY (siswa_id) REFERENCES siswa(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_peserta (jadwal_pelatihan_id, siswa_id)
);

-- Tabel Log Aktivitas
CREATE TABLE log_aktivitas (
    id SERIAL PRIMARY KEY,
    user_id INT NULL,
    tabel_terkait VARCHAR(50) NOT NULL,
    id_record INT NOT NULL,
    aksi ENUM('create', 'update', 'delete', 'view') NOT NULL,
    data_lama JSON NULL,
    data_baru JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabel Pengaturan Sistem
CREATE TABLE pengaturan_sistem (
    id SERIAL PRIMARY KEY,
    kunci VARCHAR(100) UNIQUE NOT NULL,
    nilai TEXT NOT NULL,
    deskripsi TEXT NULL,
    kategori VARCHAR(50) DEFAULT 'general',
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- =====================================================
-- INDEXES untuk Optimasi Performance
-- =====================================================

-- Index untuk pencarian siswa
CREATE INDEX idx_siswa_nama ON siswa(nama_lengkap);
CREATE INDEX idx_siswa_nik ON siswa(nik);
CREATE INDEX idx_siswa_lpk ON siswa(lpk_id);
CREATE INDEX idx_siswa_status ON siswa(status_pendaftaran);

-- Index untuk pencarian LPK
CREATE INDEX idx_lpk_nama ON lpk_mitra(nama_lpk);
CREATE INDEX idx_lpk_kota ON lpk_mitra(kota_kabupaten);
CREATE INDEX idx_lpk_status ON lpk_mitra(status);

-- Index untuk pencarian Kumiai
CREATE INDEX idx_kumiai_nama ON kumiai(nama_kumiai);
CREATE INDEX idx_kumiai_kode ON kumiai(kode_kumiai);

-- Index untuk pencarian Perusahaan
CREATE INDEX idx_perusahaan_nama ON perusahaan_penerima(nama_perusahaan);
CREATE INDEX idx_perusahaan_kumiai ON perusahaan_penerima(kumiai_id);
CREATE INDEX idx_perusahaan_prefecture ON perusahaan_penerima(prefecture);

-- Index untuk Job Order
CREATE INDEX idx_job_order_status ON job_order(status);
CREATE INDEX idx_job_order_tanggal ON job_order(tanggal_buka, tanggal_tutup);

-- Index untuk Penempatan
CREATE INDEX idx_penempatan_siswa ON penempatan_siswa(siswa_id);
CREATE INDEX idx_penempatan_status ON penempatan_siswa(status_penempatan);
CREATE INDEX idx_penempatan_tahun ON penempatan_siswa(tahun_keberangkatan);

-- Index untuk Dokumen
CREATE INDEX idx_dokumen_siswa ON dokumen_siswa(siswa_id);
CREATE INDEX idx_dokumen_status ON dokumen_siswa(status_verifikasi);

-- Index untuk Log
CREATE INDEX idx_log_user ON log_aktivitas(user_id);
CREATE INDEX idx_log_tabel ON log_aktivitas(tabel_terkait, id_record);
CREATE INDEX idx_log_tanggal ON log_aktivitas(created_at);
