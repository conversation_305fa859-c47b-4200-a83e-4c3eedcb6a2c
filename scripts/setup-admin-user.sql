-- =====================================================
-- Setup Admin User Script
-- Dashboard Magang Jepang - RBAC System
-- =====================================================

-- This script helps you setup the first admin user
-- Replace 'YOUR_USER_ID' with the actual user ID from Supabase Auth

-- =====================================================
-- Step 1: Create user in Supabase Dashboard first
-- =====================================================
-- 1. Go to Supabase Dashboard > Authentication > Users
-- 2. Click "Add user" 
-- 3. Enter email and password
-- 4. Copy the User ID from the created user
-- 5. Replace 'YOUR_USER_ID' below with the actual ID

-- =====================================================
-- Step 2: Insert user profile
-- =====================================================

-- Replace 'YOUR_USER_ID' with actual user ID from Supabase Auth
INSERT INTO user_profiles (
    id, 
    full_name, 
    username, 
    is_active,
    created_at,
    updated_at
) VALUES (
    'YOUR_USER_ID'::uuid,  -- Replace with actual user ID
    'System Administrator',
    'admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    username = EXCLUDED.username,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- =====================================================
-- Step 3: Assign administrator role
-- =====================================================

INSERT INTO user_role_assignments (
    user_id,
    role_id,
    assigned_by,
    is_active,
    assigned_at
) 
SELECT 
    'YOUR_USER_ID'::uuid,  -- Replace with actual user ID
    r.id,
    'YOUR_USER_ID'::uuid,  -- Self-assigned
    true,
    NOW()
FROM roles r 
WHERE r.name = 'administrator'
ON CONFLICT (user_id, role_id) DO UPDATE SET
    is_active = true,
    assigned_at = NOW();

-- =====================================================
-- Step 4: Verify setup
-- =====================================================

-- Check if user profile was created
SELECT 
    'User Profile Check' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM user_profiles WHERE id = 'YOUR_USER_ID'::uuid) 
        THEN '✅ User profile created successfully'
        ELSE '❌ User profile not found - check user ID'
    END as result;

-- Check if admin role was assigned
SELECT 
    'Admin Role Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_role_assignments ura
            JOIN roles r ON ura.role_id = r.id
            WHERE ura.user_id = 'YOUR_USER_ID'::uuid 
            AND r.name = 'administrator'
            AND ura.is_active = true
        ) 
        THEN '✅ Administrator role assigned successfully'
        ELSE '❌ Administrator role not assigned - check setup'
    END as result;

-- Show user details
SELECT 
    up.id,
    up.full_name,
    up.username,
    up.is_active,
    r.display_name as role,
    ura.assigned_at
FROM user_profiles up
LEFT JOIN user_role_assignments ura ON up.id = ura.user_id AND ura.is_active = true
LEFT JOIN roles r ON ura.role_id = r.id
WHERE up.id = 'YOUR_USER_ID'::uuid;

-- =====================================================
-- Alternative: Quick setup with email lookup
-- =====================================================

-- Setup for demo user: <EMAIL>
-- (Uncomment this section if you created the demo user)

/*
-- Get user ID from email
WITH admin_user AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
-- Insert profile
INSERT INTO user_profiles (id, full_name, username, is_active)
SELECT id, 'System Administrator', 'admin', true
FROM admin_user
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    username = EXCLUDED.username,
    updated_at = NOW();

-- Assign admin role
WITH admin_user AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
INSERT INTO user_role_assignments (user_id, role_id, assigned_by, is_active, assigned_at)
SELECT
    au.id,
    r.id,
    au.id,
    true,
    NOW()
FROM admin_user au, roles r
WHERE r.name = 'administrator'
ON CONFLICT (user_id, role_id) DO UPDATE SET
    is_active = true,
    assigned_at = NOW();

-- Verify demo user setup
SELECT
    'Demo User Verification' as check_type,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM auth.users au
            JOIN user_profiles up ON au.id = up.id
            JOIN user_role_assignments ura ON up.id = ura.user_id
            JOIN roles r ON ura.role_id = r.id
            WHERE au.email = '<EMAIL>'
            AND r.name = 'administrator'
            AND ura.is_active = true
        )
        THEN '✅ Demo user setup complete - you can <NAME_EMAIL>'
        ELSE '❌ Demo user not found - create user in Supabase Dashboard first'
    END as result;
*/

-- =====================================================
-- Success message
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 Admin user setup script completed!';
    RAISE NOTICE 'Make sure to:';
    RAISE NOTICE '1. Replace YOUR_USER_ID with actual user ID';
    RAISE NOTICE '2. Check the verification results above';
    RAISE NOTICE '3. Test login with the admin user';
    RAISE NOTICE '4. Access /users and /roles pages to manage the system';
END $$;
