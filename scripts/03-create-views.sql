-- =====================================================
-- Views untuk Laporan dan Dashboard
-- =====================================================

-- View untuk Dashboard Statistik
CREATE VIEW v_dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM siswa) as total_siswa,
    (SELECT COUNT(*) FROM siswa WHERE status_pendaftaran = 'approved') as siswa_approved,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'aktif') as siswa_aktif_jepang,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'berangkat') as siswa_berangkat,
    (SELECT COUNT(*) FROM penempatan_siswa WHERE status_penempatan = 'selesai') as siswa_selesai,
    (SELECT COUNT(*) FROM lpk_mitra WHERE status = 'aktif') as lpk_aktif,
    (SELECT COUNT(*) FROM kumiai WHERE status = 'aktif') as kumiai_aktif,
    (SELECT COUNT(*) FROM perusahaan_penerima WHERE status = 'aktif') as perusahaan_aktif,
    (SELECT COUNT(*) FROM job_order WHERE status = 'open') as job_order_open;

-- View untuk Data Siswa Lengkap
CREATE VIEW v_siswa_lengkap AS
SELECT 
    s.id,
    s.nama_lengkap,
    s.nik,
    s.jenis_kelamin,
    s.tanggal_lahir,
    TIMESTAMPDIFF(YEAR, s.tanggal_lahir, CURDATE()) as usia,
    s.alamat_lengkap,
    s.kota_kabupaten,
    s.provinsi,
    s.nomor_hp,
    s.email,
    s.pendidikan_terakhir,
    s.status_pendaftaran,
    s.tanggal_daftar,
    
    -- Data LPK
    l.nama_lpk,
    l.kota_kabupaten as lpk_kota,
    
    -- Data Program
    p.nama_program,
    
    -- Data Penempatan (jika ada)
    pen.status_penempatan,
    pen.tanggal_keberangkatan,
    pen.tanggal_kepulangan,
    per.nama_perusahaan,
    per.prefecture,
    k.nama_kumiai,
    
    s.created_at,
    s.updated_at
FROM siswa s
LEFT JOIN lpk_mitra l ON s.lpk_id = l.id
LEFT JOIN program_pendidikan p ON s.program_pendidikan_id = p.id
LEFT JOIN penempatan_siswa pen ON s.id = pen.siswa_id
LEFT JOIN perusahaan_penerima per ON pen.perusahaan_id = per.id
LEFT JOIN kumiai k ON pen.kumiai_id = k.id;

-- View untuk Laporan Penempatan
CREATE VIEW v_laporan_penempatan AS
SELECT 
    pen.id,
    s.nama_lengkap as nama_siswa,
    s.jenis_kelamin,
    l.nama_lpk,
    per.nama_perusahaan,
    per.prefecture,
    per.kota,
    k.nama_kumiai,
    pen.posisi_kerja,
    pen.gaji_pokok,
    pen.tanggal_keberangkatan,
    pen.bulan_keberangkatan,
    pen.tahun_keberangkatan,
    pen.tanggal_kepulangan,
    pen.bulan_kepulangan,
    pen.tahun_kepulangan,
    pen.status_penempatan,
    pen.created_at as tanggal_penempatan
FROM penempatan_siswa pen
JOIN siswa s ON pen.siswa_id = s.id
JOIN lpk_mitra l ON s.lpk_id = l.id
JOIN perusahaan_penerima per ON pen.perusahaan_id = per.id
JOIN kumiai k ON pen.kumiai_id = k.id;

-- View untuk Status Dokumen Siswa
CREATE VIEW v_status_dokumen_siswa AS
SELECT 
    s.id as siswa_id,
    s.nama_lengkap,
    l.nama_lpk,
    COUNT(jd.id) as total_dokumen_wajib,
    COUNT(ds.id) as dokumen_uploaded,
    COUNT(CASE WHEN ds.status_verifikasi = 'approved' THEN 1 END) as dokumen_approved,
    COUNT(CASE WHEN ds.status_verifikasi = 'rejected' THEN 1 END) as dokumen_rejected,
    COUNT(CASE WHEN ds.status_verifikasi = 'pending' THEN 1 END) as dokumen_pending,
    CASE 
        WHEN COUNT(jd.id) = COUNT(CASE WHEN ds.status_verifikasi = 'approved' THEN 1 END) THEN 'Lengkap'
        WHEN COUNT(ds.id) = 0 THEN 'Belum Upload'
        ELSE 'Tidak Lengkap'
    END as status_kelengkapan
FROM siswa s
LEFT JOIN lpk_mitra l ON s.lpk_id = l.id
LEFT JOIN jenis_dokumen jd ON jd.wajib = true AND jd.status = 'aktif'
LEFT JOIN dokumen_siswa ds ON s.id = ds.siswa_id AND ds.jenis_dokumen_id = jd.id
GROUP BY s.id, s.nama_lengkap, l.nama_lpk;

-- View untuk Statistik LPK
CREATE VIEW v_statistik_lpk AS
SELECT 
    l.id,
    l.nama_lpk,
    l.kota_kabupaten,
    l.status,
    COUNT(s.id) as total_siswa,
    COUNT(CASE WHEN s.status_pendaftaran = 'approved' THEN 1 END) as siswa_approved,
    COUNT(CASE WHEN pen.status_penempatan = 'aktif' THEN 1 END) as siswa_aktif_jepang,
    COUNT(CASE WHEN pen.status_penempatan = 'selesai' THEN 1 END) as siswa_selesai,
    l.tanggal_registrasi,
    l.created_at
FROM lpk_mitra l
LEFT JOIN siswa s ON l.id = s.lpk_id
LEFT JOIN penempatan_siswa pen ON s.id = pen.siswa_id
GROUP BY l.id, l.nama_lpk, l.kota_kabupaten, l.status, l.tanggal_registrasi, l.created_at;

-- View untuk Statistik Kumiai
CREATE VIEW v_statistik_kumiai AS
SELECT 
    k.id,
    k.nama_kumiai,
    k.kode_kumiai,
    k.status,
    COUNT(DISTINCT per.id) as total_perusahaan,
    COUNT(DISTINCT pen.siswa_id) as total_siswa_ditempatkan,
    COUNT(CASE WHEN pen.status_penempatan = 'aktif' THEN 1 END) as siswa_aktif,
    SUM(per.kapasitas_siswa) as total_kapasitas,
    k.created_at
FROM kumiai k
LEFT JOIN perusahaan_penerima per ON k.id = per.kumiai_id
LEFT JOIN penempatan_siswa pen ON per.id = pen.perusahaan_id
GROUP BY k.id, k.nama_kumiai, k.kode_kumiai, k.status, k.created_at;
