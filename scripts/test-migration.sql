-- Test Migration Script
-- Dashboard Sistem Magang Jepang
-- This script tests if the migration can run successfully

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Test basic schema creation (simplified version)
DO $$
BEGIN
    -- Test if tables exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'lpk_mitra') THEN
        RAISE NOTICE 'Table lpk_mitra does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table lpk_mitra exists - OK';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'kumiai') THEN
        RAISE NOTICE 'Table kumiai does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table kumiai exists - OK';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'perusahaan_penerima') THEN
        RAISE NOTICE 'Table perusahaan_penerima does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table perusahaan_penerima exists - OK';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'job_order') THEN
        RAISE NOTICE 'Table job_order does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table job_order exists - OK';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'siswa') THEN
        RAISE NOTICE 'Table siswa does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table siswa exists - OK';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'penempatan_siswa') THEN
        RAISE NOTICE 'Table penempatan_siswa does not exist - migration needed';
    ELSE
        RAISE NOTICE 'Table penempatan_siswa exists - OK';
    END IF;
END $$;

-- Test data counts
DO $$
DECLARE
    lpk_count INTEGER;
    kumiai_count INTEGER;
    perusahaan_count INTEGER;
    job_order_count INTEGER;
    siswa_count INTEGER;
    penempatan_count INTEGER;
BEGIN
    -- Count existing data
    SELECT COUNT(*) INTO lpk_count FROM lpk_mitra WHERE 1=1;
    SELECT COUNT(*) INTO kumiai_count FROM kumiai WHERE 1=1;
    SELECT COUNT(*) INTO perusahaan_count FROM perusahaan_penerima WHERE 1=1;
    SELECT COUNT(*) INTO job_order_count FROM job_order WHERE 1=1;
    SELECT COUNT(*) INTO siswa_count FROM siswa WHERE 1=1;
    SELECT COUNT(*) INTO penempatan_count FROM penempatan_siswa WHERE 1=1;
    
    RAISE NOTICE 'Current data counts:';
    RAISE NOTICE '- LPK Mitra: %', lpk_count;
    RAISE NOTICE '- Kumiai: %', kumiai_count;
    RAISE NOTICE '- Perusahaan: %', perusahaan_count;
    RAISE NOTICE '- Job Order: %', job_order_count;
    RAISE NOTICE '- Siswa: %', siswa_count;
    RAISE NOTICE '- Penempatan: %', penempatan_count;
    
    IF lpk_count = 0 THEN
        RAISE NOTICE 'No LPK data found - seed data needed';
    END IF;
    
    IF siswa_count = 0 THEN
        RAISE NOTICE 'No student data found - seed data needed';
    END IF;
END $$;

-- Test views and functions
DO $$
BEGIN
    -- Test if views exist
    IF EXISTS (SELECT FROM information_schema.views WHERE table_name = 'v_dashboard_statistics') THEN
        RAISE NOTICE 'View v_dashboard_statistics exists - OK';
    ELSE
        RAISE NOTICE 'View v_dashboard_statistics missing - migration needed';
    END IF;
    
    -- Test if functions exist
    IF EXISTS (SELECT FROM information_schema.routines WHERE routine_name = 'get_monthly_statistics') THEN
        RAISE NOTICE 'Function get_monthly_statistics exists - OK';
    ELSE
        RAISE NOTICE 'Function get_monthly_statistics missing - migration needed';
    END IF;
END $$;

-- Test sample queries (only if data exists)
DO $$
DECLARE
    siswa_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO siswa_count FROM siswa WHERE 1=1;
    
    IF siswa_count > 0 THEN
        RAISE NOTICE 'Testing sample queries...';
        
        -- Test dashboard statistics
        PERFORM * FROM v_dashboard_statistics LIMIT 1;
        RAISE NOTICE 'Dashboard statistics query - OK';
        
        -- Test monthly statistics
        PERFORM * FROM get_monthly_statistics(2024) LIMIT 1;
        RAISE NOTICE 'Monthly statistics function - OK';
        
        -- Test LPK performance
        PERFORM * FROM get_lpk_performance() LIMIT 1;
        RAISE NOTICE 'LPK performance function - OK';
        
    ELSE
        RAISE NOTICE 'No student data available for testing queries';
    END IF;
END $$;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '=================================';
    RAISE NOTICE 'Migration test completed!';
    RAISE NOTICE '=================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. If tables are missing, run: supabase db push';
    RAISE NOTICE '2. If data is missing, run seed data scripts:';
    RAISE NOTICE '   - For sample: psql $DATABASE_URL -f seed-data-yutaka.sql';
    RAISE NOTICE '   - For full: psql $DATABASE_URL -f seed-data-full.sql';
    RAISE NOTICE '3. Test the application dashboard';
END $$;
