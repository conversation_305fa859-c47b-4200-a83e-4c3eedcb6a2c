-- =====================================================
-- RBAC System Testing and Validation Script
-- Dashboard Magang Jepang
-- =====================================================

-- =====================================================
-- 1. Verify Tables and Structure
-- =====================================================

-- Check if all RBAC tables exist
SELECT 
    schemaname,
    tablename,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE tablename IN ('roles', 'permissions', 'role_permissions', 'user_role_assignments', 'user_profiles')
ORDER BY tablename;

-- Check table row counts
SELECT 
    'roles' as table_name, COUNT(*) as row_count FROM roles
UNION ALL
SELECT 
    'permissions' as table_name, COUNT(*) as row_count FROM permissions
UNION ALL
SELECT 
    'role_permissions' as table_name, COUNT(*) as row_count FROM role_permissions
UNION ALL
SELECT 
    'user_role_assignments' as table_name, COUNT(*) as row_count FROM user_role_assignments
UNION ALL
SELECT 
    'user_profiles' as table_name, COUNT(*) as row_count FROM user_profiles;

-- =====================================================
-- 2. Verify Default Roles and Permissions
-- =====================================================

-- Check all roles
SELECT 
    name,
    display_name,
    is_active,
    created_at
FROM roles
ORDER BY name;

-- Check permissions by module
SELECT 
    module,
    COUNT(*) as permission_count,
    STRING_AGG(action, ', ' ORDER BY action) as actions
FROM permissions
GROUP BY module
ORDER BY module;

-- Check role-permission assignments
SELECT 
    r.display_name as role,
    COUNT(rp.permission_id) as permission_count
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
GROUP BY r.id, r.display_name
ORDER BY r.display_name;

-- =====================================================
-- 3. Test Helper Functions
-- =====================================================

-- Test permission checking functions (requires authenticated user)
-- These will work when called from application context

-- Example: Check if functions exist
SELECT 
    proname as function_name,
    pronargs as arg_count,
    prorettype::regtype as return_type
FROM pg_proc 
WHERE proname IN ('get_user_permissions', 'user_has_permission', 'get_user_roles', 'assign_user_role', 'revoke_user_role')
ORDER BY proname;

-- =====================================================
-- 4. Verify RLS Policies
-- =====================================================

-- Check which tables have RLS enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('user_profiles', 'siswa', 'lpk_mitra', 'kumiai', 'job_order', 'penempatan_siswa', 'dokumen_siswa')
ORDER BY tablename;

-- Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- 5. Test Data Scenarios
-- =====================================================

-- Create test LPK Mitra (if not exists)
INSERT INTO lpk_mitra (nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email)
VALUES ('LPK Test A', 'Jl. Test A No. 1', 'Jakarta', 'DKI Jakarta', 'Pimpinan A', 'Kontak A', '081234567890', '<EMAIL>')
ON CONFLICT DO NOTHING;

INSERT INTO lpk_mitra (nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email)
VALUES ('LPK Test B', 'Jl. Test B No. 2', 'Bandung', 'Jawa Barat', 'Pimpinan B', 'Kontak B', '081234567891', '<EMAIL>')
ON CONFLICT DO NOTHING;

-- =====================================================
-- 6. Validation Queries
-- =====================================================

-- Check role permission matrix
SELECT 
    r.display_name as role,
    p.module,
    p.action,
    CASE WHEN rp.id IS NOT NULL THEN '✓' ELSE '✗' END as has_permission
FROM roles r
CROSS JOIN permissions p
LEFT JOIN role_permissions rp ON r.id = rp.role_id AND p.id = rp.permission_id
WHERE r.is_active = true
ORDER BY r.display_name, p.module, 
    CASE p.action 
        WHEN 'read' THEN 1 
        WHEN 'create' THEN 2 
        WHEN 'update' THEN 3 
        WHEN 'delete' THEN 4 
        ELSE 5 
    END;

-- Check user role assignments (if any users exist)
SELECT 
    up.full_name,
    up.username,
    r.display_name as role,
    ura.is_active,
    lm.nama_lpk as lpk_mitra,
    ura.assigned_at
FROM user_profiles up
LEFT JOIN user_role_assignments ura ON up.id = ura.user_id
LEFT JOIN roles r ON ura.role_id = r.id
LEFT JOIN lpk_mitra lm ON ura.lpk_mitra_id = lm.id
ORDER BY up.full_name, r.display_name;

-- =====================================================
-- 7. Performance Check
-- =====================================================

-- Check indexes on important columns
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
AND tablename IN ('user_role_assignments', 'role_permissions', 'user_profiles')
ORDER BY tablename, indexname;

-- =====================================================
-- 8. Data Integrity Checks
-- =====================================================

-- Check for orphaned role assignments
SELECT 
    'Orphaned role assignments' as issue,
    COUNT(*) as count
FROM user_role_assignments ura
LEFT JOIN roles r ON ura.role_id = r.id
WHERE r.id IS NULL;

-- Check for orphaned role permissions
SELECT 
    'Orphaned role permissions' as issue,
    COUNT(*) as count
FROM role_permissions rp
LEFT JOIN roles r ON rp.role_id = r.id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE r.id IS NULL OR p.id IS NULL;

-- Check for users without profiles
SELECT 
    'Users without profiles' as issue,
    COUNT(*) as count
FROM auth.users au
LEFT JOIN user_profiles up ON au.id = up.id
WHERE up.id IS NULL;

-- =====================================================
-- 9. Security Validation
-- =====================================================

-- Check if RLS helper functions are properly secured
SELECT 
    proname,
    prosecdef as security_definer,
    provolatile as volatility
FROM pg_proc 
WHERE proname IN ('user_has_role', 'user_lpk_mitra_id')
ORDER BY proname;

-- =====================================================
-- 10. Summary Report
-- =====================================================

-- Generate summary report
WITH role_stats AS (
    SELECT 
        r.name,
        r.display_name,
        COUNT(rp.permission_id) as permission_count,
        COUNT(ura.user_id) as user_count
    FROM roles r
    LEFT JOIN role_permissions rp ON r.id = rp.role_id
    LEFT JOIN user_role_assignments ura ON r.id = ura.role_id AND ura.is_active = true
    GROUP BY r.id, r.name, r.display_name
)
SELECT 
    'RBAC System Summary' as report_section,
    json_build_object(
        'total_roles', (SELECT COUNT(*) FROM roles WHERE is_active = true),
        'total_permissions', (SELECT COUNT(*) FROM permissions),
        'total_users', (SELECT COUNT(*) FROM user_profiles WHERE is_active = true),
        'total_active_assignments', (SELECT COUNT(*) FROM user_role_assignments WHERE is_active = true),
        'roles_detail', (
            SELECT json_agg(
                json_build_object(
                    'role', display_name,
                    'permissions', permission_count,
                    'users', user_count
                )
            )
            FROM role_stats
        )
    ) as summary;

-- =====================================================
-- Test Completion Message
-- =====================================================

SELECT 
    '🎉 RBAC System Testing Complete!' as message,
    'Check the results above to verify system integrity' as instruction,
    NOW() as tested_at;
