# 🔐 Testing Authentication Flow

## Steps to Test

### 1. **Start the Application**
```bash
npm run dev
```

### 2. **Test Unauthenticated Access**
- Open browser to `http://localhost:3000`
- Should redirect to `http://localhost:3000/login`
- Should NOT see infinite redirect loop
- Should see login form

### 3. **Test Login Page**
- Should see branded login form
- Should see demo credentials box
- Should be able to type in email/password fields
- Should see password toggle button

### 4. **Test Login Process**
- Use demo credentials:
  - Email: `<EMAIL>`
  - Password: `admin123`
- Click "Masuk" button
- Should see loading state
- Should redirect to dashboard after successful login

### 5. **Test Authenticated Access**
- Should see dashboard with sidebar
- Should see user name in header
- Should see logout button
- Navigation should show only permitted items

### 6. **Test Logout**
- Click logout button in header
- Should redirect back to login page
- Should clear session

### 7. **Test Direct Page Access**
- Try accessing `http://localhost:3000/users` directly
- Should redirect to login if not authenticated
- Should show page if authenticated with proper permissions

## Expected Behavior

### ✅ Login Page (`/login`)
- ✅ No infinite redirect loop
- ✅ Clean login form with branding
- ✅ Demo credentials display
- ✅ Form validation and error handling
- ✅ Loading states during login
- ✅ Auto-redirect if already logged in

### ✅ Authentication Flow
- ✅ Unauthenticated users redirect to login
- ✅ Successful login redirects to dashboard
- ✅ Session persists across page refresh
- ✅ Logout clears session and redirects to login

### ✅ Protected Routes
- ✅ Dashboard requires authentication
- ✅ User management requires admin permissions
- ✅ Role management requires admin permissions
- ✅ Graceful error handling for unauthorized access

## Troubleshooting

### Issue: Infinite Redirect Loop
**Symptoms**: Console shows repeated `GET /login 200` requests
**Solution**: 
- Check that login page has its own layout (`app/login/layout.tsx`)
- Verify AuthContext doesn't redirect when already on login page
- Ensure login page doesn't use `useAuth()` hook

### Issue: Login Form Not Working
**Symptoms**: Login button doesn't work or shows errors
**Solution**:
- Verify Supabase connection
- Check that demo user exists in Supabase Auth
- Verify user profile and role assignments in database

### Issue: No Navigation After Login
**Symptoms**: Login successful but no sidebar/navigation
**Solution**:
- Check user has proper role assignments
- Verify permissions are loaded correctly
- Check browser console for errors

### Issue: Permission Denied
**Symptoms**: User can login but can't access pages
**Solution**:
- Verify user has administrator role
- Check role-permission assignments
- Run RBAC test script to verify setup

## Debug Commands

### Check User Session
```javascript
// In browser console
supabase.auth.getSession().then(console.log)
```

### Check User Permissions
```sql
-- In Supabase SQL Editor
SELECT * FROM v_user_permissions WHERE user_id = 'USER_ID_HERE';
```

### Verify Role Assignments
```sql
-- In Supabase SQL Editor
SELECT * FROM v_user_roles WHERE id = 'USER_ID_HERE';
```
