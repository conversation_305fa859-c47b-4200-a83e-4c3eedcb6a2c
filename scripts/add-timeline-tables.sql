-- =====================================================
-- Timeline Progress Tables untuk Student Tracking
-- =====================================================

-- Tabel Master Timeline Stages
CREATE TABLE timeline_stages (
    id SERIAL PRIMARY KEY,
    nama_stage VARCHAR(100) NOT NULL,
    deskripsi TEXT NOT NULL,
    urutan INTEGER NOT NULL,
    icon VARCHAR(50) NOT NULL,
    kategori ENUM('preparation', 'selection', 'documentation', 'training', 'deployment') DEFAULT 'preparation',
    is_aktif BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_urutan (urutan)
);

-- Tabel Progress Siswa
CREATE TABLE siswa_timeline_progress (
    id SERIAL PRIMARY KEY,
    siswa_id VARCHAR(255) NOT NULL, -- UUID from siswa table
    timeline_stage_id INT NOT NULL,
    status ENUM('belum_mulai', 'berlangsung', 'selesai', 'dibatalkan') DEFAULT 'belum_mulai',
    tanggal_mulai DATE NULL,
    tanggal_selesai DATE NULL,
    catatan TEXT NULL,
    dokumen_pendukung JSON NULL, -- Store document references if needed
    created_by VARCHAR(255) NULL, -- User who updated this stage
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (timeline_stage_id) REFERENCES timeline_stages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_siswa_stage (siswa_id, timeline_stage_id)
);

-- Insert Default Timeline Stages
INSERT INTO timeline_stages (nama_stage, deskripsi, urutan, icon, kategori) VALUES
('Pre-seleksi', 'Verifikasi dokumen awal dan kelengkapan berkas', 1, 'ClipboardCheck', 'preparation'),
('Pendidikan Pra-Diklat', 'Persiapan dasar sebelum memasuki program diklat', 2, 'BookOpen', 'training'),
('Pendidikan Diklat', 'Program pelatihan teknis dan bahasa Jepang', 3, 'GraduationCap', 'training'),
('Seleksi Administrasi', 'Verifikasi kelengkapan dokumen untuk seleksi', 4, 'FileCheck', 'selection'),
('Wawancara', 'Tes wawancara dengan pihak perusahaan Jepang', 5, 'MessageSquare', 'selection'),
('Pemberkasan', 'Pengurusan dokumen resmi untuk keberangkatan', 6, 'FileText', 'documentation'),
('Pendidikan Pasca-Diklat', 'Pelatihan lanjutan dan orientasi budaya Jepang', 7, 'Award', 'training'),
('Surat Rekomendasi Disnaker', 'Penerbitan surat rekomendasi dari Dinas Tenaga Kerja', 8, 'Award', 'documentation'),
('Pemberangkatan ke Jepang', 'Keberangkatan menuju tempat kerja di Jepang', 9, 'Plane', 'deployment');

-- Create indexes for performance
CREATE INDEX idx_siswa_timeline_siswa_id ON siswa_timeline_progress(siswa_id);
CREATE INDEX idx_siswa_timeline_status ON siswa_timeline_progress(status);
CREATE INDEX idx_timeline_stages_urutan ON timeline_stages(urutan);
CREATE INDEX idx_timeline_stages_kategori ON timeline_stages(kategori);

-- Create view for easy timeline progress retrieval
CREATE VIEW v_siswa_timeline_progress AS
SELECT 
    stp.id,
    stp.siswa_id,
    s.nama_lengkap,
    ts.nama_stage,
    ts.deskripsi,
    ts.urutan,
    ts.icon,
    ts.kategori,
    stp.status,
    stp.tanggal_mulai,
    stp.tanggal_selesai,
    stp.catatan,
    stp.created_at,
    stp.updated_at
FROM siswa_timeline_progress stp
JOIN timeline_stages ts ON stp.timeline_stage_id = ts.id
JOIN siswa s ON stp.siswa_id = s.id
ORDER BY ts.urutan;

-- Function to initialize timeline for new student
DELIMITER //
CREATE FUNCTION init_student_timeline(student_id VARCHAR(255))
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE stage_id INT;
    DECLARE stage_cursor CURSOR FOR 
        SELECT id FROM timeline_stages WHERE is_aktif = true ORDER BY urutan;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN stage_cursor;
    read_loop: LOOP
        FETCH stage_cursor INTO stage_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        INSERT IGNORE INTO siswa_timeline_progress (siswa_id, timeline_stage_id, status)
        VALUES (student_id, stage_id, 'belum_mulai');
    END LOOP;
    CLOSE stage_cursor;
    
    RETURN TRUE;
END //
DELIMITER ;

-- Trigger to auto-initialize timeline when student is created
-- Note: This would need to be adapted for Supabase/PostgreSQL syntax 