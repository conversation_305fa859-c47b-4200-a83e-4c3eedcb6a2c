-- SAFE FULL Seed Data Dashboard Magang <PERSON> - Yutaka LPK
-- Generated: 2025-07-11T07:26:59.062Z
-- Total Records: LPK(3), <PERSON><PERSON><PERSON>(3), <PERSON><PERSON><PERSON><PERSON>(4), <PERSON> Order(3), <PERSON><PERSON><PERSON>(170), <PERSON><PERSON><PERSON><PERSON>(110)
-- 
-- SAFE VERSION: Uses UPSERT to prevent duplicate key errors
-- Choose one of the options below:

-- =====================================================
-- OPTION 1: CLEAN INSTALL (Recommended for fresh start)
-- Uncomment the lines below to clear all existing data
-- =====================================================

-- TRUNCATE TABLE penempatan_siswa CASCADE;
-- TRUNCATE TABLE siswa CASCADE;
-- TRUNCATE TABLE perusahaan_penerima CASCADE;
-- TRUNCATE TABLE job_order CASCADE;
-- TRUNCATE TABLE lpk_mitra CASCADE;
-- TRUNCATE TABLE kumiai CASCADE;

-- =====================================================
-- OPTION 2: SAFE UPSERT (Recommended for existing data)
-- Uses ON CONFLICT to handle duplicates gracefully
-- =====================================================

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Insert LPK Mitra (3 records) - Safe UPSERT
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('7d73dafd-57a5-4e15-a67d-f090ee5db537', 'Yutaka', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak Yutaka Tanaka', 'Ibu Sari Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', 'LPK Yutaka - Partner utama untuk program magang Jepang')
ON CONFLICT (id) DO UPDATE SET
    nama_lpk = EXCLUDED.nama_lpk,
    alamat_lengkap = EXCLUDED.alamat_lengkap,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('c853738c-3cdb-4da9-821c-ca3e649c680e', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development')
ON CONFLICT (id) DO UPDATE SET
    nama_lpk = EXCLUDED.nama_lpk,
    alamat_lengkap = EXCLUDED.alamat_lengkap,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'LPK Central Java', 'Jl. Pemuda No. 789, Semarang 50132', 'Semarang', 'Jawa Tengah', 'Bapak Direktur Central', 'Ibu Manager Central', '024-1111-2222', '<EMAIL>', 'https://lpkcentral.co.id', 'aktif', '2024-01-01', 'LPK Central Java untuk wilayah Jawa Tengah')
ON CONFLICT (id) DO UPDATE SET
    nama_lpk = EXCLUDED.nama_lpk,
    alamat_lengkap = EXCLUDED.alamat_lengkap,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Kumiai (3 records) - Safe UPSERT
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang')
ON CONFLICT (kode_kumiai) DO UPDATE SET
    nama_kumiai = EXCLUDED.nama_kumiai,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik')
ON CONFLICT (kode_kumiai) DO UPDATE SET
    nama_kumiai = EXCLUDED.nama_kumiai,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('8fe768d9-3081-4392-b861-cafa681426ca', 'Osaka Workers Kumiai', 'OWK', '3-3-3 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Suzuki San', '+81-6-3456-7890', '<EMAIL>', 'https://owk.jp', 'aktif', 'Kumiai untuk wilayah Osaka')
ON CONFLICT (kode_kumiai) DO UPDATE SET
    nama_kumiai = EXCLUDED.nama_kumiai,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Perusahaan (4 records) - Safe UPSERT
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Tokyo Manufacturing Co., Ltd.', '4-4-4 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-4567-8901', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo')
ON CONFLICT (id) DO UPDATE SET
    nama_perusahaan = EXCLUDED.nama_perusahaan,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'Osaka Technical Industries', '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', 'Osaka', 'Osaka', 'Technical Services', 'Watanabe San', '+81-6-5678-9012', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka')
ON CONFLICT (id) DO UPDATE SET
    nama_perusahaan = EXCLUDED.nama_perusahaan,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', 'Nagoya Automotive Parts', '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', 'Nagoya', 'Aichi', 'Automotive', 'Takahashi San', '+81-52-6789-0123', '<EMAIL>', 'https://nagoya-auto.jp', 'aktif', 'Perusahaan otomotif di Nagoya')
ON CONFLICT (id) DO UPDATE SET
    nama_perusahaan = EXCLUDED.nama_perusahaan,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Kyoto Electronics Corp.', '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', 'Kyoto', 'Kyoto', 'Electronics', 'Nakamura San', '+81-75-7890-1234', '<EMAIL>', 'https://kyoto-elec.jp', 'aktif', 'Perusahaan elektronik di Kyoto')
ON CONFLICT (id) DO UPDATE SET
    nama_perusahaan = EXCLUDED.nama_perusahaan,
    alamat_jepang = EXCLUDED.alamat_jepang,
    updated_at = CURRENT_TIMESTAMP;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Safe seed data installation completed!';
    RAISE NOTICE 'Records processed:';
    RAISE NOTICE '- LPK Mitra: 3 records';
    RAISE NOTICE '- Kumiai: 3 records';  
    RAISE NOTICE '- Perusahaan: 4 records';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  Note: This is a partial installation.';
    RAISE NOTICE 'For complete data (Job Orders, Siswa, Penempatan),';
    RAISE NOTICE 'use the full seed-data-full.sql after clearing existing data.';
END $$;
