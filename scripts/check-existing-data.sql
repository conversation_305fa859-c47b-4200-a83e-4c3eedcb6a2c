-- Check Existing Data in Dashboard Magang Jepang Database
-- Run this script to see what data already exists before inserting seed data

-- =====================================================
-- DATABASE OVERVIEW
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔍 CHECKING EXISTING DATA IN DATABASE';
    RAISE NOTICE '=====================================';
END $$;

-- Check LPK Mitra
SELECT 
    'LPK_MITRA' as table_name,
    COUNT(*) as total_records,
    STRING_AGG(nama_lpk, ', ') as existing_names
FROM lpk_mitra;

-- Check <PERSON><PERSON>i
SELECT 
    'KUMIAI' as table_name,
    COUNT(*) as total_records,
    STRING_AGG(kode_kumiai || ' (' || nama_kumiai || ')', ', ') as existing_codes
FROM kumiai;

-- Check Perusahaan
SELECT 
    'PERUSAHAAN' as table_name,
    COUNT(*) as total_records,
    STRING_AGG(nama_perusahaan, ', ') as existing_names
FROM perusahaan_penerima;

-- Check Job Order
SELECT 
    'JOB_ORDER' as table_name,
    COUNT(*) as total_records,
    STRING_AGG(judul_pekerjaan, ', ') as existing_jobs
FROM job_order;

-- Check Siswa
SELECT 
    'SISWA' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status_pendaftaran = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status_pendaftaran = 'review' THEN 1 END) as review_count
FROM siswa;

-- Check Penempatan
SELECT 
    'PENEMPATAN' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status_penempatan = 'aktif' THEN 1 END) as aktif_count,
    COUNT(CASE WHEN status_penempatan = 'berangkat' THEN 1 END) as berangkat_count
FROM penempatan_siswa;

-- =====================================================
-- DETAILED BREAKDOWN
-- =====================================================

-- Show existing Kumiai codes (potential conflicts)
SELECT 
    'EXISTING KUMIAI CODES' as info,
    kode_kumiai,
    nama_kumiai,
    status
FROM kumiai 
ORDER BY kode_kumiai;

-- Show existing LPK names
SELECT 
    'EXISTING LPK NAMES' as info,
    nama_lpk,
    kota,
    status
FROM lpk_mitra 
ORDER BY nama_lpk;

-- =====================================================
-- CONFLICT DETECTION
-- =====================================================

-- Check for potential conflicts with seed data
DO $$
DECLARE
    gokei_exists BOOLEAN;
    tic_exists BOOLEAN;
    owk_exists BOOLEAN;
    yutaka_exists BOOLEAN;
BEGIN
    -- Check Kumiai conflicts
    SELECT EXISTS(SELECT 1 FROM kumiai WHERE kode_kumiai = 'GOKEI') INTO gokei_exists;
    SELECT EXISTS(SELECT 1 FROM kumiai WHERE kode_kumiai = 'TIC') INTO tic_exists;
    SELECT EXISTS(SELECT 1 FROM kumiai WHERE kode_kumiai = 'OWK') INTO owk_exists;
    
    -- Check LPK conflicts
    SELECT EXISTS(SELECT 1 FROM lpk_mitra WHERE nama_lpk = 'Yutaka') INTO yutaka_exists;
    
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  POTENTIAL CONFLICTS DETECTED:';
    RAISE NOTICE '================================';
    
    IF gokei_exists THEN
        RAISE NOTICE '❌ Kumiai GOKEI already exists';
    ELSE
        RAISE NOTICE '✅ Kumiai GOKEI - OK to insert';
    END IF;
    
    IF tic_exists THEN
        RAISE NOTICE '❌ Kumiai TIC already exists';
    ELSE
        RAISE NOTICE '✅ Kumiai TIC - OK to insert';
    END IF;
    
    IF owk_exists THEN
        RAISE NOTICE '❌ Kumiai OWK already exists';
    ELSE
        RAISE NOTICE '✅ Kumiai OWK - OK to insert';
    END IF;
    
    IF yutaka_exists THEN
        RAISE NOTICE '❌ LPK Yutaka already exists';
    ELSE
        RAISE NOTICE '✅ LPK Yutaka - OK to insert';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '📋 RECOMMENDATIONS:';
    RAISE NOTICE '==================';
    
    IF gokei_exists OR tic_exists OR owk_exists OR yutaka_exists THEN
        RAISE NOTICE '1. Use seed-data-full-safe.sql for UPSERT approach';
        RAISE NOTICE '2. Or clear existing data first with TRUNCATE commands';
        RAISE NOTICE '3. Or manually resolve conflicts before inserting';
    ELSE
        RAISE NOTICE '✅ No conflicts detected - safe to use any seed file';
    END IF;
END $$;

-- =====================================================
-- SUMMARY STATISTICS
-- =====================================================

SELECT 
    'SUMMARY' as section,
    (SELECT COUNT(*) FROM lpk_mitra) as lpk_count,
    (SELECT COUNT(*) FROM kumiai) as kumiai_count,
    (SELECT COUNT(*) FROM perusahaan_penerima) as perusahaan_count,
    (SELECT COUNT(*) FROM job_order) as job_order_count,
    (SELECT COUNT(*) FROM siswa) as siswa_count,
    (SELECT COUNT(*) FROM penempatan_siswa) as penempatan_count;
