-- Seed Data Dashboard Magang <PERSON> - Yutaka LPK
-- Generated: 2025-07-11T07:27:09.920Z
-- Records: <PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON><PERSON>(2), <PERSON>(1), <PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON><PERSON>(16)

-- Disable foreign key checks
SET session_replication_role = replica;

-- Insert LPK Mitra
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'Yutaka', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak Yutaka Tanaka', 'Ibu Sa<PERSON> Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', 'LPK Yutaka - Partner utama untuk program magang <PERSON>pan<PERSON>');
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('b618a842-f095-4757-be1b-90809df69fc1', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development');

-- Insert Kumiai
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang');
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik');

-- Insert Perusahaan
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', 'Tokyo Manufacturing Co., Ltd.', '3-3-3 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-3456-7890', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', 'Osaka Technical Industries', '4-4-4 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Technical Services', 'Suzuki San', '+81-6-4567-8901', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka');

-- Insert Job Order
INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) VALUES ('5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', 'Factory Worker - Manufacturing', 'Pekerjaan di bidang manufaktur dengan sistem shift. Membutuhkan ketelitian dan kemampuan bekerja dalam tim.', 'Production Operator', 'Manufacturing', 'L/P', 20, 35, 'SMA', 'Tidak diperlukan pengalaman, akan ada training', 'Kemampuan bekerja dalam tim, teliti, disiplin', 180000, 20000, 8, 5, true, 'Disediakan dormitory dengan fasilitas lengkap', 'Shuttle bus dari dormitory ke pabrik', 'Asuransi kesehatan dan kecelakaan kerja', 'Kantin, gym, wifi gratis', 10, 0, 'published', '2024-01-01', '2024-12-31');

-- Insert Siswa
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3db79d16-84f7-4150-b34c-9415b8d0ff7c', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'GULAM FATHUR RISQI', '3301092611020001', 'CILACAP', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GULAM', 'Ibu GULAM', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 1');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('818bb395-78df-4b3b-8931-18d2cc78f20b', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ADITYA HERI PURNOMO', '3301240603030001', 'CILACAP', '2003-03-06', 'L', 'Islam', 'belum_menikah', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITYA', 'Ibu ADITYA', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 2');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1b7e3860-492d-449c-adc8-7e2fa5a35b47', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'BRIYAN BINTORO', '3403010607990003', 'GUNUNG KIDUL', '1999-07-06', 'L', 'Islam', 'belum_menikah', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Gunung Kidul', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BRIYAN', 'Ibu BRIYAN', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 3');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ae1a27f5-3d5e-4a5c-9d6a-c09169bf6121', 'b618a842-f095-4757-be1b-90809df69fc1', 'RIO DWI SATRIOTOMO', '3322031212030002', 'KAB. SEMARANG', '1905-06-25', 'L', 'Islam', 'belum_menikah', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIO', 'Ibu RIO', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 4');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('012b89ea-ef56-46db-a93d-945e6cc42ee6', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'DIMAS WAHYU LUWANGGA PRASETYA', '3322081912000001', 'KAB. SEMARANG', '2000-12-19', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 5');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cb5a86a4-a4d7-4de9-8a8e-fd08f55abb2c', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'MIRZA ALI HILMI', '3328111510020004', 'Tegal', '2002-10-15', 'L', 'Islam', 'belum_menikah', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Tegal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MIRZA', 'Ibu MIRZA', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 6');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1c6e95cc-d023-426c-ad38-12571aa9429d', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'LANGGA PRATAMA', '3301090701050001', 'Cilacap', '2005-01-07', 'L', 'Islam', 'belum_menikah', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LANGGA', 'Ibu LANGGA', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 7');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('956c5e0e-ddb3-43d2-9537-0a3f337383b8', 'b618a842-f095-4757-be1b-90809df69fc1', 'ADI NUR MUHAMMAD SAPUTRA', '3324110111010003', 'KENDAL', '2001-11-01', 'L', 'Islam', 'belum_menikah', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADI', 'Ibu ADI', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 8');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('09e87a6c-3d34-4217-a5ee-043238a49aa9', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'DEDE NURYADIN', '3302032212960007', 'BANYUMAS', '1996-12-22', 'L', 'Islam', 'belum_menikah', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEDE', 'Ibu DEDE', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 9');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('353027ff-ce14-4c2a-8e06-bdf5aeba83aa', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'FIRNANDA ABDI RICOLA', '3321012002040008', 'DEMAK', '2004-02-20', 'L', 'Islam', 'belum_menikah', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FIRNANDA', 'Ibu FIRNANDA', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 10');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('85d9b705-0e0e-4bfa-a4f9-35d989149b07', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ANDREANSYAH', '3302032005030003', 'BANYUMAS', '2003-05-20', 'L', 'Islam', 'belum_menikah', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANDREANSYAH', 'Ibu ANDREANSYAH', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 11');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6a4cf6d9-462d-4dc1-a1d9-30c4e38e93e7', 'b618a842-f095-4757-be1b-90809df69fc1', 'DANIEL FADILA PUTRA', '3301032404050001', 'CILACAP', '2005-04-24', 'L', 'Islam', 'belum_menikah', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANIEL', 'Ibu DANIEL', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 12');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e47b34e8-e124-492f-9c68-abb5a8be85af', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ADITIYA RIFQI ANWAR', '3301022701040004', 'CILACAP', '2004-01-27', 'L', 'Islam', 'belum_menikah', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITIYA', 'Ibu ADITIYA', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 13');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c0872131-6b92-4ab0-a6a7-b7cc2871b81e', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'KHOIRUNNISA', '3324185909970001', 'KENDAL', '1997-09-19', 'P', 'Islam', 'belum_menikah', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUNNISA', 'Ibu KHOIRUNNISA', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 14');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('694f5813-9658-4151-9814-f362042789b1', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'AGUS HERMAWAN', '3307042709010005', 'WONOSOBO', '2001-09-27', 'L', 'Islam', 'belum_menikah', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Wonosobo', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGUS', 'Ibu AGUS', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 15');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('545530fe-ceb8-4d38-9769-d7a053650c3b', 'b618a842-f095-4757-be1b-90809df69fc1', 'ANGGA SANTOSO', '1807211003020004', 'LABUHAN RATU LIMA', '2002-03-10', 'L', 'Islam', 'belum_menikah', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANGGA', 'Ibu ANGGA', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 16');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c8e07cce-4e3c-4b06-875f-075119d7138a', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'FATHUR ROHMAN', '3301091102050003', 'CILACAP', '2005-02-11', 'L', 'Islam', 'belum_menikah', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHUR', 'Ibu FATHUR', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 17');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7ce49785-1dec-4eaf-8a33-12cd82e886fe', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ITMAMUL WAFA', '3301100411970001', 'CILACAP', '1997-11-04', 'L', 'Islam', 'belum_menikah', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ITMAMUL', 'Ibu ITMAMUL', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 18');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7e9a6e62-dd12-4ea8-8b84-5aebc1456c47', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'DWI WAHYU SETIAWAN', '3301012510990001', 'CILACAP', '1999-10-25', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 19');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a86f3cda-f794-42ce-b4cd-07a8f1022ecd', 'b618a842-f095-4757-be1b-90809df69fc1', 'DEWA ARI SAPUTRA GOEVARA', '3322181411050002', 'KAB. SEMARANG', '2005-11-14', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 20');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('399a0b3a-5b5c-4277-831c-fce71587a8c8', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'VERDY ARYA IRAWAN', '3301090807050002', 'CILACAP', '2005-07-08', 'L', 'Islam', 'belum_menikah', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah VERDY', 'Ibu VERDY', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 21');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('722a94b1-0bc8-4767-990e-b7c18f95bf42', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'STEWARD OMEGA BENYAMIN', '3374080608940001', 'SEMARANG', '1994-08-06', 'L', 'Islam', 'belum_menikah', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah STEWARD', 'Ibu STEWARD', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 22');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f1214414-05a1-457b-9021-7e73dd312043', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'MUHAMMAD IRFAN EFENDI', '3315131511000001', 'GRBOGAN', '2000-11-15', 'L', 'Islam', 'belum_menikah', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 23');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7c8ec733-420b-40b2-b8f7-d7679b220588', 'b618a842-f095-4757-be1b-90809df69fc1', 'FERRY NUR SAPUTRA', '3315182305040003', 'GROBOGAN', '2004-05-23', 'L', 'Islam', 'belum_menikah', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERRY', 'Ibu FERRY', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 24');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('900eee41-36e3-4e22-ac83-e872707e179b', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'WAHID MUZANI', '3301012603030004', 'CILACAP', '2003-03-26', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHID', 'Ibu WAHID', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 25');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d1e01c78-9bc0-4267-8701-d590caf97e30', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'SYAIFUL ANJAR', '1810072601020003', 'WARINGINSARI TIMUR', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SYAIFUL', 'Ibu SYAIFUL', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 26');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e9daa01a-994d-4b08-adb3-d83eb1c6fe17', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'FEBRI SETIAWAN', '3301011202990001', 'CILACAP', '1999-02-12', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBRI', 'Ibu FEBRI', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 27');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f629cd18-db19-4b29-a313-a16f5611cb40', 'b618a842-f095-4757-be1b-90809df69fc1', 'PERI AKBAR WIBOWO', '3329090808970002', 'BREBES', '1997-08-08', 'L', 'Islam', 'belum_menikah', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Brebes', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PERI', 'Ibu PERI', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 28');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2a2e026c-0e2b-4da3-988d-dab249f1bf49', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'KURNIAWAN FEBRIANSYAH', '3204141602030002', 'BANDUNG', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Bandung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KURNIAWAN', 'Ibu KURNIAWAN', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 29');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d5774ecb-2d9d-41bf-b953-9905f6c6fd7b', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'RINA HESTIANA', '3308105703040001', 'MAGELANG', '2002-09-15', 'P', 'Islam', 'belum_menikah', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Magelang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RINA', 'Ibu RINA', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 30');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7dd5cafe-7f80-4378-b125-4bdb8c734844', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'KOLIFAH LISTYANINGRUM', '3374024509980002', 'SEMARANG', '1998-09-05', 'P', 'Islam', 'belum_menikah', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KOLIFAH', 'Ibu KOLIFAH', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 31');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2958558a-b8ed-40c3-b935-d7d6df0c12ae', 'b618a842-f095-4757-be1b-90809df69fc1', 'RIZKI NOVIANA', '3324084211980002', 'KENDAL', '1998-11-02', 'P', 'Islam', 'belum_menikah', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIZKI', 'Ibu RIZKI', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 32');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7f43ca30-a56c-4fec-9af1-dc82e2e263fa', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'SUTAN MHD AMIN JAMAL', '1302102310980002', 'SALAYO', '1998-10-23', 'L', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUTAN', 'Ibu SUTAN', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 33');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f8b366b2-42af-4237-a75b-2c3d12f87437', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'PRAMESSHEILA GITA ANISSA', '3374135804980004', 'KUDUS', '1998-04-18', 'P', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRAMESSHEILA', 'Ibu PRAMESSHEILA', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 34');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0e93b009-cf36-4573-887a-77cb080c4c79', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'AMAN BAROKAH', '3215010106050003', 'KARAWANG', '2005-06-01', 'L', 'Islam', 'belum_menikah', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AMAN', 'Ibu AMAN', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 35');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5dfbe27c-7b34-461c-8f4f-1932debbb16b', 'b618a842-f095-4757-be1b-90809df69fc1', 'ALIF AL FIAN HIDAYAT', '3215292212050006', 'CIAMIS', '2005-12-22', 'L', 'Islam', 'belum_menikah', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 36');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a03a1854-0a48-4c19-aecf-991dcd6d087a', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'REYVAL HIDAYAT RHAMADAN', '3215052109050008', 'KARAWANG', '2005-09-21', 'L', 'Islam', 'belum_menikah', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REYVAL', 'Ibu REYVAL', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 37');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eecb803a-81f2-4010-b043-43524f551c20', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'FEBY SUSANTO', '3273121802990003', 'CILACAP', '1999-02-18', 'L', 'Islam', 'belum_menikah', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBY', 'Ibu FEBY', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 38');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('208c2473-479e-45fd-b0f3-30a715523897', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'SATRIA ADJI NUGROHO', '3301211303020001', 'CILACAP', '2002-03-13', 'L', 'Islam', 'belum_menikah', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIA', 'Ibu SATRIA', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 39');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('947c206b-809c-482a-99c5-66f4eea6f4e1', 'b618a842-f095-4757-be1b-90809df69fc1', 'MUHAMAD NIMA ROFIQ ARDIANSAH', '3322072103020001', 'KAB. SEMARANG', '2002-03-21', 'L', 'Islam', 'belum_menikah', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 40');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('46b37b5b-26bb-4af7-8783-8ab624ac5287', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'NURSALIM', '3301191003970004', 'CILACAP', '1997-03-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURSALIM', 'Ibu NURSALIM', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 41');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('abec3a1b-d460-4717-b6e5-0995015ddd43', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ALDHILA KHOIRU AKILA', '3322013007050001', 'KAB. SEMARANG', '2005-07-30', 'L', 'Islam', 'belum_menikah', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDHILA', 'Ibu ALDHILA', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 42');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bf24b8d4-b2a5-4ec6-9fdf-c9a6f912bee5', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'SAIFUL ISMAN', '3322080202060005', 'KAB. SEMARANG', '2006-02-02', 'L', 'Islam', 'belum_menikah', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAIFUL', 'Ibu SAIFUL', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 43');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a1f829b3-b33a-4d99-b6fa-0f201e2f76aa', 'b618a842-f095-4757-be1b-90809df69fc1', 'DENIS ADITYA FAHRI', '3322110407040001', 'KAB. SEMARANG', '2004-07-04', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENIS', 'Ibu DENIS', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 44');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('549034aa-66a4-4fc9-8842-7b043863d298', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'FREDY HARITS WIJANARKO', '3309070310059005', 'BOYOLALI', '2005-10-03', 'L', 'Islam', 'belum_menikah', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Boyolali', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FREDY', 'Ibu FREDY', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 45');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f9ff6ed2-c06c-4c47-99af-3e59a37b6505', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'HASAN SIDIK', '3301122003980003', 'CILACAP', '1998-03-20', 'L', 'Islam', 'belum_menikah', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HASAN', 'Ibu HASAN', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 46');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e071eb29-bf28-4ffd-9226-40555b7a2da8', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'MUHAMMAD ATOUR ROHMAN', '3324092512040002', 'KENDAL', '2004-12-25', 'L', 'Islam', 'belum_menikah', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 47');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c8075591-c553-430a-b43d-71adbe81f3e4', 'b618a842-f095-4757-be1b-90809df69fc1', 'RONALD MAULANA SAPUTRA', '3215052303060002', 'KARAWANG', '2006-03-23', 'L', 'Islam', 'belum_menikah', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RONALD', 'Ibu RONALD', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 48');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('584ef262-13a2-49de-a6a6-c2cb00b065b4', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'ALWI AWALUL BANI', '3215052912050005', 'TASIKMALAYA', '2005-12-29', 'L', 'Islam', 'belum_menikah', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALWI', 'Ibu ALWI', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 49');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fed81b6c-7b00-4d47-8c66-ed3f7f797731', 'ade8b9b9-b37b-465e-8379-55fd0af71fb7', 'GIN FANDIKA LESMANA', '3215050102050010', 'KARAWANG', '2005-02-01', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GIN', 'Ibu GIN', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - baris 50');

-- Insert Penempatan
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('48b8204d-f23d-45db-b87f-c2ffebbcb90f', '1b7e3860-492d-449c-adc8-7e2fa5a35b47', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6d74a806-2e07-4a45-bbb1-791870361a58', 'cb5a86a4-a4d7-4de9-8a8e-fd08f55abb2c', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('99b6ff26-88d7-4f75-91d6-0a64598e8dc5', '09e87a6c-3d34-4217-a5ee-043238a49aa9', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cd32b54d-3295-4936-adf7-fabe14cbbc8a', '6a4cf6d9-462d-4dc1-a1d9-30c4e38e93e7', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cf3b1bc8-d891-4634-a352-6f25fe9ebc5f', '694f5813-9658-4151-9814-f362042789b1', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3a9e75a6-e6f2-4915-a9dc-6574b3950e32', '7ce49785-1dec-4eaf-8a33-12cd82e886fe', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('021a2b52-d457-4b9c-b036-5b0cc0a8e170', '399a0b3a-5b5c-4277-831c-fce71587a8c8', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fe44e338-328e-46b5-8c9e-d559af5d9749', '7c8ec733-420b-40b2-b8f7-d7679b220588', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('738808ee-9cd5-451c-be16-35c4f73514e7', 'e9daa01a-994d-4b08-adb3-d83eb1c6fe17', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cb03dcf8-f03a-44b3-84d1-df38d9bc1e41', 'd5774ecb-2d9d-41bf-b953-9905f6c6fd7b', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6fe1793e-12cb-405e-a632-c1d2a294ae89', '7f43ca30-a56c-4fec-9af1-dc82e2e263fa', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c1f2cfe5-433a-47ba-ab5e-c2cf5260b85f', '5dfbe27c-7b34-461c-8f4f-1932debbb16b', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('28b2144b-b839-45e4-b61b-1b9012afd961', '208c2473-479e-45fd-b0f3-30a715523897', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a74ebd25-128e-48b9-8a01-e31bae2e59e9', 'abec3a1b-d460-4717-b6e5-0995015ddd43', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d07dc63d-b212-41a3-86a3-e21689edc15d', '549034aa-66a4-4fc9-8842-7b043863d298', '5898367f-1e50-4e51-8543-ec9ce0c45625', '51768a37-b830-4d8e-84eb-04269aa32887', '89bf8a34-bee1-49c1-8fea-65d4ffab2fa1', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Namba, Chuo-ku, Osaka 542-0076', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4a5ca9e8-7e33-48d0-ab16-7535d98bd615', 'c8075591-c553-430a-b43d-71adbe81f3e4', '5898367f-1e50-4e51-8543-ec9ce0c45625', 'aa5b41d0-a573-4e80-a802-66c86e083145', '9aac2f7a-6a6b-4eea-9b2a-7fbc6885dce2', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '3-3-3 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- ✅ Seed data import completed!
