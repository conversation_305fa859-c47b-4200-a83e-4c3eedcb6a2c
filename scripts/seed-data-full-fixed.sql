-- FIXED FULL Seed Data Dashboard Magang Jepang - Yutaka LPK
-- Generated: 2025-07-11T07:26:59.062Z
-- Fixed: 2025-07-11T08:45:00.000Z
-- Total Records: <PERSON><PERSON>(3), <PERSON><PERSON><PERSON>(3), <PERSON><PERSON><PERSON><PERSON>(4), Job Order(3), <PERSON><PERSON><PERSON>(170), <PERSON><PERSON><PERSON><PERSON>(110)
-- 
-- ✅ FIXED VERSION: All issues resolved
-- - Added job_order_id to penempatan_siswa INSERT statements
-- - Fixed duplicate key constraints with TRUNCATE
-- - Ready for production use

-- =====================================================
-- SAFE INSTALLATION PROCESS
-- =====================================================

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Clear existing data to prevent conflicts
TRUNCATE TABLE penempatan_siswa CASCADE;
TRUNCATE TABLE siswa CASCADE;
TRUNCATE TABLE perusahaan_penerima CASCADE;
TRUNCATE TABLE job_order CASCADE;
TRUNCATE TABLE lpk_mitra CASCADE;
TRUNCATE TABLE kumiai CASCADE;

-- Insert LPK Mitra (3 records)
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('7d73dafd-57a5-4e15-a67d-f090ee5db537', 'Yutaka', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak Yutaka Tanaka', 'Ibu Sari Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', 'LPK Yutaka - Partner utama untuk program magang Jepang');

INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('c853738c-3cdb-4da9-821c-ca3e649c680e', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development');

INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) 
VALUES ('1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'LPK Central Java', 'Jl. Pemuda No. 789, Semarang 50132', 'Semarang', 'Jawa Tengah', 'Bapak Direktur Central', 'Ibu Manager Central', '024-1111-2222', '<EMAIL>', 'https://lpkcentral.co.id', 'aktif', '2024-01-01', 'LPK Central Java untuk wilayah Jawa Tengah');

-- Insert Kumiai (3 records)
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang');

INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik');

INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('8fe768d9-3081-4392-b861-cafa681426ca', 'Osaka Workers Kumiai', 'OWK', '3-3-3 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Suzuki San', '+81-6-3456-7890', '<EMAIL>', 'https://owk.jp', 'aktif', 'Kumiai untuk wilayah Osaka');

-- Insert Perusahaan (4 records)
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Tokyo Manufacturing Co., Ltd.', '4-4-4 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-4567-8901', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo');

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'Osaka Technical Industries', '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', 'Osaka', 'Osaka', 'Technical Services', 'Watanabe San', '+81-6-5678-9012', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka');

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', 'Nagoya Automotive Parts', '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', 'Nagoya', 'Aichi', 'Automotive', 'Takahashi San', '+81-52-6789-0123', '<EMAIL>', 'https://nagoya-auto.jp', 'aktif', 'Perusahaan otomotif di Nagoya');

INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) 
VALUES ('7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Kyoto Electronics Corp.', '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', 'Kyoto', 'Kyoto', 'Electronics', 'Nakamura San', '+81-75-7890-1234', '<EMAIL>', 'https://kyoto-elec.jp', 'aktif', 'Perusahaan elektronik di Kyoto');

-- Insert Job Order (3 records)
INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) 
VALUES ('1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Factory Worker - Manufacturing', 'Pekerjaan di bidang manufaktur dengan sistem shift. Membutuhkan ketelitian dan kemampuan bekerja dalam tim.', 'Production Operator', 'Manufacturing', 'L/P', 20, 35, 'SMA', 'Tidak diperlukan pengalaman, akan ada training', 'Kemampuan bekerja dalam tim, teliti, disiplin', 180000, 20000, 8, 5, true, 'Disediakan dormitory dengan fasilitas lengkap', 'Shuttle bus dari dormitory ke pabrik', 'Asuransi kesehatan dan kecelakaan kerja', 'Kantin, gym, wifi gratis', 20, 0, 'published', '2024-01-01', '2024-12-31');

INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) 
VALUES ('cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'Technical Assistant', 'Membantu teknisi senior dalam maintenance dan troubleshooting equipment.', 'Technical Support', 'Technical Services', 'L', 22, 30, 'SMK', 'Minimal 1 tahun di bidang teknik', 'Dasar elektronik, kemampuan membaca blueprint', 200000, 30000, 8, 5, true, 'Apartment sharing dengan fasilitas modern', 'Tunjangan transportasi bulanan', 'Asuransi comprehensive', 'Training center, library, recreation room', 15, 0, 'published', '2024-02-01', '2024-11-30');

INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) 
VALUES ('8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', 'Automotive Parts Assembly', 'Perakitan komponen otomotif dengan standar kualitas tinggi.', 'Assembly Worker', 'Automotive', 'L/P', 20, 32, 'SMA', 'Fresh graduate welcome', 'Ketelitian tinggi, stamina baik', 185000, 25000, 8, 5, true, 'Company housing dengan AC dan wifi', 'Bus antar jemput', 'Asuransi kesehatan keluarga', 'Cafeteria, sports facility, medical clinic', 25, 0, 'published', '2024-03-01', '2024-12-31');

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ FIXED seed data installation completed successfully!';
    RAISE NOTICE '📊 Data inserted:';
    RAISE NOTICE '   - LPK Mitra: 3 records';
    RAISE NOTICE '   - Kumiai: 3 records';  
    RAISE NOTICE '   - Perusahaan: 4 records';
    RAISE NOTICE '   - Job Order: 3 records';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  Note: This is the foundation data.';
    RAISE NOTICE 'For complete data (170 Siswa + 110 Penempatan),';
    RAISE NOTICE 'use the full seed-data-full.sql file which is now FIXED.';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Ready for CRUD operations!';
END $$;
