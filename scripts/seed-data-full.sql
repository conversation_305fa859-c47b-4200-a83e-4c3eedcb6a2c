-- FULL Seed Data Dashboard Magang Jepang - Yutaka LPK
-- Generated: 2025-07-11T07:26:59.062Z
-- Total Records: <PERSON><PERSON>(3), <PERSON><PERSON><PERSON>(3), <PERSON><PERSON><PERSON><PERSON>(4), Job Order(3), <PERSON><PERSON><PERSON>(170), <PERSON><PERSON><PERSON><PERSON>(110)

-- Disable foreign key checks
SET session_replication_role = replica;

-- Clear existing data to prevent duplicate key errors
TRUNCATE TABLE penempatan_siswa CASCADE;
TRUNCATE TABLE siswa CASCADE;
TRUNCATE TABLE perusahaan_penerima CASCADE;
TRUNCATE TABLE job_order CASCADE;
TRUNCATE TABLE lpk_mitra CASCADE;
TRUNCATE TABLE kumiai CASCADE;

-- Insert LPK Mitra (3 records)
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('7d73dafd-57a5-4e15-a67d-f090ee5db537', 'Yutaka', 'Jl. Raya Utama No. 123, Jakarta Pusat 10110', 'Jakarta', 'DKI Jakarta', 'Bapak Yutaka Tanaka', 'Ibu Sari Wijaya', '021-1234-5678', '<EMAIL>', 'https://yutaka.co.id', 'aktif', '2024-01-01', 'LPK Yutaka - Partner utama untuk program magang Jepang')
ON CONFLICT (id) DO NOTHING;
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('c853738c-3cdb-4da9-821c-ca3e649c680e', 'LPK Dummy', 'Jl. Pendidikan No. 456, Bandung 40123', 'Bandung', 'Jawa Barat', 'Bapak Pimpinan LPK', 'Ibu Kontak Person', '022-9876-5432', '<EMAIL>', 'https://lpkdummy.co.id', 'aktif', '2024-01-01', 'LPK Dummy untuk testing dan development')
ON CONFLICT (id) DO NOTHING;
INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, email, website, status, tanggal_kerjasama, catatan) VALUES ('1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'LPK Central Java', 'Jl. Pemuda No. 789, Semarang 50132', 'Semarang', 'Jawa Tengah', 'Bapak Direktur Central', 'Ibu Manager Central', '024-1111-2222', '<EMAIL>', 'https://lpkcentral.co.id', 'aktif', '2024-01-01', 'LPK Central Java untuk wilayah Jawa Tengah')
ON CONFLICT (id) DO NOTHING;

-- Insert Kumiai (3 records)
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Gokei Cloud Kyodo Kumiai', 'GOKEI', '1-1-1 Shibuya, Shibuya-ku, Tokyo 150-0002', 'Tokyo', 'Tokyo', 'Tanaka San', '+81-3-1234-5678', '<EMAIL>', 'https://gokei.jp', 'aktif', 'Kumiai utama untuk program magang')
ON CONFLICT (kode_kumiai) DO NOTHING;
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'TIC Kyodo Kumiai', 'TIC', '2-2-2 Shinjuku, Shinjuku-ku, Tokyo 160-0022', 'Tokyo', 'Tokyo', 'Yamada San', '+81-3-2345-6789', '<EMAIL>', 'https://tic.jp', 'aktif', 'Kumiai untuk bidang teknik')
ON CONFLICT (kode_kumiai) DO NOTHING;
INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('8fe768d9-3081-4392-b861-cafa681426ca', 'Osaka Workers Kumiai', 'OWK', '3-3-3 Namba, Chuo-ku, Osaka 542-0076', 'Osaka', 'Osaka', 'Suzuki San', '+81-6-3456-7890', '<EMAIL>', 'https://owk.jp', 'aktif', 'Kumiai untuk wilayah Osaka')
ON CONFLICT (kode_kumiai) DO NOTHING;

-- Insert Perusahaan (4 records)
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Tokyo Manufacturing Co., Ltd.', '4-4-4 Minato, Minato-ku, Tokyo 105-0003', 'Tokyo', 'Tokyo', 'Manufacturing', 'Sato San', '+81-3-4567-8901', '<EMAIL>', 'https://tokyo-mfg.jp', 'aktif', 'Perusahaan manufaktur terkemuka di Tokyo');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'Osaka Technical Industries', '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', 'Osaka', 'Osaka', 'Technical Services', 'Watanabe San', '+81-6-5678-9012', '<EMAIL>', 'https://osaka-tech.jp', 'aktif', 'Perusahaan teknik di Osaka');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', 'Nagoya Automotive Parts', '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', 'Nagoya', 'Aichi', 'Automotive', 'Takahashi San', '+81-52-6789-0123', '<EMAIL>', 'https://nagoya-auto.jp', 'aktif', 'Perusahaan otomotif di Nagoya');
INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, email, website, status, keterangan) VALUES ('7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Kyoto Electronics Corp.', '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', 'Kyoto', 'Kyoto', 'Electronics', 'Nakamura San', '+81-75-7890-1234', '<EMAIL>', 'https://kyoto-elec.jp', 'aktif', 'Perusahaan elektronik di Kyoto');

-- Insert Job Order (3 records)
INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) VALUES ('1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', 'Factory Worker - Manufacturing', 'Pekerjaan di bidang manufaktur dengan sistem shift. Membutuhkan ketelitian dan kemampuan bekerja dalam tim.', 'Production Operator', 'Manufacturing', 'L/P', 20, 35, 'SMA', 'Tidak diperlukan pengalaman, akan ada training', 'Kemampuan bekerja dalam tim, teliti, disiplin', 180000, 20000, 8, 5, true, 'Disediakan dormitory dengan fasilitas lengkap', 'Shuttle bus dari dormitory ke pabrik', 'Asuransi kesehatan dan kecelakaan kerja', 'Kantin, gym, wifi gratis', 20, 0, 'published', '2024-01-01', '2024-12-31');
INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) VALUES ('cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', 'Technical Assistant', 'Membantu teknisi senior dalam maintenance dan troubleshooting equipment.', 'Technical Support', 'Technical Services', 'L', 22, 30, 'SMK', 'Minimal 1 tahun di bidang teknik', 'Dasar elektronik, kemampuan membaca blueprint', 200000, 30000, 8, 5, true, 'Apartment sharing dengan fasilitas modern', 'Tunjangan transportasi bulanan', 'Asuransi comprehensive', 'Training center, library, recreation room', 15, 0, 'published', '2024-02-01', '2024-11-30');
INSERT INTO job_order (id, perusahaan_id, kumiai_id, judul_pekerjaan, deskripsi_pekerjaan, posisi, bidang_kerja, jenis_kelamin, usia_min, usia_max, pendidikan_min, pengalaman_kerja, keahlian_khusus, gaji_pokok, tunjangan, jam_kerja_per_hari, hari_kerja_per_minggu, overtime_available, akomodasi, transportasi, asuransi, fasilitas_lain, jumlah_kuota, kuota_terisi, status, tanggal_buka, tanggal_tutup) VALUES ('8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', 'Automotive Parts Assembly', 'Perakitan komponen otomotif dengan standar kualitas tinggi.', 'Assembly Worker', 'Automotive', 'L/P', 20, 32, 'SMA', 'Fresh graduate welcome', 'Ketelitian tinggi, stamina baik', 185000, 25000, 8, 5, true, 'Company housing dengan AC dan wifi', 'Bus antar jemput', 'Asuransi kesehatan keluarga', 'Cafeteria, sports facility, medical clinic', 25, 0, 'published', '2024-03-01', '2024-12-31');

-- Insert Siswa (170 records)
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2396ee88-3928-401a-ae73-be6e1a838b63', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'GULAM FATHUR RISQI', '3301092611020001', 'CILACAP', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GULAM', 'Ibu GULAM', 'DUSUN KAWUNGANTEN RT. 004 RW. 001 KEL. KAWUNGANTEN LOR KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a03565e4-c71f-41e1-ad17-a58df6414c53', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ADITYA HERI PURNOMO', '3301240603030001', 'CILACAP', '2003-03-06', 'L', 'Islam', 'belum_menikah', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITYA', 'Ibu ADITYA', 'DUSUN BUGEL RT. 001 RW. 010 KEL. PANIKEL KEC. KAMPUNG LAUT KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b5882487-4131-41ea-8982-168737ca4dec', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'BRIYAN BINTORO', '3403010607990003', 'GUNUNG KIDUL', '1999-07-06', 'L', 'Islam', 'belum_menikah', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Gunung Kidul', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BRIYAN', 'Ibu BRIYAN', 'PAKELREJO RT. 001 RW. 008 KEL. PIYAMAN KEC. WONOSARI KAB. GUNUNG KIDUL  DI YOGYAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7e4b5e60-208e-4523-9d2b-9d738ad4d997', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'RIO DWI SATRIOTOMO', '3322031212030002', 'KAB. SEMARANG', '1905-06-25', 'L', 'Islam', 'belum_menikah', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIO', 'Ibu RIO', 'LINGK. KRAJAN RT. 003 RW. 001 KEL. NGAMPIN KEC. AMBARAWA KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6281a97a-9693-42ca-a7cd-f9f2fb940473', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'DIMAS WAHYU LUWANGGA PRASETYA', '3322081912000001', 'KAB. SEMARANG', '2000-12-19', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'KRAJAN RT. 002 RW. 001 KEL. BEDONO KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('738b6fde-1f0c-4ca2-90d4-fb812d6a3945', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MIRZA ALI HILMI', '3328111510020004', 'Tegal', '2002-10-15', 'L', 'Islam', 'belum_menikah', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Tegal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MIRZA', 'Ibu MIRZA', 'Kalimati RT. 015 RW. 003 Kel. Kalimati Kec. Adewerna Kab. Tegal Jawa Tengah', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f697d16e-4f0b-4673-a2c9-1bf0d500d3a5', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'LANGGA PRATAMA', '3301090701050001', 'Cilacap', '2005-01-07', 'L', 'Islam', 'belum_menikah', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LANGGA', 'Ibu LANGGA', 'DUSUN SOKAWERA KULON RT. 002 RW. 007 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('88cf331e-f3d9-4130-a5be-d55aa8eb1735', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'ADI NUR MUHAMMAD SAPUTRA', '3324110111010003', 'KENDAL', '2001-11-01', 'L', 'Islam', 'belum_menikah', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADI', 'Ibu ADI', 'JOHO KRAJAN RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d7c27df9-96ff-47e4-b6c1-eb28552eef58', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DEDE NURYADIN', '3302032212960007', 'BANYUMAS', '1996-12-22', 'L', 'Islam', 'belum_menikah', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEDE', 'Ibu DEDE', 'DESA TUNJUNG RT. 005 RW. 003 KEL. TUNJUNG KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8a37ad58-3f17-4b30-8123-069739d87857', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FIRNANDA ABDI RICOLA', '3321012002040008', 'DEMAK', '2004-02-20', 'L', 'Islam', 'belum_menikah', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FIRNANDA', 'Ibu FIRNANDA', 'KEMBANGAN RT. 008 RW. 005 KEL. KEMBANGARUM KEC. MRANGGEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('36d13fc5-7e54-421c-afc7-f50d64d47e42', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ANDREANSYAH', '3302032005030003', 'BANYUMAS', '2003-05-20', 'L', 'Islam', 'belum_menikah', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Banyumas', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANDREANSYAH', 'Ibu ANDREANSYAH', 'DESA KARANGLEWAS RT. 001 RW. 003 KEL. KARANGLEWAS KEC. JATILAWANG KAB. BANYUMAS JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0829dd78-93a9-46ef-93c9-0daaa5312e8d', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DANIEL FADILA PUTRA', '3301032404050001', 'CILACAP', '2005-04-24', 'L', 'Islam', 'belum_menikah', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANIEL', 'Ibu DANIEL', 'JL. LAUT RT. 005 RW. 002 KEL. KARANGANYAR KEC. ADIPALA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eed45e76-ac92-4158-98a0-8210ed14f642', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ADITIYA RIFQI ANWAR', '3301022701040004', 'CILACAP', '2004-01-27', 'L', 'Islam', 'belum_menikah', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADITIYA', 'Ibu ADITIYA', 'JL. ARMADA NO. 15 RT. 006 RW. 002 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f0a779e2-bdb1-4b99-b800-6d8a1bdce2fd', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'KHOIRUNNISA', '3324185909970001', 'KENDAL', '1997-09-19', 'P', 'Islam', 'belum_menikah', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUNNISA', 'Ibu KHOIRUNNISA', 'TLOGOREJO RT. 001 RW. 006 KEL. TLOGOREJO KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('7f0b7ac8-213f-4121-a78b-0643a0476fb8', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'AGUS HERMAWAN', '3307042709010005', 'WONOSOBO', '2001-09-27', 'L', 'Islam', 'belum_menikah', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Wonosobo', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGUS', 'Ibu AGUS', 'SILINTANG RT. 005 RW. 002 KEL. PUCUNGKEREP KEC. KALIWIRO KAB. WONOSOBO JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d8cb3b12-69fe-46a3-8c4a-fa1f7a3f97f6', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'ANGGA SANTOSO', '1807211003020004', 'LABUHAN RATU LIMA', '2002-03-10', 'L', 'Islam', 'belum_menikah', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANGGA', 'Ibu ANGGA', 'BERINGIN RT. 004 RW. 002 KEL. LABUHAN RATU V KEC. LABUHAN RATU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9554612a-5a8f-4489-81c2-438353bd60d4', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FATHUR ROHMAN', '3301091102050003', 'CILACAP', '2005-02-11', 'L', 'Islam', 'belum_menikah', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHUR', 'Ibu FATHUR', 'DUSUN KUBANGKANGKUNG KIDUL RT. 005 RW. 006 KEL. KUBANGKANGKUNG KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8d548be4-873a-451c-8ee3-57fc7f5e953e', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ITMAMUL WAFA', '3301100411970001', 'CILACAP', '1997-11-04', 'L', 'Islam', 'belum_menikah', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ITMAMUL', 'Ibu ITMAMUL', 'LAYANSARI RT. 007 RW. 002 KEL. LAYANSARI KEC. GANDRUNGMANGU KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bf0e62da-6bd0-4395-80c6-7dd94483e483', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'DWI WAHYU SETIAWAN', '3301012510990001', 'CILACAP', '1999-10-25', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'DUSUN PONDOKWUNGU RT. 003 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8de10c45-16c9-498b-a581-05490e0e48b4', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DEWA ARI SAPUTRA GOEVARA', '3322181411050002', 'KAB. SEMARANG', '2005-11-14', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'LANGENSARI BARAT RT. 002 RW. 004 KEL. LANGENSARI KEC. UNGARAN BARAT KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ebafc1b7-4f55-4429-b906-4cd75bae3c1d', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'VERDY ARYA IRAWAN', '3301090807050002', 'CILACAP', '2005-07-08', 'L', 'Islam', 'belum_menikah', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah VERDY', 'Ibu VERDY', 'DUSUN TEGALANYAR RT. 001 RW. 003 KEL. KALIJERUK KEC. KAWUNGANTEN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f861daed-78ba-4150-bb9b-abcca9d84079', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'STEWARD OMEGA BENYAMIN', '3374080608940001', 'SEMARANG', '1994-08-06', 'L', 'Islam', 'belum_menikah', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah STEWARD', 'Ibu STEWARD', 'JIMBARAN RT. 005 RW. 008 KEL. GONDORIYO KEC. BERGAS KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('08741280-c333-47d7-be83-1495343c5e04', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUHAMMAD IRFAN EFENDI', '3315131511000001', 'GRBOGAN', '2000-11-15', 'L', 'Islam', 'belum_menikah', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'NOLOKERTO RT. 008 RW. 005 KEL. NOLOKERTO KEC. KALIWUNGU KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a400f4d6-1429-41fe-9064-cc8980581e53', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'FERRY NUR SAPUTRA', '3315182305040003', 'GROBOGAN', '2004-05-23', 'L', 'Islam', 'belum_menikah', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERRY', 'Ibu FERRY', 'DUSUN JATI RT. 002 RW. 001 KEL. SUKOREJO KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a863c1cf-fbb4-4deb-85cc-b5b20762b977', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'WAHID MUZANI', '3301012603030004', 'CILACAP', '2003-03-26', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHID', 'Ibu WAHID', 'DUSUN PONDOKWUNGU RT. 001 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e7efa27-7b3b-4214-a68d-c194430bf34d', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'SYAIFUL ANJAR', '1810072601020003', 'WARINGINSARI TIMUR', '2002-11-26', 'L', 'Islam', 'belum_menikah', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SYAIFUL', 'Ibu SYAIFUL', 'WARINGIN SARI TIMUR RT. 022 RW. 007 KEL. WARINGIN SARI TIMUR KEC. ADILUWIH KAB. PRINGSEWU LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2fcf750c-7582-4c2d-82c8-00d6d0e27899', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'FEBRI SETIAWAN', '3301011202990001', 'CILACAP', '1999-02-12', 'L', 'Islam', 'belum_menikah', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBRI', 'Ibu FEBRI', 'DUSUN PONDOKWUNGU RT. 002 RW. 006 KEL. KALIWUNGU KEC. KEDUNGREJA KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a5701a9e-c8cd-4d99-81cf-68d3a8d88585', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'PERI AKBAR WIBOWO', '3329090808970002', 'BREBES', '1997-08-08', 'L', 'Islam', 'belum_menikah', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Brebes', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PERI', 'Ibu PERI', 'PASARBATANG RT. 004 RW. 011 KEL. PASAR BATANG KEC. BREBES KAB. BREBES JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0a0c2ba6-e20b-46f4-9cb6-758c907bce94', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'KURNIAWAN FEBRIANSYAH', '3204141602030002', 'BANDUNG', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Bandung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KURNIAWAN', 'Ibu KURNIAWAN', 'KP SAWAH LUHUR RT. 001 RW. 010 KEL. SUKASARI KEC. PAMEUNGPEUKKAB. BANDUNG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('11ed7995-9a5a-42e7-8646-77e1cd992b96', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RINA HESTIANA', '3308105703040001', 'MAGELANG', '2002-09-15', 'P', 'Islam', 'belum_menikah', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Magelang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RINA', 'Ibu RINA', 'BANYUURIP RT. 002 RW. 005 KEL. BANYUURIP KEC. TEGALREJO KAB. MAGELANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6e6de6a9-b3ef-43b8-bd90-36350df14daa', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'KOLIFAH LISTYANINGRUM', '3374024509980002', 'SEMARANG', '1998-09-05', 'P', 'Islam', 'belum_menikah', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KOLIFAH', 'Ibu KOLIFAH', 'JL. TENGIRI VII RT. 006 RW. 006 KEL. BANDARHARJO KEC. SEMARANG UTARA KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('54ab142d-0b23-4622-89dc-e000207efc70', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'RIZKI NOVIANA', '3324084211980002', 'KENDAL', '1998-11-02', 'P', 'Islam', 'belum_menikah', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIZKI', 'Ibu RIZKI', 'KEDUNGSUREN RT. 002 RW. 003 KEL. KEDUNGSUREN KEC. KALIWUNGU SELATAN KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f723cb59-251e-4681-b1fc-9fa92bd91e3f', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'SUTAN MHD AMIN JAMAL', '1302102310980002', 'SALAYO', '1998-10-23', 'L', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUTAN', 'Ibu SUTAN', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2d776fa9-226d-4f1d-9122-3e01d9acbc6a', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'PRAMESSHEILA GITA ANISSA', '3374135804980004', 'KUDUS', '1998-04-18', 'P', 'Islam', 'belum_menikah', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRAMESSHEILA', 'Ibu PRAMESSHEILA', 'JL. CANDI PENATARAN TIMUR RT. 001 RW. 001 KEL. KALIPANCUR KEC. NGALIYAN KOTA SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('444bcbce-af1e-413a-b161-c2243fb37aa4', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AMAN BAROKAH', '3215010106050003', 'KARAWANG', '2005-06-01', 'L', 'Islam', 'belum_menikah', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AMAN', 'Ibu AMAN', 'JL RANGGAGEDE RT. 006 RW. 012 KEL. TANJUNGMEKAR KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('542a3b95-f037-44d9-828e-22f513a4ab4d', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ALIF AL FIAN HIDAYAT', '3215292212050006', 'CIAMIS', '2005-12-22', 'L', 'Islam', 'belum_menikah', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', 'GRIYA MAS KARAWANG. G 2/04 RT. 006 RW. 007 KEL. CENGKONG KEC. PURWASARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d2b2d05f-8c82-4c3a-a467-0e7214243637', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'REYVAL HIDAYAT RHAMADAN', '3215052109050008', 'KARAWANG', '2005-09-21', 'L', 'Islam', 'belum_menikah', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REYVAL', 'Ibu REYVAL', 'PERUM NUANSA TRADISI RESIDENCE BLOK A8/15 RT. 042 RW. 013 KEL. KONDANGJAYA KEC. KARAWANG TIMUR kab. Karawang jawa barat', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ec468482-0117-4a81-b8ac-4d4495d5eeaa', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FEBY SUSANTO', '3273121802990003', 'CILACAP', '1999-02-18', 'L', 'Islam', 'belum_menikah', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FEBY', 'Ibu FEBY', 'JL. PRAMUKA TIMUR RT. 006 RW. 002 KEL. MAOSKIDUL KEC. MAOS KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('45ef39b4-b30c-40de-b908-cab21d61f022', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'SATRIA ADJI NUGROHO', '3301211303020001', 'CILACAP', '2002-03-13', 'L', 'Islam', 'belum_menikah', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIA', 'Ibu SATRIA', 'JL. TANJUNG GG. TANJUNG I RT. 004 RW. 013 KEL. SIDAKAYA KEC. CILACAP SELATAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d5c21c92-5110-4bcb-921e-2f33139900bd', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'MUHAMAD NIMA ROFIQ ARDIANSAH', '3322072103020001', 'KAB. SEMARANG', '2002-03-21', 'L', 'Islam', 'belum_menikah', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'DSN KRAJAN I RT. 002 RW. 002 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('abe5f8c5-d2e9-4f1f-9629-0ab668fe16d7', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'NURSALIM', '3301191003970004', 'CILACAP', '1997-03-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURSALIM', 'Ibu NURSALIM', 'DUSUN KALENAREN RT. 001 RW. 006 KEL. BULUPAYUNG KEC. PATIMUAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f62adb39-7b21-4869-825f-0167d9c66c81', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ALDHILA KHOIRU AKILA', '3322013007050001', 'KAB. SEMARANG', '2005-07-30', 'L', 'Islam', 'belum_menikah', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDHILA', 'Ibu ALDHILA', 'KEBONPETE RT. 002 RW. 002 KEL. POLOBOGO KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('472dfb88-d454-4620-ad66-314741a3fa21', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'SAIFUL ISMAN', '3322080202060005', 'KAB. SEMARANG', '2006-02-02', 'L', 'Islam', 'belum_menikah', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAIFUL', 'Ibu SAIFUL', 'KALISARI RT. 011 RW. 002 KEL. KUWARASAN KEC. JAMBU KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d7a58a4d-8263-4e28-b1cb-ce8e29fb72f8', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DENIS ADITYA FAHRI', '3322110407040001', 'KAB. SEMARANG', '2004-07-04', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENIS', 'Ibu DENIS', 'LINGKUNGAN MERAKREJO RT. 002 RW. 008 KEL. HARJOSARI KEC. BAWEN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0b757450-e9ca-4498-ac4d-a4f14dfbaa07', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'FREDY HARITS WIJANARKO', '3309070310059005', 'BOYOLALI', '2005-10-03', 'L', 'Islam', 'belum_menikah', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Boyolali', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FREDY', 'Ibu FREDY', 'LEBAK RT. 006 RW. 001 KEL. NEPEN KEC. TERAS KAB. BOYOLALI JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b03727b5-1c34-4c2e-8134-4f856cfa75d1', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'HASAN SIDIK', '3301122003980003', 'CILACAP', '1998-03-20', 'L', 'Islam', 'belum_menikah', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HASAN', 'Ibu HASAN', 'DUSUN PURBAYASA RT. 002 RW. 002 KEL. SINDANGBARANG KEC. KARANGPUCUNG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('acc0cca2-76b2-43be-9eb8-b895d67d521f', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUHAMMAD ATOUR ROHMAN', '3324092512040002', 'KENDAL', '2004-12-25', 'L', 'Islam', 'belum_menikah', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'PURWOKERTO RT. 001 RW. 002 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a9d1acc1-90fe-440e-9b03-f838790511f9', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RONALD MAULANA SAPUTRA', '3215052303060002', 'KARAWANG', '2006-03-23', 'L', 'Islam', 'belum_menikah', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RONALD', 'Ibu RONALD', 'DUSUN KOSAMBI II RT. 025 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('258266c8-ca7d-4242-b663-281f6f3f2e00', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ALWI AWALUL BANI', '3215052912050005', 'TASIKMALAYA', '2005-12-29', 'L', 'Islam', 'belum_menikah', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALWI', 'Ibu ALWI', 'KOSAMBI II RT. 026 RW. 007 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('05815537-6756-4668-8b9f-ba9a4f87674b', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'GIN FANDIKA LESMANA', '3215050102050010', 'KARAWANG', '2005-02-01', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GIN', 'Ibu GIN', 'DUSUN KRAJAN RT. 001 RW. 001 KEL. CIBALONGSARI KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('06c991f3-930e-4a8f-928e-ab5a1126ea16', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DENDRA JULPIKAR', '3215062001060002', 'KARAWANG', '2006-01-20', 'L', 'Islam', 'belum_menikah', 'CIKANGKUNG BARAT II RT. 010 RW. 002 KEL. RENGASDENGKLOK UTARA KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENDRA', 'Ibu DENDRA', 'CIKANGKUNG BARAT II RT. 010 RW. 002 KEL. RENGASDENGKLOK UTARA KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0b2a0c80-82a4-4041-ba1a-2d44bcd72ce2', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'AHMAD', '3215271806050001', 'KARAWANG', '2005-06-18', 'L', 'Islam', 'belum_menikah', 'LEUWEUNG KAUNG RT. 008 RW. 004 KEL. MEKARMULYA KEC. TELUKJAMBE BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'LEUWEUNG KAUNG RT. 008 RW. 004 KEL. MEKARMULYA KEC. TELUKJAMBE BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8b042c8e-43f3-4fab-9282-e8737e3b814f', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'OKSAY GILANG RAMADHAN', '3215053010030002', 'KARAWANG', '2004-10-30', 'L', 'Islam', 'belum_menikah', 'PERUM PURI KOSAMBI BLOK G NO 29 RT. 056 RW. 016 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah OKSAY', 'Ibu OKSAY', 'PERUM PURI KOSAMBI BLOK G NO 29 RT. 056 RW. 016 KEL. DUREN KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('65e7f186-ca38-4e7b-b832-fc7e24b4fb67', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DEWA SAPUTRA', '3322101903040002', 'KAB. SEMARANG', '2004-03-19', 'L', 'Islam', 'belum_menikah', 'PANDEAN RT. 005 RW. 001 KEL. LODOYONG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEWA', 'Ibu DEWA', 'PANDEAN RT. 005 RW. 001 KEL. LODOYONG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5ec5e9f9-8c9b-431a-86a1-8e41d547b440', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUHAMMAD SUALMAN', '3324092904020002', 'KENDAL', '2002-04-29', 'L', 'Islam', 'belum_menikah', 'KRAYAPAN RT. 003 RW. 4 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KRAYAPAN RT. 003 RW. 4 KEL. PURWOKERTO KEC. BRANGSONG Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3adf5bb8-8ac6-4102-92e0-552062fbec49', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'FAHRI CAHYADI', '3215061802060001', 'KARAWANG', '2006-02-18', 'L', 'Islam', 'belum_menikah', 'DUSUN PACING UTARA RT. 007 RW. 003 KEL. DEWISARI KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAHRI', 'Ibu FAHRI', 'DUSUN PACING UTARA RT. 007 RW. 003 KEL. DEWISARI KEC. RENGASDENGKLOK KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('28089be5-74ee-47f2-8271-22caf947a935', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'GALERIUS MIKAEL PORAWOUW', '3275030806050004', 'BEKASI', '2005-06-08', 'L', 'Islam', 'belum_menikah', 'PRIMA HARAPAN REGENCY L12/21 RT. 001 RW. 012 KEL. HARAPANBARU KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Bekasi', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GALERIUS', 'Ibu GALERIUS', 'PRIMA HARAPAN REGENCY L12/21 RT. 001 RW. 012 KEL. HARAPANBARU KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e5191a79-2b82-42ba-bf80-b0a7c205c31c', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'NURMANSYAH', '3215011811050003', 'KARAWANG', '2005-11-18', 'L', 'Islam', 'belum_menikah', 'PASIR JENGKOL RT. 007 RW. 013 KEL. TANJUNGPURA KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NURMANSYAH', 'Ibu NURMANSYAH', 'PASIR JENGKOL RT. 007 RW. 013 KEL. TANJUNGPURA KEC. KARAWANG BARAT KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('87d57d77-a78a-43b0-92d5-44e50ddd6840', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MICO RAMADHAN', '3172041711011005', 'JAKARTA', '2001-11-17', 'L', 'Islam', 'belum_menikah', 'JL. KAYU TINGGI KP.KANDANG SAPI RT. 002 RW. 006 KEL. CAKUNG TIMUR KEC. CAKUNG JAKARTA TIMUR DKI JAKARTA', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MICO', 'Ibu MICO', 'JL. KAYU TINGGI KP.KANDANG SAPI RT. 002 RW. 006 KEL. CAKUNG TIMUR KEC. CAKUNG JAKARTA TIMUR DKI JAKARTA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('93fe90cd-7581-4783-9fbe-b260e89472ee', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MUHAMMAD FATKHUR ROZI', '3275032903050006', 'BEKASI', '2005-03-29', 'L', 'Islam', 'belum_menikah', 'KAV.KABEL MAS RT. 002 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Bekasi', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KAV.KABEL MAS RT. 002 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA KOTA BEKASI JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('49e20b12-461b-43dd-8e68-037bc70c2475', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'TUBAGUS MUHAMMAD FADIL HASBI', '3215051007050002', 'KARAWANG', '2005-07-10', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN I RT. 004 RW. 001 KEL. GINTUNGKERTA KEC. KLARI KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah TUBAGUS', 'Ibu TUBAGUS', 'DUSUN KRAJAN I RT. 004 RW. 001 KEL. GINTUNGKERTA KEC. KLARI KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('407a2a35-988b-443f-a78d-9884eb8f6606', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'EVAN MAULANA WIDAYAT', '3374143101050001', 'KENDAL', '2005-01-31', 'L', 'Islam', 'belum_menikah', 'NGADIPIRO RT. 002 RW. 010 KEL. KERTOSARI KEC. SINGOROJO KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah EVAN', 'Ibu EVAN', 'NGADIPIRO RT. 002 RW. 010 KEL. KERTOSARI KEC. SINGOROJO KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('935cc330-59de-47b4-afae-e0b199ebc51a', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ILHAM ALFATTA HAFID ITTAQULLAHA', '3322010309050001', 'KAB.SEMARANG', '2005-09-03', 'L', 'Islam', 'belum_menikah', 'GEDAD RT. 009 RW. 001 KEL. WATES KEC. GETASAN KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. / Kota Semarang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ILHAM', 'Ibu ILHAM', 'GEDAD RT. 009 RW. 001 KEL. WATES KEC. GETASAN KAB. SEMARANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('dcb33bc4-aaa3-4d70-9c2c-7aedd7eda3bb', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'MOHAMMAD IRFAN CHARISUDDIN', '3324192705020003', 'KENDAL', '2002-05-27', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 001 RW. 001 KEL. NGAMPEL KULON KEC. NGAMPEL KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMMAD', 'Ibu MOHAMMAD', 'KRAJAN RT. 001 RW. 001 KEL. NGAMPEL KULON KEC. NGAMPEL KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a7815e66-ddba-4926-8622-770b1de05d52', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MAHBUB KHASAN FAUZI', '3323070409020002', 'TEMANGGUNG', '2002-08-27', 'L', 'Islam', 'belum_menikah', 'KAUMAN RT. 006 RW. 002 KEL. GONDANGWAYANG KEC. KEDU KAB. TEMANGGUNG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Temanggung', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MAHBUB', 'Ibu MAHBUB', 'KAUMAN RT. 006 RW. 002 KEL. GONDANGWAYANG KEC. KEDU KAB. TEMANGGUNG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d6882e4d-d850-4241-911a-7b7c7748a802', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MUHAMAD RIJAL ARDI ROHMAN', '3324112409030001', 'KENDAL', '2003-09-24', 'L', 'Islam', 'belum_menikah', 'JOHOREJO RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'JOHOREJO RT. 002 RW. 001 KEL. JOHOREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('38236937-7a0c-4353-b041-f0b54c49bd1c', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AHMAD MUSTAFIQI', '3324110105990002', 'KENDAL', '1999-05-01', 'L', 'Islam', 'belum_menikah', 'GG PANCASILA NAMPUROTO RT. 002 RW. 003 KEL. PUCANGREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'GG PANCASILA NAMPUROTO RT. 002 RW. 003 KEL. PUCANGREJO KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cdd55212-97c5-4aa4-9245-0acafc046ce8', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DIMAS ANDRIYANTO', '3315180506000001', 'GROBOGAN', '2000-09-22', 'L', 'Islam', 'belum_menikah', 'DUSUN MLANGI RT. 003 RW. 003 KEL. TAJEMSARI KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DIMAS', 'Ibu DIMAS', 'DUSUN MLANGI RT. 003 RW. 003 KEL. TAJEMSARI KEC. TEGOWANU KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('15f9e9fc-46c6-44ab-81ed-94ddfd81e3d6', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ERVINA NUR ALIFIAH', '3215266401040003', 'BOYOLALI', '2004-01-24', 'P', 'Islam', 'belum_menikah', 'SARI INDAH RT. 004 RW. 019 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ERVINA', 'Ibu ERVINA', 'SARI INDAH RT. 004 RW. 019 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2f8cddb3-3769-493f-a71a-c343d987bd14', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'THIO ANSYAHRI', '3214042606940004', 'PURWAKARTA', '1994-06-26', 'L', 'Islam', 'belum_menikah', 'KP ANJUN RT. 003 RW. 001 KEL. ANJUN KEC. PLERED KAB. PURWAKARTA JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Purwakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah THIO', 'Ibu THIO', 'KP ANJUN RT. 003 RW. 001 KEL. ANJUN KEC. PLERED KAB. PURWAKARTA JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('451134c1-1e12-48a0-80df-665636acc6a8', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'SISKA WIDYANENGSIH', '3215236909010003', 'KARAWANG', '2001-09-29', 'P', 'Islam', 'belum_menikah', 'PULOPUTRI RT. 007 RW. 003 KEL. SUKAMULYA KEC. CILAMAYA KULON KAB. KARAWANG JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Karawang', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SISKA', 'Ibu SISKA', 'PULOPUTRI RT. 007 RW. 003 KEL. SUKAMULYA KEC. CILAMAYA KULON KAB. KARAWANG JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('49d88006-79a5-4213-8e40-47f4404299cc', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'YUMRORTUN NAFISATUR ROHMANIYAH', '3315037112010002', 'GROBOGAN', '2001-12-31', 'P', 'Islam', 'belum_menikah', 'DUSUN KETOPO RT. 003 RW. 004 KEL. KARANGWADER KEC. PENAWANGAN KAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Grobogan', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YUMRORTUN', 'Ibu YUMRORTUN', 'DUSUN KETOPO RT. 003 RW. 004 KEL. KARANGWADER KEC. PENAWANGAN KAB. GROBOGAN JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('965f11c9-db93-4ae7-ad31-63633df74006', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'NISVIATIN NASIKHAH', '3301027110010003', 'CILACAP', '2001-10-31', 'P', 'Islam', 'belum_menikah', 'DONDONG RT. 002 RW. 004 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Cilacap', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NISVIATIN', 'Ibu NISVIATIN', 'DONDONG RT. 002 RW. 004 KEL. DONDONG KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e40c3464-6c60-4074-9bd3-00f6cbe04764', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'DWI KARTINI SARASWATI', '332102610499000', 'DEMAK', '1999-04-21', 'P', 'Islam', 'belum_menikah', 'BILO KAUMAN RT. 002 RW. 008 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', 'BILO KAUMAN RT. 002 RW. 008 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e7f2c761-c221-4fe0-bf0f-669c3224fc21', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ANINDA SHENDIATI MIRABIA', '3321025304020002', 'DEMAK', '2002-04-13', 'P', 'Islam', 'belum_menikah', 'SAMBI RT. 001 RW. 001 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Demak', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANINDA', 'Ibu ANINDA', 'SAMBI RT. 001 RW. 001 KEL. PUNDENARUM KEC. KARANGAWEN KAB. DEMAK JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ac22baf5-b84a-4bc0-98f3-9b91b4223904', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DEPRI SAPUTRA', '1807162912040001', 'RAJABASA BARU', '2004-12-29', 'L', 'Islam', 'belum_menikah', 'DUSUN VI RT. 030 RW. 012 KEL. RAJABASA BARU KEC. MATARAM BARU KAB. LAMPUNG TIMUR LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Lampung Timur', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DEPRI', 'Ibu DEPRI', 'DUSUN VI RT. 030 RW. 012 KEL. RAJABASA BARU KEC. MATARAM BARU KAB. LAMPUNG TIMUR LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('da0be7f3-d22d-4478-a443-7de487079a7c', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MISBAKHUL HUDA', '3521133112990001', 'NGAWI', '1999-12-31', 'L', 'Islam', 'belum_menikah', 'BELAKANG LAPAS RT. 026 RW. 001 KEL. FATUKBOT KEC. ATAMBUA SELATAN KAB. BELU NUSA TENGGARA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Belu', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MISBAKHUL', 'Ibu MISBAKHUL', 'BELAKANG LAPAS RT. 026 RW. 001 KEL. FATUKBOT KEC. ATAMBUA SELATAN KAB. BELU NUSA TENGGARA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f96b74c6-9988-4e1d-a611-2d6a5365818d', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MUHAMAD PADLI HAMDANI', '3324111701030001', 'KUNINGAN', '2003-01-17', 'L', 'Islam', 'belum_menikah', 'GEMUHBLANTEN RT. 001 RW. 002 KEL. GEMUHBLANTEN KEC. GEMUH KAB. KENDAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kendal', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'GEMUHBLANTEN RT. 001 RW. 002 KEL. GEMUHBLANTEN KEC. GEMUH KAB. KENDAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2e7922ca-db94-4969-85ad-555bade4fed6', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'BUDI ASTUTI', '3305246307040001', 'KEBUMEN', '2006-02-01', 'P', 'Islam', 'belum_menikah', 'KRAJAN II RT. 002 RW. 001 KEL. PEJENGKOLAN KEC. PADURESO Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BUDI', 'Ibu BUDI', 'KRAJAN II RT. 002 RW. 001 KEL. PEJENGKOLAN KEC. PADURESO Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6acc2aae-d3ef-4cbd-be37-dc5205c0f5d4', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DAVANSA EGA WIDYA PRATIWI', '3323085802060004', 'TEMANGGUNG', '2006-02-18', 'P', 'Islam', 'belum_menikah', 'JETIS LOR RT. 002 RW. 003 KEL. PARAKAN KAUMAN KEC. PARAKAN Kab. Temanggung JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DAVANSA', 'Ibu DAVANSA', 'JETIS LOR RT. 002 RW. 003 KEL. PARAKAN KAUMAN KEC. PARAKAN Kab. Temanggung JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2dec2664-b2c8-4e71-9af8-3d9700162dba', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'HERLINA UCKYA NINGSIH', '3301244112020001', 'CILACAP', '2002-12-01', 'P', 'Islam', 'belum_menikah', 'DUSUN KARANG JAYA RT. 003 RW. 002 KEL. UJUNGGAGAK KEC. KAMPUNG LAUT Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah HERLINA', 'Ibu HERLINA', 'DUSUN KARANG JAYA RT. 003 RW. 002 KEL. UJUNGGAGAK KEC. KAMPUNG LAUT Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bf9882ba-75fa-4b99-b065-d3e67505a582', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'LULU DWI OKTARIANI', '3301235110030001', 'CILACAP', '2003-10-11', 'P', 'Islam', 'belum_menikah', 'JL. MAHONI NO. 71 RT. 002 RW. 002 KEL. TRITIH KULON KEC. CILACAP UTARA Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LULU', 'Ibu LULU', 'JL. MAHONI NO. 71 RT. 002 RW. 002 KEL. TRITIH KULON KEC. CILACAP UTARA Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b407786f-dc68-4d79-87ee-6a46fc4db7c5', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'DANAR MAULANA KUSUMA', '3215051602060004', 'KARAWANG', '2006-02-16', 'L', 'Islam', 'belum_menikah', 'JLN.NAGASARI DALAM RT. 003 RW. 002 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DANAR', 'Ibu DANAR', 'JLN.NAGASARI DALAM RT. 003 RW. 002 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9b98ad9a-09d5-49df-b761-ab4f3152a295', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DYO FIRMAN RAHMADIANSYAH', '3275111106050003', 'BEKASI', '2005-06-11', 'L', 'Islam', 'belum_menikah', 'PERUM BMI 2 BLOK B3 NO 21 RT. 002 RW. 011 KEL. DAWUAN BARAT KEC. CIKAMPEK Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DYO', 'Ibu DYO', 'PERUM BMI 2 BLOK B3 NO 21 RT. 002 RW. 011 KEL. DAWUAN BARAT KEC. CIKAMPEK Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('96831ffd-587d-47e7-a089-b85ad0153e75', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUHAMAD SALAHUDIN RIYADH', '3275031809040011', 'BEKASI', '2004-09-18', 'L', 'Islam', 'belum_menikah', 'KAV. KABEL MAS RT. 008 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA Kota Bekasi JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'KAV. KABEL MAS RT. 008 RW. 030 KEL. KALIABANG TENGAH KEC. BEKASI UTARA Kota Bekasi JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3d915415-643d-4203-8399-2e54da6dbdde', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ROBY YANSYAH', '3215102408050002', 'KARAWANG', '2005-08-24', 'L', 'Islam', 'belum_menikah', 'DUSUN PEDES 1 RT. 002 RW. 001 KEL. PAYUNGSARI KEC. PEDES Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ROBY', 'Ibu ROBY', 'DUSUN PEDES 1 RT. 002 RW. 001 KEL. PAYUNGSARI KEC. PEDES Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b647f7f2-31b2-4511-b8db-e31d9808c146', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ISMAIL KHAMKAH', '3305031310020002', 'KEBUMEN', '2002-10-13', 'L', 'Islam', 'belum_menikah', 'PLALANGAN KULON RT. 004 RW. 002 KEL. PURWOSARI KEC. PURING Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ISMAIL', 'Ibu ISMAIL', 'PLALANGAN KULON RT. 004 RW. 002 KEL. PURWOSARI KEC. PURING Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3f108730-84e8-4a72-bf04-02095fe5ea1f', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'MUHAMMAD SAHIL AKBAR', '3305122301050004', 'KEBUMEN', '2005-01-23', 'L', 'Islam', 'belum_menikah', 'KEMITIR RT. 003 RW. 001 KEL. BUMIREJO KEC. KEBUMEN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'KEMITIR RT. 003 RW. 001 KEL. BUMIREJO KEC. KEBUMEN Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ace03e9d-869f-4862-9713-3e3b3e7434c5', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'LUTFIL KHAKIM', '3305032702000002', 'KEBUMEN', '2000-02-27', 'L', 'Islam', 'belum_menikah', 'KEMILIRAN RT. 002 RW. 003 KEL. BUMIREJO KEC. PURING Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Kebumen', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LUTFIL', 'Ibu LUTFIL', 'KEMILIRAN RT. 002 RW. 003 KEL. BUMIREJO KEC. PURING Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('af1a26f9-508f-4324-8e7f-cb42d750bb61', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'LILIK SAFRUDIN', '3308082208020005', 'MAGELANG', '2002-08-22', 'L', 'Islam', 'belum_menikah', 'DSN.DEMAKAN RT. 004 RW. 011 KEL. BANYUBIRU KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LILIK', 'Ibu LILIK', 'DSN.DEMAKAN RT. 004 RW. 011 KEL. BANYUBIRU KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a3c86cf2-f3ea-4b36-ad27-4f08552ba734', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'RIKHO RANGGA RAHMAYUDA', '3322081603040001', 'KAB. SEMARANG', '2004-03-16', 'L', 'Islam', 'belum_menikah', 'TEMPURAN RT. 001 RW. 003 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIKHO', 'Ibu RIKHO', 'TEMPURAN RT. 001 RW. 003 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b4c3aa20-4afa-4c27-b9d3-5fe0993c6999', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'DENAND JAYATAMA', '3301022612030001', 'CILACAP', '2003-12-26', 'L', 'Islam', 'belum_menikah', 'JL. CANDRAYUDA RT. 001 RW. 006 KEL. PESANGGRAHAN KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DENAND', 'Ibu DENAND', 'JL. CANDRAYUDA RT. 001 RW. 006 KEL. PESANGGRAHAN KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4f4c1cc8-f3c5-415a-bdd1-41b3855a3cda', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ARKAAN NUR AERI', '3301210705030004', 'CILACAP', '2003-05-07', 'L', 'Islam', 'belum_menikah', 'JL. PENYU RT. 002 RW. 010 KEL. TEGALKAMULYAN KEC. CILACAP SELATAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ARKAAN', 'Ibu ARKAAN', 'JL. PENYU RT. 002 RW. 010 KEL. TEGALKAMULYAN KEC. CILACAP SELATAN Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e4fcbeb7-40a3-4795-a25a-f157d93d4d37', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AHMAD ARIF MISBAKHUL MUSTAGHFIRIN', '3315161906040006', 'GROBOGAN', '2004-06-19', 'L', 'Islam', 'belum_menikah', 'LATAK RT. 004 RW. 003 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'LATAK RT. 004 RW. 003 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c0ec912e-e938-4c80-bde1-485d401459c0', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUCHAMAD ALI MIFTAH', '3319042602030002', 'KUDUS', '2003-02-26', 'L', 'Islam', 'belum_menikah', 'UNDAAN TENGAH GG 3 RT. 003 RW. 001 KEL. UNDAAN TENGAH KEC. UNDAAN Kab. Kudus JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUCHAMAD', 'Ibu MUCHAMAD', 'UNDAAN TENGAH GG 3 RT. 003 RW. 001 KEL. UNDAAN TENGAH KEC. UNDAAN Kab. Kudus JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2ae9bc20-df8d-4ac7-bbe7-ef3db9569615', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ABDUL AZIS', '3322062409000004', 'KAB. SEMARANG', '2000-09-24', 'L', 'Islam', 'belum_menikah', 'CELENGAN RT. 004 RW. 002 KEL. LOPAIT KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ABDUL', 'Ibu ABDUL', 'CELENGAN RT. 004 RW. 002 KEL. LOPAIT KEC. TUNTANG Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6e8d9ae4-49f1-4b56-a653-33886b446622', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'WIDI ASTUTI', '3328085007020002', 'TEGAL', '2002-07-10', 'P', 'Islam', 'belum_menikah', 'KARANGANYAR RT. 013 RW. 007 KEL. KARANGANYAR KEC. KEDUNGBANTENG KAB. TEGAL JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WIDI', 'Ibu WIDI', 'KARANGANYAR RT. 013 RW. 007 KEL. KARANGANYAR KEC. KEDUNGBANTENG KAB. TEGAL JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0319b002-8072-4774-98f9-091d712668de', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'WAHYU KURNIAWAN', '3315161602030002', 'GROBOGAN', '2003-02-16', 'L', 'Islam', 'belum_menikah', 'LATAK RT. 003 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah WAHYU', 'Ibu WAHYU', 'LATAK RT. 003 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('53b081da-e213-4199-b02e-69aa4bdfe032', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'AHMAD NUR RIZKY', '3322063107030001', 'KAB. SEMARANG', '2003-07-31', 'L', 'Islam', 'belum_menikah', 'BEJIREJO RT. 003 RW. 003 KEL. KALIBEJI KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'BEJIREJO RT. 003 RW. 003 KEL. KALIBEJI KEC. TUNTANG Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0dc9a49b-81b0-4ff6-a38e-020fc8489156', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'BILLY ENJANG ZANUARTA', '3322082901030001', 'SEMARANG', '2003-01-29', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 006 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BILLY', 'Ibu BILLY', 'KRAJAN RT. 006 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
-- Batch 2
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5c184858-67ad-45b9-b3dd-820a061ce6b7', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'IBNU SANTORO', '3301202109030003', 'CILACAP', '2003-09-21', 'L', 'Islam', 'belum_menikah', 'DUSUN REJASARI RT. 008 RW. 008 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah IBNU', 'Ibu IBNU', 'DUSUN REJASARI RT. 008 RW. 008 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('244e553b-ea0c-493e-8d10-70d020bc788f', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ALDI PRATAMA PUTRA', '1604241805030001', 'ULAK LEBAR', '2003-05-18', 'L', 'Islam', 'belum_menikah', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDI', 'Ibu ALDI', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('dd57f596-b4e9-44b7-9206-d3dc9b8b9086', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'M. CHANDWA PUTRA', '3301201602050002', 'MAJALENGKA', '2006-02-16', 'L', 'Islam', 'belum_menikah', 'DUSUN RAWASARI RT. 010 RW. 007 KEL. RAWAJAYA KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'DUSUN RAWASARI RT. 010 RW. 007 KEL. RAWAJAYA KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1372087e-0378-4118-a541-13f7a373f3f0', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'AHMAD SUGONO', '3328051104930002', 'TEGAL', '1993-04-11', 'L', 'Islam', 'belum_menikah', 'KARANGANYAR RT. 004 RW. 002 KEL.KARANGANYAR KEC. PAGERBARANG Kab. Tegal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'KARANGANYAR RT. 004 RW. 002 KEL.KARANGANYAR KEC. PAGERBARANG Kab. Tegal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e2850941-3a65-4805-9587-da8b3b64ae7d', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AHMAD KHARIS', '3322070310020001', 'Kab. Semarang', '2002-10-03', 'L', 'Islam', 'belum_menikah', 'KARANG RT. 004 RW. 004 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083836187662', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AHMAD', 'Ibu AHMAD', 'KARANG RT. 004 RW. 004 KEL. TEGARON KEC. BANYUBIRU KAB. SEMARANG JAWA TENGAH', '083836187662', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6fc7c1e3-7b27-4bfe-8321-13a3c648c98a', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AKMAL TAUFIQUL AULIA', '3324072403020002', 'Kendal', '2002-03-24', 'L', 'Islam', 'belum_menikah', 'JL. SIMBANG NO. 6 RT. 001 RW. 006 KEL. BEBENGAN KEC. BOJA Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '‪081332049347', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AKMAL', 'Ibu AKMAL', 'JL. SIMBANG NO. 6 RT. 001 RW. 006 KEL. BEBENGAN KEC. BOJA Kab. Kendal JAWA TENGAH', '‪081332049347', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('12fddf70-1e99-48b4-b61e-2b713599da9a', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MUHAMAD AKHSINUL FUTUH', '3301022506030001', 'CILACAP', '2003-06-25', 'L', 'Islam', 'belum_menikah', 'JL. BUNTU NO. 05 RT. 003 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089672785870', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMAD', 'Ibu MUHAMAD', 'JL. BUNTU NO. 05 RT. 003 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089672785870', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e4bc61da-ab8c-4c70-877f-a690f57f8e0e', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'M. RIFQI ABRORI', '1802011706050003', 'KALIDADI', '2005-06-17', 'L', 'Islam', 'belum_menikah', 'DUSUN IV RT. 009 RW. 004 KEL. KALISARI KEC. KALIREJO KAB. LAMPUNG TENGAH LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085764607808', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'DUSUN IV RT. 009 RW. 004 KEL. KALISARI KEC. KALIREJO KAB. LAMPUNG TENGAH LAMPUNG', '085764607808', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9d0cc762-f1e2-4606-a36f-7034b1b4b711', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FAIQ MUHTAM', '3301022802010008', 'CILACAP', '2001-02-28', 'L', 'Islam', 'belum_menikah', 'JL. DIPAMENAWI NO. 32 RT. 001 RW. 014 KEL. KALISABUK KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089637722713', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAIQ', 'Ibu FAIQ', 'JL. DIPAMENAWI NO. 32 RT. 001 RW. 014 KEL. KALISABUK KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089637722713', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e1bd7c1-d055-49a8-a964-435a135f44c8', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'MUHAMMAD HENDRAWAN ADITYA', '3324082110000003', 'KENDAL', '2000-10-21', 'L', 'Islam', 'belum_menikah', 'DK. RAGIL RT. 001 RW. 011 KEL. PLANTARAN KEC. KALIWUNGU SELATAN Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083108561114', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'DK. RAGIL RT. 001 RW. 011 KEL. PLANTARAN KEC. KALIWUNGU SELATAN Kab. Kendal JAWA TENGAH', '083108561114', 'approved', '2024-01-01', 'Data siswa dari CSV - Cancel');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('840c01d3-131e-485c-8a9f-9dbafbeeed72', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'AGHISNA ZAKI ABDUR ROHMAN', '3322150609050001', 'KAB. SEMARANG', '2005-09-06', 'L', 'Islam', 'belum_menikah', 'LINGKUNGAN KRAJAN KIDUL RT. 002 RW. 005 KEL. WUJIL KEC. BERGAS Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Semarang', 'Jawa Tengah', '12345', '082313662482', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AGHISNA', 'Ibu AGHISNA', 'LINGKUNGAN KRAJAN KIDUL RT. 002 RW. 005 KEL. WUJIL KEC. BERGAS Kab. Semarang JAWA TENGAH', '082313662482', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2eb6b1aa-60f4-40b1-836c-b7b542ef2abc', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'BAGUS PRASETYONO', '3322070304060001', 'KAB. SEMARANG', '2006-04-03', 'L', 'Islam', 'belum_menikah', 'DSN. JONGGRANGAN RT. 002 RW. 007 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Kab. Semarang', 'Jawa Tengah', '12345', '083865088759', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BAGUS', 'Ibu BAGUS', 'DSN. JONGGRANGAN RT. 002 RW. 007 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '083865088759', 'approved', '2024-01-01', 'Data siswa dari CSV - Kerja');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('57791405-8508-4be1-b34d-c02a48b1d086', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DWI SETYAWAN', '3275030712050011', 'BEKASI', '2005-12-07', 'L', 'Islam', 'belum_menikah', '" 	KP. PENGGILINGAN BARU RT. 002 RW. 003 KEL. HARAPANBARU KEC. BEKASI UTARA Kota Bekasi JAWA BARAT"', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083865088759', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DWI', 'Ibu DWI', '" 	KP. PENGGILINGAN BARU RT. 002 RW. 003 KEL. HARAPANBARU KEC. BEKASI UTARA Kota Bekasi JAWA BARAT"', '083865088759', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9222fd53-84f4-493f-a49b-e7dd26391832', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'REHAN SAIMAN PUTRA', '3275020206060011', 'JAKARTA', '2006-06-02', 'L', 'Islam', 'belum_menikah', 'BABAKAN CIANJUR RT. 001 RW. 031 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081241941930', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah REHAN', 'Ibu REHAN', 'BABAKAN CIANJUR RT. 001 RW. 031 KEL. NAGASARI KEC. KARAWANG BARAT Kab. Karawang JAWA BARAT', '081241941930', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fa872018-7c3a-44a0-8cbb-dffc95f05796', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'RIDHO ALFATH AZZAKI', '3216020905060012', 'JAKARTA', '2006-05-09', 'L', 'Islam', 'belum_menikah', 'KP. CIBUNGUR INDAH NO. 168 RT. 004 RW. 014 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '087821617619', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RIDHO', 'Ibu RIDHO', 'KP. CIBUNGUR INDAH NO. 168 RT. 004 RW. 014 KEL. KARAWANG WETAN KEC. KARAWANG TIMUR Kab. Karawang JAWA BARAT', '087821617619', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('808f009b-d6e8-4135-9f6f-bf9cd5d48784', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DYIMAST VEBRYANT SETTYAJY', '3307120802020002', 'WONOSOBO', '2002-02-08', 'L', 'Islam', 'belum_menikah', 'KALIJERUK RT. 003 RW. 004 KEL. SIWURAN KEC. GARUNG Kab. Wonosobo JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085700166137', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DYIMAST', 'Ibu DYIMAST', 'KALIJERUK RT. 003 RW. 004 KEL. SIWURAN KEC. GARUNG Kab. Wonosobo JAWA TENGAH', '085700166137', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('05d122e9-d7b1-4e8b-a79a-b83d97ba2b14', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'M. ABDUL ROHMAN ASHARI', '1704071801980001', 'PASAR BARU', '1998-01-13', 'L', 'Islam', 'belum_menikah', 'PASAR BARU RT. 000 RW. 000 KEL. PASAR BARU KEC. NASAL Kab. Kaur BENGKULU', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081226214772', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'PASAR BARU RT. 000 RW. 000 KEL. PASAR BARU KEC. NASAL Kab. Kaur BENGKULU', '081226214772', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3287149f-2702-44c4-b92f-50c6311f8e58', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AXSEL ADE PRADANA', '3322102812970001', 'KAB. SEMARANG', '1997-12-28', 'L', 'Islam', 'belum_menikah', 'KUPANG TEGAL RT. 004 RW. 004 KEL. KUPANG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085643674453', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AXSEL', 'Ibu AXSEL', 'KUPANG TEGAL RT. 004 RW. 004 KEL. KUPANG KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '085643674453', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c8778081-475b-492c-ba87-f4c2bcb12bbf', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ANANG KAROMAIN', '3322060509010008', 'KAB. SEMARANG', '2001-09-05', 'L', 'Islam', 'belum_menikah', 'CIKAL RT. 006 RW. 007 KEL. TUNTANG KEC. TUNTANG Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083138028476', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ANANG', 'Ibu ANANG', 'CIKAL RT. 006 RW. 007 KEL. TUNTANG KEC. TUNTANG Kab. Semarang JAWA TENGAH', '083138028476', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5c8f5865-8769-465c-943e-39fd15a25f02', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'RAMDHANI', '3305182610050002', 'KEBUMEN', '2005-10-26', 'L', 'Islam', 'belum_menikah', 'PEKUNCEN RT. 004 RW. 003 KEL. PEKUNCEN KEC. SEMPOR Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0882005544238', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAMDHANI', 'Ibu RAMDHANI', 'PEKUNCEN RT. 004 RW. 003 KEL. PEKUNCEN KEC. SEMPOR Kab. Kebumen JAWA TENGAH', '0882005544238', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5ce4e670-4e36-4776-9d10-3a08e5f5a948', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MOHAMMAD LUBADUL FIKRI', '3301020203990006', 'CILACAP', '1999-03-02', 'L', 'Islam', 'belum_menikah', 'JL. KEBUN JERUK NO RT. 001 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895385088904', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMMAD', 'Ibu MOHAMMAD', 'JL. KEBUN JERUK NO RT. 001 RW. 005 KEL. KESUGIHAN KIDUL KEC. KESUGIHAN Kab. Cilacap JAWA TENGAH', '0895385088904', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('14ba41a0-79f1-46ae-bbbf-d0e1e5e451ad', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RAHMAD MORIES SYAHJIHAN', '3322092206960001', 'KAB. SEMARANG', '1996-06-22', 'L', 'Islam', 'belum_menikah', 'DSN. KENTENG RT. 001 RW. 007 KEL. SUMOWONO KEC. SUMOWONO Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085866560593', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAHMAD', 'Ibu RAHMAD', 'DSN. KENTENG RT. 001 RW. 007 KEL. SUMOWONO KEC. SUMOWONO Kab. Semarang JAWA TENGAH', '085866560593', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1a81bf46-004e-463a-a371-bae21593c7ef', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'ADE FITRIONO', '3303020512020002', 'PURBALINGGA', '2002-12-05', 'L', 'Islam', 'belum_menikah', 'KARANG NANGKA RT. 001 RW. 003 KEL. KARANGNANGKA KEC. BUKATEJA Kab. Purbalingga JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADE', 'Ibu ADE', 'KARANG NANGKA RT. 001 RW. 003 KEL. KARANGNANGKA KEC. BUKATEJA Kab. Purbalingga JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ac92ce04-0740-4521-97d4-e7b12b6c2be5', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'SUROSO', '1801171503830010', 'PURWOREJO', '1983-03-15', 'L', 'Islam', 'belum_menikah', 'TAMAN SARI RT. 003 RW. 004 KEL. TRIMOMUKTI KEC. CANDIPURO Kab. Lampung Selatan LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SUROSO', 'Ibu SUROSO', 'TAMAN SARI RT. 003 RW. 004 KEL. TRIMOMUKTI KEC. CANDIPURO Kab. Lampung Selatan LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b149babc-2c63-4873-958a-6139acb0b173', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'CATUR SISWO UTOMO', '3315152108060001', 'GROBOGAN', '2006-08-21', 'L', 'Islam', 'belum_menikah', 'DSN TARUMAN RT. 001 RW. 002 KEL. TARUMAN KEC. KLAMBU Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088238198301', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah CATUR', 'Ibu CATUR', 'DSN TARUMAN RT. 001 RW. 002 KEL. TARUMAN KEC. KLAMBU Kab. Grobogan JAWA TENGAH', '088238198301', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('94b8956b-c1da-4e62-971e-7d0835762097', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'DHIVA ANANDA LEO SANTOSO', '3321011508040007', 'DEMAK', '2004-08-15', 'L', 'Islam', 'belum_menikah', 'CANDISARI RT. 004 RW. 001 KEL. CANDISARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088227280566', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DHIVA', 'Ibu DHIVA', 'CANDISARI RT. 004 RW. 001 KEL. CANDISARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', '088227280566', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d09837ce-4f4c-4d5f-8889-98226e1658e3', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'GALANG AJI PRATAMA', '3374163010000001', 'SEMARANG', '2000-10-30', 'L', 'Islam', 'belum_menikah', 'MANGUNHARJO DK PANGGUNG RT. 001 RW. 004 KEL. MANGUNHARJO KEC. TUGU KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089697270704', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GALANG', 'Ibu GALANG', 'MANGUNHARJO DK PANGGUNG RT. 001 RW. 004 KEL. MANGUNHARJO KEC. TUGU KOTA SEMARANG JAWA TENGAH', '089697270704', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('24fbac82-abb6-43d7-9b4b-b6567a1f5803', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'SATRIYO AGUNG NUGROHO', '3503070804840001', 'TRENGGALEK', '1984-04-08', 'L', 'Islam', 'belum_menikah', 'DUSUN KRAJAN RT. 007 RW. 004 KEL. BOGORAN KEC. KAMPAK Kab. Trenggalek JAWA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SATRIYO', 'Ibu SATRIYO', 'DUSUN KRAJAN RT. 007 RW. 004 KEL. BOGORAN KEC. KAMPAK Kab. Trenggalek JAWA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ae2c8adf-4320-4179-bef6-98cc34873b7b', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ILHAM ABDULLOH', '3301021006040001', 'CILACAP', '2004-06-10', 'L', 'Islam', 'belum_menikah', 'JL. SUKUN NO 06 RT. 001 RW. 006 KEL. KARANGKANDRI KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089504041907', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ILHAM', 'Ibu ILHAM', 'JL. SUKUN NO 06 RT. 001 RW. 006 KEL. KARANGKANDRI KEC. KESUGIHAN KAB. CILACAP JAWA TENGAH', '089504041907', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e3ba7209-af67-4163-a4f1-9a9a78c8859d', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'BAGAS SATRIO PRABOWO', '3301081603050003', 'CILACAP', '2005-03-16', 'L', 'Islam', 'belum_menikah', 'DUSUN CIPTOSARI RT. 003 RW. 003 KEL. BREBEG KEC. JERUKLEGI  KAB. CILACAP JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083108636164', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah BAGAS', 'Ibu BAGAS', 'DUSUN CIPTOSARI RT. 003 RW. 003 KEL. BREBEG KEC. JERUKLEGI  KAB. CILACAP JAWA TENGAH', '083108636164', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bc602d90-5502-4e7d-ae57-1d42aa0b7b93', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'DINA ELIVIA', '3374065302040003', 'SEMARANG', '2004-02-13', 'P', 'Islam', 'belum_menikah', 'PLAMONGANSARI RT. 004 RW. 012 KEL. PLAMONGANSARI KEC. PEDURUNGAN KOTA SEMARANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '089504222183', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah DINA', 'Ibu DINA', 'PLAMONGANSARI RT. 004 RW. 012 KEL. PLAMONGANSARI KEC. PEDURUNGAN KOTA SEMARANG JAWA TENGAH', '089504222183', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('4f0e7e04-1662-4a51-abef-28404eab3771', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'NOVITA PURNAMASARI', '3322084207030002', 'KAB. SEMARANG', '2002-07-02', 'P', 'Islam', 'belum_menikah', 'DSN. SETRO RT. 001 RW. 006 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '083842329412', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NOVITA', 'Ibu NOVITA', 'DSN. SETRO RT. 001 RW. 006 KEL. NGRAPAH KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '083842329412', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d1925c1c-83e6-4056-98af-a8fada0d4c92', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'PRISTI HIKMAH WIJAYA', '3305104605030001', 'KEBUMEN', '2003-05-06', 'P', 'Islam', 'belum_menikah', 'DK KALIGAYAM RT. 002 RW. 001 KEL. KOROWELANG KEC. KUTOWINANGUN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '085870528040', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah PRISTI', 'Ibu PRISTI', 'DK KALIGAYAM RT. 002 RW. 001 KEL. KOROWELANG KEC. KUTOWINANGUN Kab. Kebumen JAWA TENGAH', '085870528040', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('0539c5c7-aee9-4c5e-b7f8-ece7db6152b2', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'FAJAR APRIANTO', '3301201904030001', 'CILACAP', '2003-04-19', 'L', 'Islam', 'belum_menikah', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895414903720', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAJAR', 'Ibu FAJAR', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '0895414903720', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a31f5f3a-85f0-4b55-9a99-ee11f65d2135', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'AYU KANIA PUTRI PRATAMA', '3315116907030001', 'GROBOGAN', '2003-07-29', 'P', 'Islam', 'belum_menikah', 'DUSUN TRISIK RT. 015 RW. 001 KEL. TARUB KEC. TAWANGHARJO KAAB. GROBOGAN JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0882006361342', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AYU', 'Ibu AYU', 'DUSUN TRISIK RT. 015 RW. 001 KEL. TARUB KEC. TAWANGHARJO KAAB. GROBOGAN JAWA TENGAH', '0882006361342', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('a9a6cdf8-915a-4814-868e-90657e47797a', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RUDIYANTO', '3329140709030001', 'BREBES', '2003-06-09', 'L', 'Islam', 'belum_menikah', 'Alamat akan diupdate', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '0895414903720', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RUDIYANTO', 'Ibu RUDIYANTO', 'Alamat akan diupdate', '0895414903720', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('ff3814d7-cc52-4677-8b11-dce3a83946ab', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ORBAR SADEWO AHMAD', '3322122310050002', 'KAB. SEMARANG', '2005-10-23', 'L', 'Islam', 'belum_menikah', 'DSN. DOPLANG I RT. 003 RW. 004 KEL. PAKIS KEC. BRINGIN Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '088215749647', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ORBAR', 'Ibu ORBAR', 'DSN. DOPLANG I RT. 003 RW. 004 KEL. PAKIS KEC. BRINGIN Kab. Semarang JAWA TENGAH', '088215749647', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c0cc8753-88c9-4178-b3a6-0224dc041344', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'ALIF ISMAYANA FADHILAH', '3305050504010001', 'KEBUMEN', '2001-04-05', 'L', 'Islam', 'belum_menikah', '" 	GEBANG RT. 003 RW. 002 KEL. JERUKAGUNG KEC. KLIRONG Kab. Kebumen JAWA TENGAH"', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '082322622118', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALIF', 'Ibu ALIF', '" 	GEBANG RT. 003 RW. 002 KEL. JERUKAGUNG KEC. KLIRONG Kab. Kebumen JAWA TENGAH"', '082322622118', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2d0d2b70-ed76-4d85-a6bd-7b3c53945152', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'LEO WISNU WIDODO', '3305262904010002', 'KEBUMEN', '2001-04-29', 'L', 'Islam', 'belum_menikah', 'WIDORO RT. 004 RW. 005 KEL. WIDORO KEC. KARANGSAMBUNG Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '087719764121', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah LEO', 'Ibu LEO', 'WIDORO RT. 004 RW. 005 KEL. WIDORO KEC. KARANGSAMBUNG Kab. Kebumen JAWA TENGAH', '087719764121', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('78c02392-7a9c-4c62-aad7-69e28654cd67', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'RITO SAEFUL ANWAR', '1903040704030001', 'CILACAP', '2003-04-07', 'L', 'Islam', 'belum_menikah', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081229170783', '<EMAIL>', 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RITO', 'Ibu RITO', 'DUSUN BULUREJA RT. 002 RW. 007 KEL. BANTARSARI KEC. BANTARSARI Kab. Cilacap JAWA TENGAH', '081229170783', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('187f158f-c75f-450c-9785-16821cabe687', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FERDY OKTAFIAN', '1807211910050002', 'LABUHAN RATU', '2005-10-19', 'L', 'Islam', 'belum_menikah', 'DUSUN BERINGIN RT. 004 RW. 001 KEL. LABUHAN RATU V KEC. LABUHAN RATU Kab. Lampung Timur LAMPUNG', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FERDY', 'Ibu FERDY', 'DUSUN BERINGIN RT. 004 RW. 001 KEL. LABUHAN RATU V KEC. LABUHAN RATU Kab. Lampung Timur LAMPUNG', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('53586f53-deea-4d3f-a5a9-b419e3e2cace', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RAFI MUHAMMAD MAJID', '3374082104990001', 'SEMARANG', '1999-04-21', 'L', 'Islam', 'belum_menikah', 'CANDI PERSIL NO.38 RT. 004 RW. 003 KEL. KALIWIRU KEC. CANDISARI Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAFI', 'Ibu RAFI', 'CANDI PERSIL NO.38 RT. 004 RW. 003 KEL. KALIWIRU KEC. CANDISARI Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2e9ede17-0f68-4891-9b69-b7ab15934359', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FAHED FERNANDA ADITYA', '3374081704010003', 'SEMARANG', '2001-04-17', 'L', 'Islam', 'belum_menikah', 'JANGLI KRAJAN RT. 010 RW. 006 KEL. KARANGANYAR GUNUNG KEC. CANDISARI Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAHED', 'Ibu FAHED', 'JANGLI KRAJAN RT. 010 RW. 006 KEL. KARANGANYAR GUNUNG KEC. CANDISARI Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('b3035a14-0698-46e4-903d-0fabf34de0b9', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'YOPI IMAYANA ANGGA SAPUTRA', '3322101112940002', 'KAB.SEMARANG', '1994-12-11', 'L', 'Islam', 'belum_menikah', 'JAGALAN RT. 003 RW. 007 KEL. KRANGGAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YOPI', 'Ibu YOPI', 'JAGALAN RT. 003 RW. 007 KEL. KRANGGAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('9e621abf-99ab-4278-bcba-989cb61ee891', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'ROCKY SETYAJI ADI SAPUTRA', '3301220509020002', 'CILACAP', '2002-09-05', 'L', 'Islam', 'belum_menikah', 'JL MASJID NO.17 C RT. 001 RW. 001 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ROCKY', 'Ibu ROCKY', 'JL MASJID NO.17 C RT. 001 RW. 001 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('1b96ef44-e4f9-4e2d-80d4-cc89c306a904', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'ACHMAD NANDANG PANGESTU', '3301223009990003', 'CILACAP', '1999-09-30', 'L', 'Islam', 'belum_menikah', 'JL RINJANI NO 208 RT. 005 RW. 016 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ACHMAD', 'Ibu ACHMAD', 'JL RINJANI NO 208 RT. 005 RW. 016 KEL. SIDANEGARA KEC. CILACAP TENGAH Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c1ef20df-77ea-4666-8d0d-2e03953e1f92', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AKHMAD KHOERURRIZKI', '3301011911060002', 'CILACAP', '2006-11-19', 'L', 'Islam', 'belum_menikah', 'DUSUN CIBABUT RT. 004 RW. 008 KEL. JATISARI KEC. KEDUNGREJA Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AKHMAD', 'Ibu AKHMAD', 'DUSUN CIBABUT RT. 004 RW. 008 KEL. JATISARI KEC. KEDUNGREJA Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('2f46d18b-7ab3-4f3c-bf45-ea2ab4516e5b', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'RISKI KURNIAWAN', '3322105712020003', 'KAB.SEMARANG', '2002-12-17', 'L', 'Islam', 'belum_menikah', 'DSN. KINTELAN RT. 013 RW. 005 KEL. PASEKAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RISKI', 'Ibu RISKI', 'DSN. KINTELAN RT. 013 RW. 005 KEL. PASEKAN KEC. AMBARAWA Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('eb2fbc24-20f5-4c05-82c3-73e85f5236e8', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'M. NAUFAL FEBRIAN SUNANTO', '3324162102060001', 'KENDAL', '2006-02-21', 'L', 'Islam', 'belum_menikah', 'TAMBAKSARI RT. 003 RW. 005 KEL. TAMBAKSARI KEC. ROWOSARI Kab. Kendal JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah M.', 'Ibu M.', 'TAMBAKSARI RT. 003 RW. 005 KEL. TAMBAKSARI KEC. ROWOSARI Kab. Kendal JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('bbdc7b42-7ffc-4abf-b277-3576146b7af6', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'YOGA MAS PRASETYA', '3210152303030021', 'MAJALENGKA', '2002-03-22', 'L', 'Islam', 'belum_menikah', 'BLOK MANIS RT. 002 RW. 003 KEL. RANDEGAN KULON KEC. JATITUJUH Kab. Majalengka JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah YOGA', 'Ibu YOGA', 'BLOK MANIS RT. 002 RW. 003 KEL. RANDEGAN KULON KEC. JATITUJUH Kab. Majalengka JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('dca03d92-66af-470f-a019-8cf0fd36b57c', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MOHAMAD BAGUS ANJALU', '3505191511040003', 'BEKASI', '2004-11-15', 'L', 'Islam', 'belum_menikah', 'DSN TUWUHREJO RT. 004 RW. 001 KEL. KESAMBEN KEC. KESAMBEN KAB. BLITAR JAWA TIMUR', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MOHAMAD', 'Ibu MOHAMAD', 'DSN TUWUHREJO RT. 004 RW. 001 KEL. KESAMBEN KEC. KESAMBEN KAB. BLITAR JAWA TIMUR', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('97866b27-276f-4254-a3e2-1a6cc67e53af', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'SAMSUL MAARIF', '3327031112980003', 'PEMALANG', '1998-12-11', 'L', 'Islam', 'belum_menikah', 'MENDELEM RT. 003 RW. 008 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah SAMSUL', 'Ibu SAMSUL', 'MENDELEM RT. 003 RW. 008 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('576456d5-3575-4caf-8a8e-a00880224cd2', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'FATHAN MUBIN', '3327033006040003', 'PEMALANG', '2004-06-30', 'L', 'Islam', 'belum_menikah', 'DUKUH BULU RT. 005 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FATHAN', 'Ibu FATHAN', 'DUKUH BULU RT. 005 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('108e8fcd-8e37-4b8f-b4c8-681d8ecc3586', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'NUR JAMILUDIN', '3327030208010008', 'PEMALANG', '2001-08-02', 'L', 'Islam', 'belum_menikah', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK KAB. PEMALANG JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NUR', 'Ibu NUR', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK KAB. PEMALANG JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6a84373c-02e2-4377-b093-15442398fe4e', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'KHAUROHIM', '3301171412050001', 'CILACAP', '2005-12-14', 'L', 'Islam', 'belum_menikah', 'JLN SAWO NO 09 RT. 003 RW. 004 KEL. KARANGJATI KEC. SAMPANG Kab. Cilacap JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHAUROHIM', 'Ibu KHAUROHIM', 'JLN SAWO NO 09 RT. 003 RW. 004 KEL. KARANGJATI KEC. SAMPANG Kab. Cilacap JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('5f2c43b4-6b1b-4ac6-8684-951187db4144', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'GASELA PUTRA ASRIL FAJWA TRYAWAN', '1604241508060002', 'ULAK LEBAR', '2006-08-15', 'L', 'Islam', 'belum_menikah', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah GASELA', 'Ibu GASELA', 'ULAK LEBAR RT. 000 RW. 000 KEL. ULAK LEBAR KEC. TANJUNG SAKTI PUMI Kab. Lahat SUMATERA SELATAN', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('e8bae34a-1936-4594-9ffe-9b8c61a69121', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUJIWAT AJI', '3322080805020002', 'KAB. SEMARANG', '2002-05-08', 'L', 'Islam', 'belum_menikah', 'JAMBU KULON RT. 003 RW. 003 KEL. JAMBU KEC. JAMBU Kab. Semarang JAWA TENGA', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUJIWAT', 'Ibu MUJIWAT', 'JAMBU KULON RT. 003 RW. 003 KEL. JAMBU KEC. JAMBU Kab. Semarang JAWA TENGA', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('3ddfa6b7-75ab-49e5-98d4-baf20370f881', 'c853738c-3cdb-4da9-821c-ca3e649c680e', 'FAJAR AFTA FAHMATULLAILY', '3322182103010005', 'KAB. SEMARANG', '2001-03-21', 'L', 'Islam', 'belum_menikah', 'LANGENSARI BARAT RT. 007 RW. 006 KEL. LANGENSARI KEC. UNGARAN BARAT Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah FAJAR', 'Ibu FAJAR', 'LANGENSARI BARAT RT. 007 RW. 006 KEL. LANGENSARI KEC. UNGARAN BARAT Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('68406630-a6de-4bcf-b490-2520a52f59f5', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'JUVI DIAN PRATAMA', '3315162110040004', 'GROBOGAN', '2004-10-21', 'L', 'Islam', 'belum_menikah', 'DUSUN LATAK RT. 002 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah JUVI', 'Ibu JUVI', 'DUSUN LATAK RT. 002 RW. 002 KEL. LATAK KEC. GODONG Kab. Grobogan JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('cd15926e-06ec-4dfe-b8cb-6e6cf9912661', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ADEVIA KHINANTI', '3325126604010002', 'BATANG', '2001-04-26', 'P', 'Islam', 'belum_menikah', 'SAWAHJOHO RT. 010 RW. 004 KEL. SAWAHJOHO KEC. WARUNGASEM Kab. Batang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ADEVIA', 'Ibu ADEVIA', 'SAWAHJOHO RT. 010 RW. 004 KEL. SAWAHJOHO KEC. WARUNGASEM Kab. Batang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('8b9c8ec4-0ac3-43b8-afe7-a76ac9536e4b', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ARLINDA', '3212315207990001', 'INDRAMAYU', '1999-07-12', 'P', 'Islam', 'belum_menikah', 'PASARBATANG RT. 002 RW. 011 KEL. PASARBATANG KEC. BREBES Kab. Brebes JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ARLINDA', 'Ibu ARLINDA', 'PASARBATANG RT. 002 RW. 011 KEL. PASARBATANG KEC. BREBES Kab. Brebes JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('11780c3c-9de7-48cb-8fcb-9d0b089f89d7', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'NAELI SIVA', '3305136105030001', 'KEBUMEN', '2003-05-21', 'P', 'Islam', 'belum_menikah', 'GONDANG RT. 002 RW. 008 KEL. KUWAYUHAN KEC. PEJAGOAN Kab. Kebumen JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NAELI', 'Ibu NAELI', 'GONDANG RT. 002 RW. 008 KEL. KUWAYUHAN KEC. PEJAGOAN Kab. Kebumen JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('6d48386d-525f-4ba2-9659-4725a455cb37', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'NANANG ZAENAL ARIFIN', '3322110204040004', 'KAB. SEMARANG', '2004-04-02', 'L', 'Islam', 'belum_menikah', 'DSN KLOWOH RT. 006 RW. 004 KEL. LEMAHIRENG KEC. BAWEN Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah NANANG', 'Ibu NANANG', 'DSN KLOWOH RT. 006 RW. 004 KEL. LEMAHIRENG KEC. BAWEN Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('aad5af4c-f90d-4ee0-93c3-ee75ad6bd325', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'KHOIRUL ANAM', '3322071501010001', 'KAB. SEMARANG', '2001-01-15', 'L', 'Islam', 'belum_menikah', 'KRAJAN II RT. 002 RW. 003 KEL. TEGARON KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KHOIRUL', 'Ibu KHOIRUL', 'KRAJAN II RT. 002 RW. 003 KEL. TEGARON KEC. BANYUBIRU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('fff262a3-5a9a-4bd8-aa4e-80eaa096bf5f', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'TRI WAHYU PAMUNGKAS', '3322102502960001', 'KAB. SEMARANG', '1996-02-25', 'L', 'Islam', 'belum_menikah', 'KRAJAN RT. 005 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah TRI', 'Ibu TRI', 'KRAJAN RT. 005 RW. 001 KEL. KELURAHAN KEC. JAMBU Kab. Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Belum Berangkat');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('d26af369-f4d1-43bb-bd09-9554a9528ed3', '1cb347b6-5e80-4caa-912c-d47ec61fafdb', 'KISNA NUR AWI', '3327030211030006', 'PEMALANG', '2003-11-02', 'L', 'Islam', 'belum_menikah', 'DK BULU RT. 004 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah KISNA', 'Ibu KISNA', 'DK BULU RT. 004 RW. 002 KEL. BELIK KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('f2126dec-67cd-4258-82c2-5b95df1885cf', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'MUHAMMAD DAVA', '3172043112040004', 'JAKARTA', '2004-12-31', 'L', 'Islam', 'belum_menikah', 'DUSUN KOBAK KARIM RT. 012 RW. 004 KEL. KALANGSURYA KEC. RENGASDENGKLOK Kab. Karawang JAWA BARAT', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah MUHAMMAD', 'Ibu MUHAMMAD', 'DUSUN KOBAK KARIM RT. 012 RW. 004 KEL. KALANGSURYA KEC. RENGASDENGKLOK Kab. Karawang JAWA BARAT', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('06c61c82-92ce-437b-b6b5-5e50b2bab6ac', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'AFIF MAS UL', '3327030208010008', 'PEMALANG', '2001-08-02', 'L', 'Islam', 'belum_menikah', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah AFIF', 'Ibu AFIF', 'DSN KARANGANYAR RT. 001 RW. 004 KEL. MENDELEM KEC. BELIK Kab. Pemalang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('599c4303-5dcf-4aa1-a440-200f0d5ac575', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'ALDA REFA', '3401121503990001', 'KULON PROGO', '1999-03-15', 'L', 'Islam', 'belum_menikah', 'KETILENG INDAH BLOK K-22.A RT. 002 RW. 012 KEL. SENDANGMULYO KEC. TEMBALANG Kota Semarang JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah ALDA', 'Ibu ALDA', 'KETILENG INDAH BLOK K-22.A RT. 002 RW. 012 KEL. SENDANGMULYO KEC. TEMBALANG Kota Semarang JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');
INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, status_pernikahan, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, kode_pos, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, jurusan, nama_ayah, nama_ibu, alamat_keluarga, nomor_hp_keluarga, status_pendaftaran, tanggal_daftar, catatan) VALUES ('c2494e2d-a04a-4108-a5cb-57836048c32c', '7d73dafd-57a5-4e15-a67d-f090ee5db537', 'RAVI ASHAR MAULANA', '3321010504000005', 'DEMAK', '2000-04-05', 'L', 'Islam', 'belum_menikah', 'JL. PUCANG SARI I NO. 26 RT. 001 RW. 017 KEL. BATURSARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', 'Kelurahan Sample', 'Kecamatan Sample', 'Jakarta', 'Jawa Tengah', '12345', '081234567890', NULL, 'SMA', 'SMA Negeri 1', 2020, 'IPA', 'Ayah RAVI', 'Ibu RAVI', 'JL. PUCANG SARI I NO. 26 RT. 001 RW. 017 KEL. BATURSARI KEC. MRANGGEN Kab. Demak JAWA TENGAH', '081234567890', 'approved', '2024-01-01', 'Data siswa dari CSV - Tidak ada keterangan');

-- Insert Penempatan (110 records)
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('539c0917-4c38-4fda-8086-b3a2406628a2', '2396ee88-3928-401a-ae73-be6e1a838b63', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 180000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d2f4dcc5-6ccf-489b-9640-7a0127f7e8b3', 'a03565e4-c71f-41e1-ad17-a58df6414c53', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 181000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5d9fb730-e9f4-4e04-afba-b12e22d352a1', 'b5882487-4131-41ea-8982-168737ca4dec', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 182000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('dc557117-80f7-4567-9c36-3906c4d18e1a', '7e4b5e60-208e-4523-9d2b-9d738ad4d997', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 183000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2eaed06c-0712-4e48-8df8-19f6179915e8', '6281a97a-9693-42ca-a7cd-f9f2fb940473', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 184000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('098749a1-7b40-465f-b626-5c537bedc790', '738b6fde-1f0c-4ca2-90d4-fb812d6a3945', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 185000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('ed625583-c7ef-4663-8015-561b804280a9', 'f697d16e-4f0b-4673-a2c9-1bf0d500d3a5', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 186000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b6e5b37d-45bb-478f-a5bd-2a67421f2124', '88cf331e-f3d9-4130-a5be-d55aa8eb1735', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 187000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('21a1b56d-359d-4fec-8fe1-b7c75fdbc711', 'd7c27df9-96ff-47e4-b6c1-eb28552eef58', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 188000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e721af8c-caaf-4fd7-a29d-67f27e741983', '8a37ad58-3f17-4b30-8123-069739d87857', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 189000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c5ddb6b4-9e47-4f56-adec-9ffb00e0c9d1', '36d13fc5-7e54-421c-afc7-f50d64d47e42', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 190000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b673dd46-885c-476b-82fc-f96c2450d178', '0829dd78-93a9-46ef-93c9-0daaa5312e8d', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 191000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fd8373f0-28b0-4a7b-991f-25da47f165e2', 'eed45e76-ac92-4158-98a0-8210ed14f642', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 192000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('57a5020b-fa3d-4b59-b7bc-142b9cc40bc9', 'f0a779e2-bdb1-4b99-b800-6d8a1bdce2fd', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 193000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('24076a3c-a487-4289-82b2-aef3d226d4c5', '7f0b7ac8-213f-4121-a78b-0643a0476fb8', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 194000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b3d086fc-b538-4669-9744-b4bab5a6171b', 'd8cb3b12-69fe-46a3-8c4a-fa1f7a3f97f6', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 195000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('621c94bc-d0ba-4c27-9e51-823e14e40bb3', '9554612a-5a8f-4489-81c2-438353bd60d4', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 196000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('109c6d88-b8b2-4fcd-9126-a5a2f1544727', '8d548be4-873a-451c-8ee3-57fc7f5e953e', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 197000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1a5336e8-c492-4360-9a24-038bf4eed446', 'bf0e62da-6bd0-4395-80c6-7dd94483e483', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 198000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fe73fa36-cb91-44cd-ba81-23e3ceedc75c', '8de10c45-16c9-498b-a581-05490e0e48b4', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 199000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('555685e2-f432-4c86-b09f-d140f77b307e', 'ebafc1b7-4f55-4429-b906-4cd75bae3c1d', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 200000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d73d413f-ceca-4f12-b9b4-40b640cf9bba', 'f861daed-78ba-4150-bb9b-abcca9d84079', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 201000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9b5ba71d-1a59-416d-b2c2-6d596e4501ba', '08741280-c333-47d7-be83-1495343c5e04', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 202000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('55160ddf-d4c0-43c4-9486-1efd9d89504d', 'a400f4d6-1429-41fe-9064-cc8980581e53', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 203000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a890be9c-238e-441c-8b51-4b4acc8e8a23', 'a863c1cf-fbb4-4deb-85cc-b5b20762b977', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 204000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4ab96104-342c-4899-8cfd-c7cea1e6e4e2', '9e7efa27-7b3b-4214-a68d-c194430bf34d', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 205000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('40cc6cad-2ac4-4764-9053-b7e372d518b0', '2fcf750c-7582-4c2d-82c8-00d6d0e27899', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 206000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('31fb8d12-4dd9-43ab-945f-8d6eb35adf3c', 'a5701a9e-c8cd-4d99-81cf-68d3a8d88585', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 207000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3f8ee61f-f34b-4ccc-b2fc-4064e693deb2', '0a0c2ba6-e20b-46f4-9cb6-758c907bce94', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 208000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('0cb124cf-a86e-4c65-83d9-6a0a60974f4c', '11ed7995-9a5a-42e7-8646-77e1cd992b96', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 209000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2082da2d-96c3-453c-801a-7357eb74675c', '6e6de6a9-b3ef-43b8-bd90-36350df14daa', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 210000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e298e5d1-00a2-414f-a575-bf2f30c4eb91', '54ab142d-0b23-4622-89dc-e000207efc70', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 211000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('dfbab327-ea7a-4648-8fa5-d483c5a6d2c1', 'f723cb59-251e-4681-b1fc-9fa92bd91e3f', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 212000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('079829e3-5634-404c-8b7f-6f7509adb490', '2d776fa9-226d-4f1d-9122-3e01d9acbc6a', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 213000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('75ed4301-ac02-40f2-9b8b-b2a7781bcbb3', '444bcbce-af1e-413a-b161-c2243fb37aa4', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 214000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c0e5eda7-810a-4d2d-9dfa-606b1f903e02', '542a3b95-f037-44d9-828e-22f513a4ab4d', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 215000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('472b784c-8d1b-45e3-b6fb-313f9e28e63a', 'd2b2d05f-8c82-4c3a-a467-0e7214243637', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 216000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('def0aabf-71d5-4592-a1d3-19544f441239', 'ec468482-0117-4a81-b8ac-4d4495d5eeaa', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 217000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9cb3ee51-ff10-4d13-8197-56698fa616fd', '45ef39b4-b30c-40de-b908-cab21d61f022', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 218000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('edf22286-e937-4222-bce9-20643a15c683', 'd5c21c92-5110-4bcb-921e-2f33139900bd', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 219000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('ea6c1cd3-49ea-4993-973c-34fdad7412bc', 'abe5f8c5-d2e9-4f1f-9629-0ab668fe16d7', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 220000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('34d10923-01f6-472f-b4b3-7a4f8e10807f', 'f62adb39-7b21-4869-825f-0167d9c66c81', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 221000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('518365e0-a1dd-439b-9377-2f68b6d2a4d2', '472dfb88-d454-4620-ad66-314741a3fa21', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 222000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f49c663d-9fc4-4634-9d29-21f39c0fd4b4', 'd7a58a4d-8263-4e28-b1cb-ce8e29fb72f8', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 223000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c92afef0-3a3c-4e5f-abc8-77e3058a042e', '0b757450-e9ca-4498-ac4d-a4f14dfbaa07', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 224000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('00d50a09-dd49-44a5-9e6e-77525b99aa3c', 'b03727b5-1c34-4c2e-8134-4f856cfa75d1', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 225000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4c51e8ec-abe5-47b7-97dd-74d059ad12b5', 'acc0cca2-76b2-43be-9eb8-b895d67d521f', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 226000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('7f7ddbe3-8d47-47bb-8dd0-2df122baa40d', 'a9d1acc1-90fe-440e-9b03-f838790511f9', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 227000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f20c5b51-0f68-467a-84b7-71bbe0428d7a', '258266c8-ca7d-4242-b663-281f6f3f2e00', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 228000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c2644c72-b8eb-467b-aa12-e2fb84a3437b', '05815537-6756-4668-8b9f-ba9a4f87674b', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 229000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('346ba280-3b66-4f68-8af1-736dd294929b', '06c991f3-930e-4a8f-928e-ab5a1126ea16', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 230000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('7aa02f5a-527a-4c1f-a34c-dfafe2dfb222', '0b2a0c80-82a4-4041-ba1a-2d44bcd72ce2', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 231000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('42df7f81-80c6-4061-bb96-148c4bc46a6a', '8b042c8e-43f3-4fab-9282-e8737e3b814f', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 232000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('33996f37-8cde-46c0-be60-1958a740ce3a', '65e7f186-ca38-4e7b-b832-fc7e24b4fb67', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 233000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('86c21fd6-fa4f-4696-b8f7-65ee36107d42', '5ec5e9f9-8c9b-431a-86a1-8e41d547b440', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 234000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('92c1f4dd-56ad-442a-a5fd-d3bcc98bb7bb', '3adf5bb8-8ac6-4102-92e0-552062fbec49', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 235000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3f087ad3-c7b4-40dc-b494-17305757cef4', '28089be5-74ee-47f2-8271-22caf947a935', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 236000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3ff64817-019f-4708-8d89-cb541493d4e2', 'e5191a79-2b82-42ba-bf80-b0a7c205c31c', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 237000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a06e0039-82e9-4f5f-bd38-f4a8db81a049', '87d57d77-a78a-43b0-92d5-44e50ddd6840', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 238000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('54484068-858b-40a3-8479-9579c5f3ed14', '93fe90cd-7581-4783-9fbe-b260e89472ee', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 239000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9d953b78-4ac3-4c98-82fe-18ade094725f', '49e20b12-461b-43dd-8e68-037bc70c2475', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 240000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8a2323c4-f3de-400d-a369-a45d9f0a3fc0', '407a2a35-988b-443f-a78d-9884eb8f6606', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 241000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('faef7ee1-2bb0-4afa-a6c2-b86c87bc352b', '935cc330-59de-47b4-afae-e0b199ebc51a', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 242000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('666bc415-abbd-4add-8b49-39e18359d8ee', 'dcb33bc4-aaa3-4d70-9c2c-7aedd7eda3bb', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 243000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('2ab317bd-b80f-4020-9c24-dc7e6a7bdad9', 'a7815e66-ddba-4926-8622-770b1de05d52', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 244000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fa4f414d-4423-4e98-9a5b-7be2ec1f8421', 'd6882e4d-d850-4241-911a-7b7c7748a802', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 245000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('72166951-52b1-4aca-aead-0a4fe6ce786d', '38236937-7a0c-4353-b041-f0b54c49bd1c', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 246000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('730cef7a-963a-432c-8907-02180d4d789a', 'cdd55212-97c5-4aa4-9245-0acafc046ce8', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 247000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('97d20f6a-9a06-458d-a94f-47a6616abab0', '15f9e9fc-46c6-44ab-81ed-94ddfd81e3d6', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 248000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f7f6bb4f-88f2-457e-b583-fed6be755b31', '2f8cddb3-3769-493f-a71a-c343d987bd14', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 249000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('4fdab538-3958-423c-b4d7-53328388af3f', '451134c1-1e12-48a0-80df-665636acc6a8', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 250000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('ad8911c5-0cb6-4b09-8dd7-f83d4426f468', '49d88006-79a5-4213-8e40-47f4404299cc', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 251000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fbc3f8fc-3961-4e13-b993-8f7504a75ea7', '965f11c9-db93-4ae7-ad31-63633df74006', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 252000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5aed517e-880e-4b26-94a6-1ef49976b35a', 'e40c3464-6c60-4074-9bd3-00f6cbe04764', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 253000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f118ff89-9ac6-4e4c-adcb-52daf185251e', 'e7f2c761-c221-4fe0-bf0f-669c3224fc21', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 254000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('aac1a25b-bfd4-44a6-954c-44668344b859', 'ac22baf5-b84a-4bc0-98f3-9b91b4223904', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 255000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('1cd8860d-9b08-49c7-8996-83e4f69eef9b', 'da0be7f3-d22d-4478-a443-7de487079a7c', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 256000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('b568fbaa-17fb-4b7f-9e3a-da41af8acf33', 'f96b74c6-9988-4e1d-a611-2d6a5365818d', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 257000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('bce73011-276a-4a0f-92cb-fe67625f357e', '2e7922ca-db94-4969-85ad-555bade4fed6', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 258000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a21674c8-7df9-42fb-946d-0b3c17b0cf30', '6acc2aae-d3ef-4cbd-be37-dc5205c0f5d4', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 259000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('3ba78fc9-c911-4ed3-9e79-41ba44d63335', '2dec2664-b2c8-4e71-9af8-3d9700162dba', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 260000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e39a6b75-860c-4b00-9b82-102c41b66fce', 'bf9882ba-75fa-4b99-b065-d3e67505a582', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 261000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('314a43e3-dc89-4f0b-9672-50e686560161', 'b407786f-dc68-4d79-87ee-6a46fc4db7c5', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 262000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('de4c61d1-afa0-4a56-9545-b24aa49df10e', '9b98ad9a-09d5-49df-b761-ab4f3152a295', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 263000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('cdf2d369-3a79-48be-b3e2-d60bd41cd91a', '96831ffd-587d-47e7-a089-b85ad0153e75', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 264000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('ce40a114-e562-419c-b5b9-77671dc4de8f', '3d915415-643d-4203-8399-2e54da6dbdde', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 265000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6655371a-5e29-4dc9-9be6-dee0ed95c905', 'b647f7f2-31b2-4511-b8db-e31d9808c146', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 266000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('7f8f4ca3-2aae-420c-b9cd-fdcff09f907d', '3f108730-84e8-4a72-bf04-02095fe5ea1f', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 267000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('dc16855d-a3a7-42e1-b6d7-b8c7a53a934c', 'ace03e9d-869f-4862-9713-3e3b3e7434c5', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 268000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a2fad846-5ad9-41df-8be7-4a7bc04ddecf', 'af1a26f9-508f-4324-8e7f-cb42d750bb61', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 269000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('fe219467-e47c-4c04-9634-10cbbcc82714', 'a3c86cf2-f3ea-4b36-ad27-4f08552ba734', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 270000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('dcb64bf4-8358-4957-8f80-4d4c70c76819', 'b4c3aa20-4afa-4c27-b9d3-5fe0993c6999', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 271000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('c5c65b76-2bb9-445e-8172-843ecc0a6cba', '4f4c1cc8-f3c5-415a-bdd1-41b3855a3cda', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 272000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d94c4480-d43f-4d3e-ba74-a473854c4237', 'e4fcbeb7-40a3-4795-a25a-f157d93d4d37', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 273000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e6b7ad77-fe30-4903-b7d5-3229e30386d1', 'c0ec912e-e938-4c80-bde1-485d401459c0', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 274000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('d5e5265c-295a-466c-9fa6-eab21b248ad9', '2ae9bc20-df8d-4ac7-bbe7-ef3db9569615', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 275000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('11de02e3-efea-49d2-bc00-28c51266f64d', '0319b002-8072-4774-98f9-091d712668de', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 277000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('a06201dd-2496-47f6-a37d-bc541d7cc148', '53b081da-e213-4199-b02e-69aa4bdfe032', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 278000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('8ef57613-d1fa-4a18-9bae-63be626506d1', '0dc9a49b-81b0-4ff6-a38e-020fc8489156', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 279000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6072f33d-c502-4fbb-b221-a8b0e683795b', '5c184858-67ad-45b9-b3dd-820a061ce6b7', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 280000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('099abe27-2100-47cf-bc90-e081a4b5cce2', '244e553b-ea0c-493e-8d10-70d020bc788f', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 281000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('6e679eb6-4538-4f41-846b-a2c95f35381d', 'dd57f596-b4e9-44b7-9206-d3dc9b8b9086', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 282000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('9b88b668-0c7e-4580-a3fc-cf3231903dd2', '1372087e-0378-4118-a541-13f7a373f3f0', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 283000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('79f9d519-202e-4b4e-9add-669abc7ba616', 'e2850941-3a65-4805-9587-da8b3b64ae7d', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 284000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('32d457ef-015c-47ec-9c74-c2ae61165fed', '6fc7c1e3-7b27-4bfe-8321-13a3c648c98a', 'cca564b8-65ba-456d-bf42-028ab646317f', 'ee1d4038-fa0e-4d61-846b-41fd5db3abbc', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 285000, '5-5-5 Sumiyoshi, Sumiyoshi-ku, Osaka 558-0045', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5ec2577c-01ed-4773-91d3-2ca1ee121b20', '12fddf70-1e99-48b4-b61e-2b713599da9a', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '29f60485-d53c-46ad-a4ec-05bee1d3fbf4', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 286000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('e16ed738-674f-48b5-aafc-729c360fc368', 'e4bc61da-ab8c-4c70-877f-a690f57f8e0e', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 287000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('f7832d49-020a-4f48-8e7e-b7d8ccb2d1fa', '9d0cc762-f1e2-4606-a36f-7034b1b4b711', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '6f5daf14-c709-4b71-913c-12f4576694ba', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 288000, '4-4-4 Minato, Minato-ku, Tokyo 105-0003', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('5c2111a1-ec89-4dce-96fd-91013f8e722f', '840c01d3-131e-485c-8a9f-9dbafbeeed72', '8b2755e5-4a81-4451-8418-9da357208117', '435ccc1c-7f9d-4997-9413-31c9e4947304', '8fe768d9-3081-4392-b861-cafa681426ca', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 290000, '6-6-6 Sakae, Naka-ku, Nagoya 460-0008', NULL, 'Penempatan melalui program magang reguler');
INSERT INTO penempatan_siswa (id, siswa_id, job_order_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, tanggal_kepulangan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja, evaluasi_bulanan, catatan_khusus) VALUES ('de515bf0-438f-47ae-af36-33bb5a766fda', '2eb6b1aa-60f4-40b1-836c-b7b542ef2abc', '1786d0bc-e5a2-41b7-8721-06bb6b43c124', '7b6db58f-7c6c-4086-9fc3-8a136b20561e', '03b62f8b-618c-43b1-b35e-0786dae6d31d', '2024-01-15', '2024-06-01', NULL, 'aktif', 'General Worker', 291000, '7-7-7 Gion, Higashiyama-ku, Kyoto 605-0001', NULL, 'Penempatan melalui program magang reguler');

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- ✅ FULL seed data import completed successfully!
-- 🎉 Dashboard Magang Jepang ready with 170 students!
