-- =====================================================
-- Stored Procedures untuk Operasi Kompleks
-- =====================================================

DELIMITER //

-- Procedure untuk mendapatkan statistik dashboard
CREATE PROCEDURE GetDashboardStatistics()
BEGIN
    SELECT 
        'Total Siswa' as metric,
        COUNT(*) as value,
        'siswa' as category
    FROM siswa
    
    UNION ALL
    
    SELECT 
        'Siswa Aktif di Jepang' as metric,
        COUNT(*) as value,
        'penempatan' as category
    FROM penempatan_siswa 
    WHERE status_penempatan = 'aktif'
    
    UNION ALL
    
    SELECT 
        'LPK Mitra Aktif' as metric,
        COUNT(*) as value,
        'lpk' as category
    FROM lpk_mitra 
    WHERE status = 'aktif'
    
    UNION ALL
    
    SELECT 
        'Perusahaan Penerima' as metric,
        COUNT(*) as value,
        'perusahaan' as category
    FROM perusahaan_penerima 
    WHERE status = 'aktif';
END //

-- Procedure untuk validasi kelengkapan dokumen siswa
CREATE PROCEDURE ValidateStudentDocuments(IN student_id INT)
BEGIN
    DECLARE total_required INT DEFAULT 0;
    DECLARE total_uploaded INT DEFAULT 0;
    DECLARE total_approved INT DEFAULT 0;
    
    -- Hitung total dokumen wajib
    SELECT COUNT(*) INTO total_required
    FROM jenis_dokumen 
    WHERE wajib = true AND status = 'aktif';
    
    -- Hitung total dokumen yang sudah diupload
    SELECT COUNT(*) INTO total_uploaded
    FROM dokumen_siswa ds
    JOIN jenis_dokumen jd ON ds.jenis_dokumen_id = jd.id
    WHERE ds.siswa_id = student_id AND jd.wajib = true;
    
    -- Hitung total dokumen yang sudah diapprove
    SELECT COUNT(*) INTO total_approved
    FROM dokumen_siswa ds
    JOIN jenis_dokumen jd ON ds.jenis_dokumen_id = jd.id
    WHERE ds.siswa_id = student_id 
    AND jd.wajib = true 
    AND ds.status_verifikasi = 'approved';
    
    SELECT 
        student_id as siswa_id,
        total_required,
        total_uploaded,
        total_approved,
        CASE 
            WHEN total_approved = total_required THEN 'LENGKAP'
            WHEN total_uploaded = 0 THEN 'BELUM_UPLOAD'
            ELSE 'TIDAK_LENGKAP'
        END as status_kelengkapan,
        ROUND((total_approved / total_required) * 100, 2) as persentase_kelengkapan;
END //

-- Procedure untuk membuat penempatan siswa
CREATE PROCEDURE CreateStudentPlacement(
    IN p_siswa_id INT,
    IN p_job_order_id INT,
    IN p_perusahaan_id INT,
    IN p_kumiai_id INT,
    IN p_posisi_kerja VARCHAR(100),
    IN p_gaji_pokok DECIMAL(15,2),
    IN p_tunjangan DECIMAL(15,2),
    OUT result_message VARCHAR(255)
)
BEGIN
    DECLARE student_exists INT DEFAULT 0;
    DECLARE job_exists INT DEFAULT 0;
    DECLARE already_placed INT DEFAULT 0;
    DECLARE job_quota_available INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET result_message = 'Error: Gagal membuat penempatan siswa';
    END;
    
    START TRANSACTION;
    
    -- Validasi siswa exists dan belum ditempatkan
    SELECT COUNT(*) INTO student_exists
    FROM siswa 
    WHERE id = p_siswa_id AND status_pendaftaran = 'approved';
    
    IF student_exists = 0 THEN
        SET result_message = 'Error: Siswa tidak ditemukan atau belum diapprove';
        ROLLBACK;
    ELSE
        -- Cek apakah siswa sudah ditempatkan
        SELECT COUNT(*) INTO already_placed
        FROM penempatan_siswa 
        WHERE siswa_id = p_siswa_id AND status_penempatan IN ('ditempatkan', 'berangkat', 'aktif');
        
        IF already_placed > 0 THEN
            SET result_message = 'Error: Siswa sudah memiliki penempatan aktif';
            ROLLBACK;
        ELSE
            -- Validasi job order dan kuota
            SELECT COUNT(*) INTO job_exists
            FROM job_order 
            WHERE id = p_job_order_id 
            AND status = 'open' 
            AND kuota_terisi < kuota_total;
            
            IF job_exists = 0 THEN
                SET result_message = 'Error: Job order tidak tersedia atau kuota penuh';
                ROLLBACK;
            ELSE
                -- Insert penempatan
                INSERT INTO penempatan_siswa (
                    siswa_id, job_order_id, perusahaan_id, kumiai_id,
                    posisi_kerja, gaji_pokok, tunjangan, status_penempatan
                ) VALUES (
                    p_siswa_id, p_job_order_id, p_perusahaan_id, p_kumiai_id,
                    p_posisi_kerja, p_gaji_pokok, p_tunjangan, 'ditempatkan'
                );
                
                -- Update kuota job order
                UPDATE job_order 
                SET kuota_terisi = kuota_terisi + 1
                WHERE id = p_job_order_id;
                
                COMMIT;
                SET result_message = 'Success: Penempatan siswa berhasil dibuat';
            END IF;
        END IF;
    END IF;
END //

-- Procedure untuk laporan bulanan
CREATE PROCEDURE GetMonthlyReport(
    IN report_year INT,
    IN report_month INT
)
BEGIN
    SELECT 
        'Pendaftaran Baru' as kategori,
        COUNT(*) as jumlah
    FROM siswa 
    WHERE YEAR(tanggal_daftar) = report_year 
    AND MONTH(tanggal_daftar) = report_month
    
    UNION ALL
    
    SELECT 
        'Keberangkatan' as kategori,
        COUNT(*) as jumlah
    FROM penempatan_siswa 
    WHERE tahun_keberangkatan = report_year 
    AND bulan_keberangkatan = report_month
    
    UNION ALL
    
    SELECT 
        'Kepulangan' as kategori,
        COUNT(*) as jumlah
    FROM penempatan_siswa 
    WHERE tahun_kepulangan = report_year 
    AND bulan_kepulangan = report_month;
END //

DELIMITER ;
