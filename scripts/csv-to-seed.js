/**
 * Script untuk mengkonversi CSV data awal ke format seed data Supabase
 * Dashboard Sistem Magang Jepang
 */

const fs = require('fs');
const path = require('path');

// Fungsi untuk membersihkan dan memformat data
function cleanData(value) {
    if (!value || value === '' || value === '#REF!' || value === '#N/A') {
        return null;
    }
    return value.toString().trim();
}

// Fungsi untuk mengkonversi tanggal Excel ke format ISO
function excelDateToISO(excelDate) {
    if (!excelDate || isNaN(excelDate)) return null;
    
    // Excel date serial number (1 = 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (excelDate - 2) * 24 * 60 * 60 * 1000);
    return date.toISOString().split('T')[0];
}

// Fungsi untuk generate UUID sederhana
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Mapping data dari CSV
function parseCSVData(csvContent) {
    const lines = csvContent.split('\n');
    const headers = lines[0].split(';');

    console.log('Headers found:', headers.length, 'headers');
    console.log('Total lines:', lines.length);

    const data = {
        kumiai: new Map(),
        perusahaan: new Map(),
        lpk: new Map(),
        siswa: [],
        penempatan: []
    };

    let processedCount = 0;
    let skippedCount = 0;

    // Process each row
    for (let i = 1; i < lines.length; i++) {
        const row = lines[i].split(';');
        if (row.length < 5) {
            skippedCount++;
            continue; // Skip incomplete rows
        }
        
        const rowData = {
            no: cleanData(row[0]),
            nama: cleanData(row[1]),
            nik: cleanData(row[2]),
            tglTest: cleanData(row[3]),
            namaKumiai: cleanData(row[4]),
            kodeKumiai: cleanData(row[5]),
            kumiaiJepang: cleanData(row[6]),
            perusahaanPenerima: cleanData(row[7]),
            gender: cleanData(row[8]),
            lpk: cleanData(row[9]),
            tempatLahir: cleanData(row[10]),
            tglLahir: cleanData(row[11]),
            email: cleanData(row[12]),
            alamat: cleanData(row[13]),
            noPaspor: cleanData(row[14]),
            noHp: cleanData(row[15]),
            perusahaanJepang: cleanData(row[16]),
            alamatPerusahaan: cleanData(row[17]),
            prefektur: cleanData(row[18]),
            jenisPekerjaan: cleanData(row[19]),
            perusahaanPengirim: cleanData(row[20]),
            rencanaKeberangkatan: cleanData(row[21]),
            tibaJepang: cleanData(row[22]),
            estimasiPulang: cleanData(row[23]),
            tglPulang: cleanData(row[24]),
            keterangan: cleanData(row[25]),
            ket2: cleanData(row[26]),
            kotaDaerah: cleanData(row[41])
        };
        
        // Skip if essential data is missing
        if (!rowData.nama || !rowData.nik) {
            skippedCount++;
            continue;
        }

        processedCount++;
        
        // Process Kumiai
        if (rowData.namaKumiai && rowData.kodeKumiai) {
            const kumiaiKey = rowData.kodeKumiai;
            if (!data.kumiai.has(kumiaiKey)) {
                data.kumiai.set(kumiaiKey, {
                    id: generateUUID(),
                    nama_kumiai: rowData.namaKumiai,
                    kode_kumiai: rowData.kodeKumiai,
                    alamat_jepang: 'Alamat akan diupdate',
                    kota_jepang: 'Tokyo',
                    prefektur: rowData.prefektur || 'Tokyo',
                    kontak_person: 'Kontak Person',
                    nomor_telepon: '+81-3-0000-0000',
                    status: 'aktif'
                });
            }
        }
        
        // Process Perusahaan
        if (rowData.perusahaanPenerima) {
            const perusahaanKey = rowData.perusahaanPenerima;
            if (!data.perusahaan.has(perusahaanKey)) {
                const kumiaiId = data.kumiai.get(rowData.kodeKumiai)?.id;
                if (kumiaiId) {
                    data.perusahaan.set(perusahaanKey, {
                        id: generateUUID(),
                        kumiai_id: kumiaiId,
                        nama_perusahaan: rowData.perusahaanPenerima,
                        alamat_jepang: rowData.alamatPerusahaan || 'Alamat akan diupdate',
                        kota_jepang: 'Tokyo',
                        prefektur: rowData.prefektur || 'Tokyo',
                        bidang_usaha: rowData.jenisPekerjaan || 'Manufacturing',
                        kontak_person: 'Kontak Person',
                        nomor_telepon: '+81-3-0000-0000',
                        status: 'aktif'
                    });
                }
            }
        }
        
        // Process LPK
        if (rowData.lpk) {
            const lpkKey = rowData.lpk;
            if (!data.lpk.has(lpkKey)) {
                data.lpk.set(lpkKey, {
                    id: generateUUID(),
                    nama_lpk: rowData.lpk,
                    alamat_lengkap: 'Alamat akan diupdate',
                    kota: rowData.kotaDaerah || 'Jakarta',
                    provinsi: 'Jawa Tengah',
                    nama_pimpinan: 'Pimpinan LPK',
                    kontak_person: 'Kontak Person',
                    nomor_telepon: '021-0000-0000',
                    status: 'aktif'
                });
            }
        }
        
        // Process Siswa
        const lpkId = data.lpk.get(rowData.lpk)?.id;
        if (lpkId) {
            const siswaData = {
                id: generateUUID(),
                lpk_id: lpkId,
                nama_lengkap: rowData.nama,
                nik: rowData.nik,
                tempat_lahir: rowData.tempatLahir || 'Jakarta',
                tanggal_lahir: excelDateToISO(rowData.tglLahir) || '1990-01-01',
                jenis_kelamin: rowData.gender === 'Laki - Laki' ? 'L' : 'P',
                agama: 'Islam',
                alamat_lengkap: rowData.alamat || 'Alamat akan diupdate',
                kelurahan: 'Kelurahan',
                kecamatan: 'Kecamatan',
                kota_kabupaten: rowData.kotaDaerah || 'Jakarta',
                provinsi: 'Jawa Tengah',
                nomor_hp: rowData.noHp || '081234567890',
                email: rowData.email,
                pendidikan_terakhir: 'SMA',
                nama_sekolah: 'SMA Negeri 1',
                tahun_lulus: 2018,
                status_pendaftaran: 'approved',
                tanggal_daftar: '2024-01-01'
            };
            
            data.siswa.push(siswaData);
            
            // Process Penempatan if applicable
            const perusahaanId = data.perusahaan.get(rowData.perusahaanPenerima)?.id;
            const kumiaiId = data.kumiai.get(rowData.kodeKumiai)?.id;
            
            if (perusahaanId && kumiaiId && rowData.keterangan === 'Kerja') {
                const penempatanData = {
                    id: generateUUID(),
                    siswa_id: siswaData.id,
                    perusahaan_id: perusahaanId,
                    kumiai_id: kumiaiId,
                    tanggal_penempatan: '2024-01-01',
                    tanggal_keberangkatan: excelDateToISO(rowData.tibaJepang),
                    status_penempatan: 'aktif',
                    posisi_kerja: rowData.jenisPekerjaan || 'General Worker',
                    gaji_aktual: 180000,
                    alamat_kerja: rowData.alamatPerusahaan || 'Alamat kerja'
                };
                
                data.penempatan.push(penempatanData);
            }
        }
    }

    console.log(`Processed: ${processedCount} rows, Skipped: ${skippedCount} rows`);

    return data;
}

// Generate SQL seed files
function generateSeedSQL(data) {
    let sql = `-- Seed data generated from CSV
-- Dashboard Sistem Magang Jepang
-- Generated on: ${new Date().toISOString()}

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

`;

    // Kumiai data
    sql += `-- Insert Kumiai data\n`;
    for (const kumiai of data.kumiai.values()) {
        sql += `INSERT INTO kumiai (id, nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person, nomor_telepon, status) VALUES 
('${kumiai.id}', '${kumiai.nama_kumiai.replace(/'/g, "''")}', '${kumiai.kode_kumiai}', '${kumiai.alamat_jepang}', '${kumiai.kota_jepang}', '${kumiai.prefektur}', '${kumiai.kontak_person}', '${kumiai.nomor_telepon}', '${kumiai.status}');\n`;
    }
    
    sql += `\n-- Insert Perusahaan data\n`;
    for (const perusahaan of data.perusahaan.values()) {
        sql += `INSERT INTO perusahaan_penerima (id, kumiai_id, nama_perusahaan, alamat_jepang, kota_jepang, prefektur, bidang_usaha, kontak_person, nomor_telepon, status) VALUES 
('${perusahaan.id}', '${perusahaan.kumiai_id}', '${perusahaan.nama_perusahaan.replace(/'/g, "''")}', '${perusahaan.alamat_jepang.replace(/'/g, "''")}', '${perusahaan.kota_jepang}', '${perusahaan.prefektur}', '${perusahaan.bidang_usaha}', '${perusahaan.kontak_person}', '${perusahaan.nomor_telepon}', '${perusahaan.status}');\n`;
    }
    
    sql += `\n-- Insert LPK data\n`;
    for (const lpk of data.lpk.values()) {
        sql += `INSERT INTO lpk_mitra (id, nama_lpk, alamat_lengkap, kota, provinsi, nama_pimpinan, kontak_person, nomor_telepon, status) VALUES 
('${lpk.id}', '${lpk.nama_lpk.replace(/'/g, "''")}', '${lpk.alamat_lengkap}', '${lpk.kota}', '${lpk.provinsi}', '${lpk.nama_pimpinan}', '${lpk.kontak_person}', '${lpk.nomor_telepon}', '${lpk.status}');\n`;
    }
    
    sql += `\n-- Insert Siswa data\n`;
    data.siswa.forEach(siswa => {
        sql += `INSERT INTO siswa (id, lpk_id, nama_lengkap, nik, tempat_lahir, tanggal_lahir, jenis_kelamin, agama, alamat_lengkap, kelurahan, kecamatan, kota_kabupaten, provinsi, nomor_hp, email, pendidikan_terakhir, nama_sekolah, tahun_lulus, status_pendaftaran, tanggal_daftar) VALUES 
('${siswa.id}', '${siswa.lpk_id}', '${siswa.nama_lengkap.replace(/'/g, "''")}', '${siswa.nik}', '${siswa.tempat_lahir}', '${siswa.tanggal_lahir}', '${siswa.jenis_kelamin}', '${siswa.agama}', '${siswa.alamat_lengkap.replace(/'/g, "''")}', '${siswa.kelurahan}', '${siswa.kecamatan}', '${siswa.kota_kabupaten}', '${siswa.provinsi}', '${siswa.nomor_hp}', ${siswa.email ? `'${siswa.email}'` : 'NULL'}, '${siswa.pendidikan_terakhir}', '${siswa.nama_sekolah}', ${siswa.tahun_lulus}, '${siswa.status_pendaftaran}', '${siswa.tanggal_daftar}');\n`;
    });
    
    sql += `\n-- Insert Penempatan data\n`;
    data.penempatan.forEach(penempatan => {
        sql += `INSERT INTO penempatan_siswa (id, siswa_id, perusahaan_id, kumiai_id, tanggal_penempatan, tanggal_keberangkatan, status_penempatan, posisi_kerja, gaji_aktual, alamat_kerja) VALUES 
('${penempatan.id}', '${penempatan.siswa_id}', '${penempatan.perusahaan_id}', '${penempatan.kumiai_id}', '${penempatan.tanggal_penempatan}', ${penempatan.tanggal_keberangkatan ? `'${penempatan.tanggal_keberangkatan}'` : 'NULL'}, '${penempatan.status_penempatan}', '${penempatan.posisi_kerja.replace(/'/g, "''")}', ${penempatan.gaji_aktual}, '${penempatan.alamat_kerja.replace(/'/g, "''")}');\n`;
    });
    
    sql += `\n-- Re-enable foreign key checks\nSET session_replication_role = DEFAULT;\n`;
    
    return sql;
}

// Main execution
async function main() {
    try {
        console.log('Reading CSV file...');
        const csvPath = path.join(__dirname, '..', 'seed data awal 2025.csv');
        const csvContent = fs.readFileSync(csvPath, 'utf-8');
        
        console.log('Parsing CSV data...');
        const data = parseCSVData(csvContent);
        
        console.log('Statistics:');
        console.log(`- Kumiai: ${data.kumiai.size}`);
        console.log(`- Perusahaan: ${data.perusahaan.size}`);
        console.log(`- LPK: ${data.lpk.size}`);
        console.log(`- Siswa: ${data.siswa.length}`);
        console.log(`- Penempatan: ${data.penempatan.length}`);
        
        console.log('Generating SQL...');
        const sql = generateSeedSQL(data);
        
        const outputPath = path.join(__dirname, 'seed-data-from-csv.sql');
        fs.writeFileSync(outputPath, sql);
        
        console.log(`Seed data generated successfully: ${outputPath}`);
        
        // Generate JSON for reference
        const jsonPath = path.join(__dirname, 'seed-data-from-csv.json');
        fs.writeFileSync(jsonPath, JSON.stringify({
            kumiai: Array.from(data.kumiai.values()),
            perusahaan: Array.from(data.perusahaan.values()),
            lpk: Array.from(data.lpk.values()),
            siswa: data.siswa,
            penempatan: data.penempatan
        }, null, 2));
        
        console.log(`JSON reference generated: ${jsonPath}`);
        
    } catch (error) {
        console.error('Error processing CSV:', error);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { parseCSVData, generateSeedSQL };
