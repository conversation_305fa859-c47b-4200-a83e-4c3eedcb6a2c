import { useState } from 'react'

interface ExportOptions {
  filename?: string
  includeHeaders?: boolean
  dateFormat?: string
}

interface UseExportReturn {
  isExporting: boolean
  exportToCSV: (data: any[], options?: ExportOptions) => void
  exportToJSON: (data: any[], options?: ExportOptions) => void
}

export function useExport(): UseExportReturn {
  const [isExporting, setIsExporting] = useState(false)

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD') => {
    const d = new Date(date)
    if (isNaN(d.getTime())) return date
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    
    switch (format) {
      case 'DD/MM/YYYY':
        return `${day}/${month}/${year}`
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`
      case 'YYYY-MM-DD':
      default:
        return `${year}-${month}-${day}`
    }
  }

  const flattenObject = (obj: any, prefix = ''): any => {
    const flattened: any = {}
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key]
        const newKey = prefix ? `${prefix}.${key}` : key
        
        if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
          Object.assign(flattened, flattenObject(value, newKey))
        } else if (Array.isArray(value)) {
          flattened[newKey] = value.join(', ')
        } else if (value instanceof Date) {
          flattened[newKey] = formatDate(value)
        } else {
          flattened[newKey] = value
        }
      }
    }
    
    return flattened
  }

  const exportToCSV = async (data: any[], options: ExportOptions = {}) => {
    setIsExporting(true)
    
    try {
      const {
        filename = `export_${new Date().toISOString().split('T')[0]}.csv`,
        includeHeaders = true,
        dateFormat = 'YYYY-MM-DD'
      } = options

      if (data.length === 0) {
        throw new Error('No data to export')
      }

      // Flatten nested objects
      const flattenedData = data.map(item => flattenObject(item))
      
      // Get all unique keys
      const allKeys = Array.from(
        new Set(flattenedData.flatMap(item => Object.keys(item)))
      )

      // Create CSV content
      let csvContent = ''
      
      // Add headers
      if (includeHeaders) {
        csvContent += allKeys.map(key => `"${key}"`).join(',') + '\n'
      }
      
      // Add data rows
      flattenedData.forEach(item => {
        const row = allKeys.map(key => {
          let value = item[key] || ''
          
          // Handle dates
          if (value && typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}/)) {
            value = formatDate(value, dateFormat)
          }
          
          // Escape quotes and wrap in quotes
          value = String(value).replace(/"/g, '""')
          return `"${value}"`
        }).join(',')
        
        csvContent += row + '\n'
      })

      downloadFile(csvContent, filename, 'text/csv;charset=utf-8;')
    } catch (error) {
      console.error('Export to CSV failed:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }

  const exportToJSON = async (data: any[], options: ExportOptions = {}) => {
    setIsExporting(true)
    
    try {
      const {
        filename = `export_${new Date().toISOString().split('T')[0]}.json`
      } = options

      if (data.length === 0) {
        throw new Error('No data to export')
      }

      const jsonContent = JSON.stringify(data, null, 2)
      downloadFile(jsonContent, filename, 'application/json;charset=utf-8;')
    } catch (error) {
      console.error('Export to JSON failed:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }

  return {
    isExporting,
    exportToCSV,
    exportToJSON
  }
}
