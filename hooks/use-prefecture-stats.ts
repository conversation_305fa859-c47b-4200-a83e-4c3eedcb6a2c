import { useMemo } from 'react'

interface PlacementData {
  id: string
  siswa_id: string
  siswa?: {
    nama_lengkap: string
    jenis_kelamin: string
  }
  perusahaan_penerima?: {
    nama_perusahaan: string
    alamat_jepang: string
    kota_jepang: string
    prefektur: string
    bidang_usaha: string
  }
  kumiai?: {
    nama_kumiai: string
    kode_kumiai: string
  }
  posisi_kerja: string
  gaji_aktual: number
  status_penempatan: string
  tanggal_penempatan: string
}

interface PrefectureStats {
  prefecture: string
  totalStudents: number
  companies: string[]
  averageSalary: number
  genderDistribution: {
    male: number
    female: number
  }
  statusDistribution: {
    aktif: number
    berangkat: number
    selesai: number
    other: number
  }
  topCompanies: Array<{
    name: string
    count: number
  }>
  placements: PlacementData[]
}

export function usePrefectureStats(data: PlacementData[]) {
  const prefectureStats = useMemo(() => {
    const grouped = data.reduce((acc, placement) => {
      const prefecture = placement.perusahaan_penerima?.prefektur
      if (!prefecture) return acc

      if (!acc[prefecture]) {
        acc[prefecture] = {
          prefecture,
          totalStudents: 0,
          companies: [],
          averageSalary: 0,
          genderDistribution: { male: 0, female: 0 },
          statusDistribution: { aktif: 0, berangkat: 0, selesai: 0, other: 0 },
          topCompanies: [],
          placements: []
        }
      }

      const prefData = acc[prefecture]
      
      // Add placement
      prefData.placements.push(placement)
      prefData.totalStudents += 1

      // Track companies
      const companyName = placement.perusahaan_penerima?.nama_perusahaan
      if (companyName && !prefData.companies.includes(companyName)) {
        prefData.companies.push(companyName)
      }

      // Gender distribution
      const gender = placement.siswa?.jenis_kelamin
      if (gender === 'L') {
        prefData.genderDistribution.male += 1
      } else if (gender === 'P') {
        prefData.genderDistribution.female += 1
      }

      // Status distribution
      const status = placement.status_penempatan?.toLowerCase()
      if (status === 'aktif') {
        prefData.statusDistribution.aktif += 1
      } else if (status === 'berangkat') {
        prefData.statusDistribution.berangkat += 1
      } else if (status === 'selesai') {
        prefData.statusDistribution.selesai += 1
      } else {
        prefData.statusDistribution.other += 1
      }

      return acc
    }, {} as Record<string, PrefectureStats>)

    // Calculate derived statistics
    Object.values(grouped).forEach((prefData) => {
      // Average salary
      const totalSalary = prefData.placements.reduce((sum, p) => sum + (p.gaji_aktual || 0), 0)
      prefData.averageSalary = prefData.totalStudents > 0 ? Math.round(totalSalary / prefData.totalStudents) : 0

      // Top companies
      const companyCount = prefData.placements.reduce((acc, p) => {
        const company = p.perusahaan_penerima?.nama_perusahaan
        if (company) {
          acc[company] = (acc[company] || 0) + 1
        }
        return acc
      }, {} as Record<string, number>)

      prefData.topCompanies = Object.entries(companyCount)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5)
    })

    return Object.values(grouped).sort((a, b) => b.totalStudents - a.totalStudents)
  }, [data])

  const overallStats = useMemo(() => {
    const total = prefectureStats.reduce((acc, pref) => {
      acc.totalStudents += pref.totalStudents
      acc.totalPrefectures += 1
      acc.totalCompanies += pref.companies.length
      acc.totalSalary += pref.averageSalary * pref.totalStudents
      
      acc.genderDistribution.male += pref.genderDistribution.male
      acc.genderDistribution.female += pref.genderDistribution.female
      
      acc.statusDistribution.aktif += pref.statusDistribution.aktif
      acc.statusDistribution.berangkat += pref.statusDistribution.berangkat
      acc.statusDistribution.selesai += pref.statusDistribution.selesai
      acc.statusDistribution.other += pref.statusDistribution.other

      return acc
    }, {
      totalStudents: 0,
      totalPrefectures: 0,
      totalCompanies: 0,
      totalSalary: 0,
      averageSalary: 0,
      genderDistribution: { male: 0, female: 0 },
      statusDistribution: { aktif: 0, berangkat: 0, selesai: 0, other: 0 }
    })

    total.averageSalary = total.totalStudents > 0 ? Math.round(total.totalSalary / total.totalStudents) : 0

    return total
  }, [prefectureStats])

  const topPrefectures = useMemo(() => {
    return prefectureStats.slice(0, 10)
  }, [prefectureStats])

  const chartData = useMemo(() => {
    return {
      prefectureDistribution: prefectureStats.slice(0, 8).map(pref => ({
        name: pref.prefecture,
        value: pref.totalStudents,
        fill: getColorForIndex(prefectureStats.indexOf(pref))
      })),
      
      genderDistribution: [
        { name: 'Laki-laki', value: overallStats.genderDistribution.male, fill: '#FFA500' },
        { name: 'Perempuan', value: overallStats.genderDistribution.female, fill: '#800000' }
      ],
      
      statusDistribution: [
        { name: 'Aktif', value: overallStats.statusDistribution.aktif, fill: '#22c55e' },
        { name: 'Berangkat', value: overallStats.statusDistribution.berangkat, fill: '#3b82f6' },
        { name: 'Selesai', value: overallStats.statusDistribution.selesai, fill: '#6b7280' },
        { name: 'Lainnya', value: overallStats.statusDistribution.other, fill: '#f59e0b' }
      ],

      salaryByPrefecture: prefectureStats.slice(0, 10).map(pref => ({
        prefecture: pref.prefecture,
        averageSalary: pref.averageSalary,
        students: pref.totalStudents
      }))
    }
  }, [prefectureStats, overallStats])

  return {
    prefectureStats,
    overallStats,
    topPrefectures,
    chartData
  }
}

function getColorForIndex(index: number): string {
  const colors = [
    '#FFA500', // Orange
    '#800000', // Maroon
    '#3b82f6', // Blue
    '#22c55e', // Green
    '#8b5cf6', // Purple
    '#f59e0b', // Amber
    '#ef4444', // Red
    '#06b6d4', // Cyan
    '#84cc16', // Lime
    '#f97316', // Orange-600
    '#ec4899', // Pink
    '#6366f1'  // Indigo
  ]
  return colors[index % colors.length]
}
