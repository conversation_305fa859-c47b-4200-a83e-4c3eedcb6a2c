"use client"

import { useState } from "react"
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Search,
  Filter,
  MoreHorizontal,
  File,
  Shield,
  Globe,
  CheckCircle,
  XCircle,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

// Sample documents data
const sampleDocuments = [
  {
    id: 1,
    namaDokumen: "Kartu Tanda Penduduk (KTP)",
    tipeDokumen: "Administrasi",
    statusWajib: true,
    deskripsi: "KTP yang masih berlaku",
  },
  {
    id: 2,
    namaDokumen: "Kartu Keluarga (KK)",
    tipeDokumen: "Administrasi",
    statusWajib: true,
    deskripsi: "Kartu Keluarga asli",
  },
  {
    id: 3,
    namaDokumen: "Ijazah Terakhir",
    tipeDokumen: "Administrasi",
    statusWajib: true,
    deskripsi: "Ijazah pendidikan terakhir yang telah dilegalisir",
  },
  {
    id: 4,
    namaDokumen: "Sertifikat Bahasa Jepang",
    tipeDokumen: "Administrasi",
    statusWajib: false,
    deskripsi: "Sertifikat JLPT N4 atau N5 (jika ada)",
  },
  {
    id: 5,
    namaDokumen: "Surat Keterangan Sehat",
    tipeDokumen: "Pemberkasan",
    statusWajib: true,
    deskripsi: "Surat keterangan sehat dari dokter",
  },
  {
    id: 6,
    namaDokumen: "Surat Keterangan Bebas Narkoba",
    tipeDokumen: "Pemberkasan",
    statusWajib: true,
    deskripsi: "Surat keterangan bebas narkoba dari BNN",
  },
  {
    id: 7,
    namaDokumen: "Surat Keterangan Catatan Kepolisian (SKCK)",
    tipeDokumen: "Pemberkasan",
    statusWajib: true,
    deskripsi: "SKCK yang masih berlaku",
  },
  {
    id: 8,
    namaDokumen: "Paspor",
    tipeDokumen: "VISA",
    statusWajib: true,
    deskripsi: "Paspor dengan masa berlaku minimal 18 bulan",
  },
  {
    id: 9,
    namaDokumen: "Foto 4x6 Background Putih",
    tipeDokumen: "VISA",
    statusWajib: true,
    deskripsi: "Foto formal dengan background putih (10 lembar)",
  },
  {
    id: 10,
    namaDokumen: "Surat Sponsor",
    tipeDokumen: "VISA",
    statusWajib: false,
    deskripsi: "Surat sponsor dari perusahaan Jepang (jika diperlukan)",
  },
]

const tipeOptions = ["Semua Tipe", "Administrasi", "Pemberkasan", "VISA"]

const getTipeBadge = (tipe: string) => {
  const tipeConfig = {
    Administrasi: { color: "bg-blue-100 text-blue-800 border-blue-200", icon: File },
    Pemberkasan: { color: "bg-orange-100 text-orange-800 border-orange-200", icon: Shield },
    VISA: { color: "bg-maroon-100 text-maroon-800 border-maroon-200", icon: Globe },
  }

  const config = tipeConfig[tipe as keyof typeof tipeConfig] || tipeConfig.Administrasi
  const IconComponent = config.icon

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {tipe}
    </Badge>
  )
}

const getStatusWajibBadge = (wajib: boolean) => {
  return wajib ? (
    <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-100">
      <CheckCircle className="w-3 h-3 mr-1" />
      Wajib
    </Badge>
  ) : (
    <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-200">
      <XCircle className="w-3 h-3 mr-1" />
      Opsional
    </Badge>
  )
}

export default function DokumenKelengkapan() {
  const [searchTerm, setSearchTerm] = useState("")
  const [tipeFilter, setTipeFilter] = useState("Semua Tipe")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingDocument, setEditingDocument] = useState<any>(null)

  // Form state for add/edit
  const [formData, setFormData] = useState({
    namaDokumen: "",
    tipeDokumen: "Administrasi",
    statusWajib: true,
    deskripsi: "",
  })

  // Filter documents
  const filteredDocuments = sampleDocuments.filter((doc) => {
    const matchesSearch = doc.namaDokumen.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTipe = tipeFilter === "Semua Tipe" || doc.tipeDokumen === tipeFilter
    return matchesSearch && matchesTipe
  })

  const handleAddDocument = () => {
    console.log("Adding document:", formData)
    setIsAddDialogOpen(false)
    setFormData({
      namaDokumen: "",
      tipeDokumen: "Administrasi",
      statusWajib: true,
      deskripsi: "",
    })
  }

  const handleEditDocument = (doc: any) => {
    setEditingDocument(doc)
    setFormData({
      namaDokumen: doc.namaDokumen,
      tipeDokumen: doc.tipeDokumen,
      statusWajib: doc.statusWajib,
      deskripsi: doc.deskripsi,
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdateDocument = () => {
    console.log("Updating document:", editingDocument.id, formData)
    setIsEditDialogOpen(false)
    setEditingDocument(null)
    setFormData({
      namaDokumen: "",
      tipeDokumen: "Administrasi",
      statusWajib: true,
      deskripsi: "",
    })
  }

  const handleDeleteDocument = (id: number) => {
    console.log("Delete document with id:", id)
  }

  const resetForm = () => {
    setFormData({
      namaDokumen: "",
      tipeDokumen: "Administrasi",
      statusWajib: true,
      deskripsi: "",
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Dokumen Kelengkapan Siswa</h1>
                <p className="text-gray-600 mt-1">Kelola persyaratan dokumen untuk magang ke Jepang</p>
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => (window.location.href = "/dokumen/siswa")}
                className="border-orange-300 text-orange-600 hover:bg-orange-50"
              >
                <Users className="h-4 w-4 mr-2" />
                Kelengkapan Siswa
              </Button>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Dokumen
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-orange-600" />
                      Tambah Dokumen Baru
                    </DialogTitle>
                    <DialogDescription>Tambahkan dokumen persyaratan baru untuk siswa magang.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="nama-dokumen">Nama Dokumen *</Label>
                      <Input
                        id="nama-dokumen"
                        placeholder="Masukkan nama dokumen"
                        value={formData.namaDokumen}
                        onChange={(e) => setFormData({ ...formData, namaDokumen: e.target.value })}
                        className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="tipe-dokumen">Tipe Dokumen *</Label>
                      <Select
                        value={formData.tipeDokumen}
                        onValueChange={(value) => setFormData({ ...formData, tipeDokumen: value })}
                      >
                        <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Administrasi">Administrasi</SelectItem>
                          <SelectItem value="Pemberkasan">Pemberkasan</SelectItem>
                          <SelectItem value="VISA">VISA</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="status-wajib"
                        checked={formData.statusWajib}
                        onCheckedChange={(checked) => setFormData({ ...formData, statusWajib: checked })}
                      />
                      <Label htmlFor="status-wajib">Dokumen Wajib</Label>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="deskripsi">Deskripsi</Label>
                      <Input
                        id="deskripsi"
                        placeholder="Deskripsi dokumen (opsional)"
                        value={formData.deskripsi}
                        onChange={(e) => setFormData({ ...formData, deskripsi: e.target.value })}
                        className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Batal
                    </Button>
                    <Button
                      onClick={handleAddDocument}
                      className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
                      disabled={!formData.namaDokumen.trim()}
                    >
                      Tambah Dokumen
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <File className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Administrasi</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sampleDocuments.filter((doc) => doc.tipeDokumen === "Administrasi").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg mr-3">
                  <Shield className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Pemberkasan</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sampleDocuments.filter((doc) => doc.tipeDokumen === "Pemberkasan").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-maroon-100 rounded-lg mr-3">
                  <Globe className="h-5 w-5 text-maroon-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">VISA</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sampleDocuments.filter((doc) => doc.tipeDokumen === "VISA").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg mr-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Dokumen Wajib</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sampleDocuments.filter((doc) => doc.statusWajib).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari nama dokumen..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              {/* Type Filter */}
              <Select value={tipeFilter} onValueChange={setTipeFilter}>
                <SelectTrigger className="w-full sm:w-[200px] h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {tipeOptions.map((tipe) => (
                    <SelectItem key={tipe} value={tipe}>
                      {tipe}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
        </Card>

        {/* Documents Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Daftar Dokumen</CardTitle>
                <CardDescription>Menampilkan {filteredDocuments.length} dokumen</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                    <TableHead className="min-w-[300px] font-semibold">Nama Dokumen</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Tipe Dokumen</TableHead>
                    <TableHead className="text-center font-semibold">Status Wajib</TableHead>
                    <TableHead className="min-w-[250px] font-semibold">Deskripsi</TableHead>
                    <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDocuments.map((document, index) => (
                    <TableRow key={document.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell className="font-medium">{document.namaDokumen}</TableCell>
                      <TableCell>{getTipeBadge(document.tipeDokumen)}</TableCell>
                      <TableCell className="text-center">{getStatusWajibBadge(document.statusWajib)}</TableCell>
                      <TableCell className="text-sm text-gray-600">{document.deskripsi}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleEditDocument(document)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Hapus
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Hapus Dokumen</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Apakah Anda yakin ingin menghapus dokumen "{document.namaDokumen}"? Tindakan ini
                                    tidak dapat dibatalkan.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Batal</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteDocument(document.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Hapus
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {filteredDocuments.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada dokumen ditemukan</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || tipeFilter !== "Semua Tipe"
                    ? "Tidak ada dokumen yang sesuai dengan filter yang dipilih."
                    : "Belum ada dokumen yang ditambahkan."}
                </p>
                {!searchTerm && tipeFilter === "Semua Tipe" && (
                  <Button
                    className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
                    onClick={() => setIsAddDialogOpen(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Dokumen Pertama
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Edit className="h-5 w-5 mr-2 text-orange-600" />
                Edit Dokumen
              </DialogTitle>
              <DialogDescription>Ubah informasi dokumen persyaratan.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-nama-dokumen">Nama Dokumen *</Label>
                <Input
                  id="edit-nama-dokumen"
                  placeholder="Masukkan nama dokumen"
                  value={formData.namaDokumen}
                  onChange={(e) => setFormData({ ...formData, namaDokumen: e.target.value })}
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-tipe-dokumen">Tipe Dokumen *</Label>
                <Select
                  value={formData.tipeDokumen}
                  onValueChange={(value) => setFormData({ ...formData, tipeDokumen: value })}
                >
                  <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Administrasi">Administrasi</SelectItem>
                    <SelectItem value="Pemberkasan">Pemberkasan</SelectItem>
                    <SelectItem value="VISA">VISA</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-status-wajib"
                  checked={formData.statusWajib}
                  onCheckedChange={(checked) => setFormData({ ...formData, statusWajib: checked })}
                />
                <Label htmlFor="edit-status-wajib">Dokumen Wajib</Label>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-deskripsi">Deskripsi</Label>
                <Input
                  id="edit-deskripsi"
                  placeholder="Deskripsi dokumen (opsional)"
                  value={formData.deskripsi}
                  onChange={(e) => setFormData({ ...formData, deskripsi: e.target.value })}
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Batal
              </Button>
              <Button
                onClick={handleUpdateDocument}
                className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
                disabled={!formData.namaDokumen.trim()}
              >
                Simpan Perubahan
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
