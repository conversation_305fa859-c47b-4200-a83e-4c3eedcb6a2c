"use client"

import { useState } from "react"
import { Search, Eye, Users, FileText, Filter, ArrowLeft } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Sample students data with document completion status
const studentsData = [
  {
    id: 1,
    namaLengkap: "Ahmad Rizki Prata<PERSON>",
    lpkMitra: "LPK Sukses Mandiri Jakarta",
    kotaKabupaten: "Jakarta Selatan",
    totalDokumen: 10,
    dokumenValid: 3,
    dokumenPending: 1,
    dokumenInvalid: 1,
    dokumenBelum: 5,
    completionPercentage: 30,
  },
  {
    id: 2,
    namaLengkap: "Siti Nurhaliza",
    lpkMitra: "LPK Maju Bersama Bandung",
    kotaKabupaten: "Bandung",
    totalDokumen: 10,
    dokumenValid: 8,
    dokumenPending: 1,
    dokumenInvalid: 0,
    dokumenBelum: 1,
    completionPercentage: 80,
  },
  {
    id: 3,
    namaLengkap: "Budi Santoso",
    lpkMitra: "LPK Harapan Bangsa Surabaya",
    kotaKabupaten: "Surabaya",
    totalDokumen: 10,
    dokumenValid: 10,
    dokumenPending: 0,
    dokumenInvalid: 0,
    dokumenBelum: 0,
    completionPercentage: 100,
  },
  {
    id: 4,
    namaLengkap: "Dewi Sartika",
    lpkMitra: "LPK Karya Utama Medan",
    kotaKabupaten: "Medan",
    totalDokumen: 10,
    dokumenValid: 1,
    dokumenPending: 2,
    dokumenInvalid: 2,
    dokumenBelum: 5,
    completionPercentage: 10,
  },
  {
    id: 5,
    namaLengkap: "Andi Wijaya",
    lpkMitra: "LPK Nusantara Makassar",
    kotaKabupaten: "Makassar",
    totalDokumen: 10,
    dokumenValid: 6,
    dokumenPending: 2,
    dokumenInvalid: 1,
    dokumenBelum: 1,
    completionPercentage: 60,
  },
  {
    id: 6,
    namaLengkap: "Rini Astuti",
    lpkMitra: "LPK Bina Prestasi Yogyakarta",
    kotaKabupaten: "Yogyakarta",
    totalDokumen: 10,
    dokumenValid: 9,
    dokumenPending: 1,
    dokumenInvalid: 0,
    dokumenBelum: 0,
    completionPercentage: 90,
  },
]

const getCompletionBadge = (percentage: number) => {
  if (percentage === 100) {
    return <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-100">Lengkap</Badge>
  } else if (percentage >= 70) {
    return <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100">Hampir Lengkap</Badge>
  } else if (percentage >= 30) {
    return <Badge className="bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-100">Sebagian</Badge>
  } else {
    return <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-100">Belum Lengkap</Badge>
  }
}

export default function DaftarKelengkapanDokumen() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("Semua Status")

  const filteredStudents = studentsData.filter((student) => {
    const matchesSearch =
      student.namaLengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lpkMitra.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.kotaKabupaten.toLowerCase().includes(searchTerm.toLowerCase())

    let matchesStatus = true
    if (statusFilter === "Lengkap") matchesStatus = student.completionPercentage === 100
    else if (statusFilter === "Belum Lengkap") matchesStatus = student.completionPercentage < 100
    else if (statusFilter === "Hampir Lengkap")
      matchesStatus = student.completionPercentage >= 70 && student.completionPercentage < 100
    else if (statusFilter === "Sebagian")
      matchesStatus = student.completionPercentage >= 30 && student.completionPercentage < 70

    return matchesSearch && matchesStatus
  })

  // Calculate statistics
  const totalStudents = studentsData.length
  const completedStudents = studentsData.filter((s) => s.completionPercentage === 100).length
  const almostCompletedStudents = studentsData.filter(
    (s) => s.completionPercentage >= 70 && s.completionPercentage < 100,
  ).length
  const partialStudents = studentsData.filter((s) => s.completionPercentage >= 30 && s.completionPercentage < 70).length
  const incompleteStudents = studentsData.filter((s) => s.completionPercentage < 30).length

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <a href="/dokumen" className="text-gray-500 hover:text-gray-700">
                  Dokumen
                </a>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Kelengkapan Dokumen Siswa</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => (window.location.href = "/dokumen")}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Kelengkapan Dokumen Siswa</h1>
                  <p className="text-gray-600 mt-1">Monitor kelengkapan dokumen semua siswa</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg mr-3">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Dokumen Lengkap</p>
                  <p className="text-2xl font-bold text-gray-900">{completedStudents}</p>
                  <p className="text-xs text-gray-500">dari {totalStudents} siswa</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Hampir Lengkap</p>
                  <p className="text-2xl font-bold text-gray-900">{almostCompletedStudents}</p>
                  <p className="text-xs text-gray-500">≥70% selesai</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg mr-3">
                  <Users className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Sebagian</p>
                  <p className="text-2xl font-bold text-gray-900">{partialStudents}</p>
                  <p className="text-xs text-gray-500">30-69% selesai</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg mr-3">
                  <Users className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Belum Lengkap</p>
                  <p className="text-2xl font-bold text-gray-900">{incompleteStudents}</p>
                  <p className="text-xs text-gray-500">{"<30% selesai"}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari nama siswa, LPK, atau kota..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[200px] h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Semua Status">Semua Status</SelectItem>
                  <SelectItem value="Lengkap">Lengkap (100%)</SelectItem>
                  <SelectItem value="Hampir Lengkap">Hampir Lengkap (≥70%)</SelectItem>
                  <SelectItem value="Sebagian">Sebagian (30-69%)</SelectItem>
                  <SelectItem value="Belum Lengkap">Belum Lengkap ({"<30%"})</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
        </Card>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Daftar Siswa</CardTitle>
                <CardDescription>
                  Menampilkan {filteredStudents.length} dari {totalStudents} siswa
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Nama Siswa</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">LPK Mitra</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Kota/Kabupaten</TableHead>
                    <TableHead className="text-center font-semibold">Progress Dokumen</TableHead>
                    <TableHead className="text-center font-semibold">Status Kelengkapan</TableHead>
                    <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.map((student, index) => (
                    <TableRow key={student.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell className="font-medium">{student.namaLengkap}</TableCell>
                      <TableCell className="text-sm">{student.lpkMitra}</TableCell>
                      <TableCell>{student.kotaKabupaten}</TableCell>
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center">
                          <span className="text-sm font-medium mb-1">
                            {student.dokumenValid}/{student.totalDokumen}
                          </span>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-orange-500 to-maroon-600 h-2 rounded-full"
                              style={{ width: `${student.completionPercentage}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 mt-1">{student.completionPercentage}%</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">{getCompletionBadge(student.completionPercentage)}</TableCell>
                      <TableCell className="text-center">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => (window.location.href = `/dokumen/siswa/${student.id}`)}
                          className="border-gray-300 bg-transparent hover:bg-gray-50"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Lihat
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {filteredStudents.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada siswa ditemukan</h3>
                <p className="text-gray-500">
                  {searchTerm || statusFilter !== "Semua Status"
                    ? "Tidak ada siswa yang sesuai dengan filter yang dipilih."
                    : "Belum ada siswa yang terdaftar."}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
