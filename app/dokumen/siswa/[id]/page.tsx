"use client"

import { useState } from "react"
import {
  ArrowLeft,
  Upload,
  Eye,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  MapPin,
  Building2,
  BadgeIcon as IdCard,
  File,
  Shield,
  Globe,
  AlertCircle,
  Check,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Sample student data
const studentData = {
  id: 1,
  namaLengkap: "Ahmad Rizki Pratama",
  gender: "Laki-laki",
  nik: "3201234567890123",
  lpkMitra: "LPK Sukses Mandiri Jakarta",
  kotaKabupaten: "Jakarta Selatan",
  jobOrder: "Toyota Motor Corporation - Assembly Line Operator",
}

// Sample documents with upload status
const documentsData = [
  {
    id: 1,
    namaDokumen: "Kartu Tanda Penduduk (KTP)",
    tipeDokumen: "Administrasi",
    statusUpload: "Valid",
    tanggalUpload: "2024-03-15",
    fileName: "KTP_Ahmad_Rizki.pdf",
    catatan: "Dokumen valid dan sesuai",
    wajib: true,
  },
  {
    id: 2,
    namaDokumen: "Kartu Keluarga (KK)",
    tipeDokumen: "Administrasi",
    statusUpload: "Valid",
    tanggalUpload: "2024-03-15",
    fileName: "KK_Ahmad_Rizki.pdf",
    catatan: "Dokumen lengkap",
    wajib: true,
  },
  {
    id: 3,
    namaDokumen: "Ijazah Terakhir",
    tipeDokumen: "Administrasi",
    statusUpload: "Dalam Verifikasi",
    tanggalUpload: "2024-03-20",
    fileName: "Ijazah_Ahmad_Rizki.pdf",
    catatan: "Sedang dalam proses verifikasi",
    wajib: true,
  },
  {
    id: 4,
    namaDokumen: "Sertifikat Bahasa Jepang",
    tipeDokumen: "Administrasi",
    statusUpload: "Tidak Valid",
    tanggalUpload: "2024-03-18",
    fileName: "JLPT_Ahmad_Rizki.pdf",
    catatan: "Sertifikat sudah expired, mohon upload yang terbaru",
    wajib: false,
  },
  {
    id: 5,
    namaDokumen: "Surat Keterangan Sehat",
    tipeDokumen: "Pemberkasan",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: true,
  },
  {
    id: 6,
    namaDokumen: "Surat Keterangan Bebas Narkoba",
    tipeDokumen: "Pemberkasan",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: true,
  },
  {
    id: 7,
    namaDokumen: "Surat Keterangan Catatan Kepolisian (SKCK)",
    tipeDokumen: "Pemberkasan",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: true,
  },
  {
    id: 8,
    namaDokumen: "Paspor",
    tipeDokumen: "VISA",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: true,
  },
  {
    id: 9,
    namaDokumen: "Foto 4x6 Background Putih",
    tipeDokumen: "VISA",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: true,
  },
  {
    id: 10,
    namaDokumen: "Surat Sponsor",
    tipeDokumen: "VISA",
    statusUpload: "Belum Upload",
    tanggalUpload: null,
    fileName: null,
    catatan: null,
    wajib: false,
  },
]

const getTipeBadge = (tipe: string) => {
  const tipeConfig = {
    Administrasi: { color: "bg-blue-100 text-blue-800 border-blue-200", icon: File },
    Pemberkasan: { color: "bg-orange-100 text-orange-800 border-orange-200", icon: Shield },
    VISA: { color: "bg-maroon-100 text-maroon-800 border-maroon-200", icon: Globe },
  }

  const config = tipeConfig[tipe as keyof typeof tipeConfig] || tipeConfig.Administrasi
  const IconComponent = config.icon

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {tipe}
    </Badge>
  )
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    "Belum Upload": {
      color: "bg-gray-100 text-gray-600 border-gray-200",
      icon: AlertCircle,
      dot: "bg-gray-500",
    },
    "Dalam Verifikasi": {
      color: "bg-blue-100 text-blue-800 border-blue-200",
      icon: Clock,
      dot: "bg-blue-500",
    },
    Valid: {
      color: "bg-green-100 text-green-800 border-green-200",
      icon: CheckCircle,
      dot: "bg-green-500",
    },
    "Tidak Valid": {
      color: "bg-red-100 text-red-800 border-red-200",
      icon: XCircle,
      dot: "bg-red-500",
    },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Belum Upload"]
  const IconComponent = config.icon

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
      {status}
    </Badge>
  )
}

export default function KelengkapanDokumenSiswa() {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [validationDialogOpen, setValidationDialogOpen] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<any>(null)
  const [uploadFile, setUploadFile] = useState<File | null>(null)
  const [validationStatus, setValidationStatus] = useState("")
  const [validationNote, setValidationNote] = useState("")

  // Calculate statistics
  const totalDocuments = documentsData.length
  const uploadedDocuments = documentsData.filter((doc) => doc.statusUpload !== "Belum Upload").length
  const validDocuments = documentsData.filter((doc) => doc.statusUpload === "Valid").length
  const pendingDocuments = documentsData.filter((doc) => doc.statusUpload === "Dalam Verifikasi").length
  const invalidDocuments = documentsData.filter((doc) => doc.statusUpload === "Tidak Valid").length
  const wajibDocuments = documentsData.filter((doc) => doc.wajib).length
  const completedWajib = documentsData.filter((doc) => doc.wajib && doc.statusUpload === "Valid").length

  const completionPercentage = Math.round((validDocuments / totalDocuments) * 100)

  const handleUpload = (document: any) => {
    setSelectedDocument(document)
    setUploadDialogOpen(true)
  }

  const handleValidation = (document: any) => {
    setSelectedDocument(document)
    setValidationStatus(document.statusUpload)
    setValidationNote(document.catatan || "")
    setValidationDialogOpen(true)
  }

  const handleFileUpload = () => {
    if (uploadFile && selectedDocument) {
      console.log("Uploading file:", uploadFile.name, "for document:", selectedDocument.namaDokumen)
      // Here you would typically upload the file to your server
      setUploadDialogOpen(false)
      setUploadFile(null)
      setSelectedDocument(null)
    }
  }

  const handleValidationSubmit = () => {
    if (selectedDocument) {
      console.log("Updating validation:", {
        documentId: selectedDocument.id,
        status: validationStatus,
        note: validationNote,
      })
      // Here you would typically update the document status
      setValidationDialogOpen(false)
      setSelectedDocument(null)
      setValidationStatus("")
      setValidationNote("")
    }
  }

  const handleDownload = (fileName: string) => {
    console.log("Downloading file:", fileName)
    // Here you would typically trigger file download
  }

  const handleViewFile = (fileName: string) => {
    console.log("Viewing file:", fileName)
    // Here you would typically open file in new tab or modal
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <a href="/dokumen" className="text-gray-500 hover:text-gray-700">
                  Dokumen
                </a>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Kelengkapan Dokumen Siswa</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Kelengkapan Dokumen Siswa</h1>
                  <p className="text-gray-600 mt-1">Kelola dan validasi dokumen persyaratan siswa</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Student Information & Statistics */}
          <div className="lg:col-span-1 space-y-6">
            {/* Student Info Card */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
                <CardTitle className="text-lg text-gray-800 flex items-center">
                  <User className="h-5 w-5 mr-2 text-orange-600" />
                  Informasi Siswa
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                    <p className="text-lg font-semibold text-gray-900">{studentData.namaLengkap}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Gender</label>
                      <p className="font-medium text-gray-900">{studentData.gender}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 flex items-center">
                        <IdCard className="h-3 w-3 mr-1" />
                        NIK
                      </label>
                      <p className="font-mono text-sm text-gray-900">{studentData.nik}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      Kota/Kabupaten
                    </label>
                    <p className="font-medium text-gray-900">{studentData.kotaKabupaten}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center">
                      <Building2 className="h-4 w-4 mr-1" />
                      LPK Mitra
                    </label>
                    <p className="font-medium text-gray-900">{studentData.lpkMitra}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Progress Summary */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
                <CardTitle className="text-lg text-gray-800">Progress Dokumen</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {validDocuments}/{totalDocuments}
                  </div>
                  <p className="text-sm text-gray-500 mb-4">Dokumen valid</p>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-maroon-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${completionPercentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">{completionPercentage}% selesai</p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm font-medium text-green-800">Valid</span>
                    </div>
                    <span className="text-lg font-bold text-green-900">{validDocuments}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-blue-800">Verifikasi</span>
                    </div>
                    <span className="text-lg font-bold text-blue-900">{pendingDocuments}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex items-center">
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-sm font-medium text-red-800">Tidak Valid</span>
                    </div>
                    <span className="text-lg font-bold text-red-900">{invalidDocuments}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-gray-600 mr-2" />
                      <span className="text-sm font-medium text-gray-800">Belum Upload</span>
                    </div>
                    <span className="text-lg font-bold text-gray-900">{totalDocuments - uploadedDocuments}</span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="text-center">
                    <p className="text-sm font-medium text-orange-800">Dokumen Wajib</p>
                    <p className="text-lg font-bold text-orange-900">
                      {completedWajib}/{wajibDocuments}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Documents Table */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">Daftar Dokumen</CardTitle>
                    <CardDescription>Kelola kelengkapan dokumen siswa</CardDescription>
                  </div>
                  <Badge variant="outline" className="text-sm">
                    {totalDocuments} Dokumen
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                        <TableHead className="min-w-[250px] font-semibold">Nama Dokumen</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Tipe</TableHead>
                        <TableHead className="min-w-[150px] font-semibold">Status Upload</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Tanggal Upload</TableHead>
                        <TableHead className="min-w-[200px] font-semibold">Catatan</TableHead>
                        <TableHead className="w-[200px] text-center font-semibold">Aksi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {documentsData.map((document, index) => (
                        <TableRow key={document.id} className="hover:bg-gray-50">
                          <TableCell className="text-center font-medium">{index + 1}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <span className="font-medium">{document.namaDokumen}</span>
                              {document.wajib && (
                                <Badge variant="outline" className="ml-2 text-xs bg-red-50 text-red-700 border-red-200">
                                  Wajib
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{getTipeBadge(document.tipeDokumen)}</TableCell>
                          <TableCell>{getStatusBadge(document.statusUpload)}</TableCell>
                          <TableCell className="text-sm">
                            {document.tanggalUpload
                              ? new Date(document.tanggalUpload).toLocaleDateString("id-ID", {
                                  day: "2-digit",
                                  month: "short",
                                  year: "numeric",
                                })
                              : "-"}
                          </TableCell>
                          <TableCell className="text-sm text-gray-600 max-w-[200px] truncate">
                            {document.catatan || "-"}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {document.statusUpload === "Belum Upload" ? (
                                <Button
                                  size="sm"
                                  onClick={() => handleUpload(document)}
                                  className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white"
                                >
                                  <Upload className="h-3 w-3 mr-1" />
                                  Upload
                                </Button>
                              ) : (
                                <>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleViewFile(document.fileName!)}
                                    className="border-gray-300 bg-transparent"
                                  >
                                    <Eye className="h-3 w-3 mr-1" />
                                    Lihat
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDownload(document.fileName!)}
                                    className="border-gray-300 bg-transparent"
                                  >
                                    <Download className="h-3 w-3 mr-1" />
                                    Unduh
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleValidation(document)}
                                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                                  >
                                    Validasi
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Upload Dialog */}
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Upload className="h-5 w-5 mr-2 text-orange-600" />
                Upload Dokumen
              </DialogTitle>
              <DialogDescription>
                Upload dokumen: <strong>{selectedDocument?.namaDokumen}</strong>
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="file-upload">Pilih File *</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                />
                <p className="text-xs text-gray-500">Format yang didukung: PDF, JPG, JPEG, PNG (Max: 5MB)</p>
              </div>
              {uploadFile && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-700">File yang dipilih:</p>
                  <p className="text-sm text-gray-600">{uploadFile.name}</p>
                  <p className="text-xs text-gray-500">Ukuran: {(uploadFile.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
                Batal
              </Button>
              <Button
                onClick={handleFileUpload}
                disabled={!uploadFile}
                className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Validation Dialog */}
        <Dialog open={validationDialogOpen} onOpenChange={setValidationDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-orange-600" />
                Validasi Dokumen
              </DialogTitle>
              <DialogDescription>
                Validasi dokumen: <strong>{selectedDocument?.namaDokumen}</strong>
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="validation-status">Status Validasi *</Label>
                <Select value={validationStatus} onValueChange={setValidationStatus}>
                  <SelectTrigger className="border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="Pilih status validasi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dalam Verifikasi">Dalam Verifikasi</SelectItem>
                    <SelectItem value="Valid">Valid</SelectItem>
                    <SelectItem value="Tidak Valid">Tidak Valid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="validation-note">Catatan</Label>
                <Textarea
                  id="validation-note"
                  placeholder="Tambahkan catatan validasi..."
                  value={validationNote}
                  onChange={(e) => setValidationNote(e.target.value)}
                  className="border-gray-300 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setValidationDialogOpen(false)}>
                Batal
              </Button>
              <Button
                onClick={handleValidationSubmit}
                disabled={!validationStatus}
                className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
              >
                <Check className="h-4 w-4 mr-2" />
                Simpan Validasi
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
