"use client"

import { useState, useMemo } from "react"
import {
  Search,
  Eye,
  GraduationCap,
  BookOpen,
  Award,
  Users,
  Calendar,
  TrendingUp,
  Filter,
  Download,
  RefreshCw,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Sample education data
const educationData = [
  // Pra-Diklat
  {
    id: 1,
    namaSiswa: "Ahmad Rizki <PERSON>",
    lpkMitra: "LPK Sukses Mandiri Jakarta",
    jenisPendidikan: "Pra-Dikla<PERSON>",
    tanggalMulai: "2024-01-15",
    tanggalSelesai: "2024-02-15",
    status: "Selesai",
    hasilPendidikan: "Baik",
    durasi: "30 hari",
    materi: "Orientasi Dasar, Bahasa Jepang Level 1",
  },
  {
    id: 2,
    namaSiswa: "Siti Nurhaliza",
    lpkMitra: "LPK Maju Bersama Bandung",
    jenisPendidikan: "Pra-Diklat",
    tanggalMulai: "2024-01-20",
    tanggalSelesai: "2024-02-20",
    status: "Berlangsung",
    hasilPendidikan: null,
    durasi: "30 hari",
    materi: "Orientasi Dasar, Bahasa Jepang Level 1",
  },
  {
    id: 3,
    namaSiswa: "Budi Santoso",
    lpkMitra: "LPK Harapan Bangsa Surabaya",
    jenisPendidikan: "Pra-Diklat",
    tanggalMulai: "2024-02-01",
    tanggalSelesai: "2024-03-01",
    status: "Belum Mulai",
    hasilPendidikan: null,
    durasi: "30 hari",
    materi: "Orientasi Dasar, Bahasa Jepang Level 1",
  },
  // Diklat
  {
    id: 4,
    namaSiswa: "Ahmad Rizki Pratama",
    lpkMitra: "LPK Sukses Mandiri Jakarta",
    jenisPendidikan: "Diklat",
    tanggalMulai: "2024-02-16",
    tanggalSelesai: "2024-05-16",
    status: "Berlangsung",
    hasilPendidikan: null,
    durasi: "90 hari",
    materi: "Teknik Manufaktur, Bahasa Jepang Level 2, Budaya Kerja Jepang",
  },
  {
    id: 5,
    namaSiswa: "Dewi Sartika",
    lpkMitra: "LPK Karya Utama Medan",
    jenisPendidikan: "Diklat",
    tanggalMulai: "2024-01-10",
    tanggalSelesai: "2024-04-10",
    status: "Selesai",
    hasilPendidikan: "Baik",
    durasi: "90 hari",
    materi: "Teknik Manufaktur, Bahasa Jepang Level 2, Budaya Kerja Jepang",
  },
  {
    id: 6,
    namaSiswa: "Andi Wijaya",
    lpkMitra: "LPK Nusantara Makassar",
    jenisPendidikan: "Diklat",
    tanggalMulai: "2024-01-05",
    tanggalSelesai: "2024-04-05",
    status: "Selesai",
    hasilPendidikan: "Cukup",
    durasi: "90 hari",
    materi: "Teknik Manufaktur, Bahasa Jepang Level 2, Budaya Kerja Jepang",
  },
  // Pasca-Diklat
  {
    id: 7,
    namaSiswa: "Dewi Sartika",
    lpkMitra: "LPK Karya Utama Medan",
    jenisPendidikan: "Pasca-Diklat",
    tanggalMulai: "2024-04-11",
    tanggalSelesai: "2024-04-25",
    status: "Selesai",
    hasilPendidikan: "Baik",
    durasi: "14 hari",
    materi: "Persiapan Keberangkatan, Orientasi Budaya Jepang",
  },
  {
    id: 8,
    namaSiswa: "Andi Wijaya",
    lpkMitra: "LPK Nusantara Makassar",
    jenisPendidikan: "Pasca-Diklat",
    tanggalMulai: "2024-04-06",
    tanggalSelesai: "2024-04-20",
    status: "Berlangsung",
    hasilPendidikan: null,
    durasi: "14 hari",
    materi: "Persiapan Keberangkatan, Orientasi Budaya Jepang",
  },
  {
    id: 9,
    namaSiswa: "Rini Astuti",
    lpkMitra: "LPK Bina Prestasi Yogyakarta",
    jenisPendidikan: "Pasca-Diklat",
    tanggalMulai: "2024-04-15",
    tanggalSelesai: "2024-04-29",
    status: "Belum Mulai",
    hasilPendidikan: null,
    durasi: "14 hari",
    materi: "Persiapan Keberangkatan, Orientasi Budaya Jepang",
  },
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    "Belum Mulai": {
      color: "bg-gray-100 text-gray-600 border-gray-200",
      dot: "bg-gray-500",
    },
    Berlangsung: {
      color: "bg-blue-100 text-blue-800 border-blue-200",
      dot: "bg-blue-500",
    },
    Selesai: {
      color: "bg-green-100 text-green-800 border-green-200",
      dot: "bg-green-500",
    },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Belum Mulai"]

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
      {status}
    </Badge>
  )
}

const getHasilBadge = (hasil: string | null) => {
  if (!hasil) {
    return <span className="text-gray-400 text-sm">-</span>
  }

  const hasilConfig = {
    Baik: {
      color: "bg-green-100 text-green-800 border-green-200",
    },
    Cukup: {
      color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    },
    Kurang: {
      color: "bg-orange-100 text-orange-800 border-orange-200",
    },
    "Tidak Lulus": {
      color: "bg-red-100 text-red-800 border-red-200",
    },
  }

  const config = hasilConfig[hasil as keyof typeof hasilConfig] || hasilConfig.Baik

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      {hasil}
    </Badge>
  )
}

const getJenisBadge = (jenis: string) => {
  const jenisConfig = {
    "Pra-Diklat": {
      color: "bg-blue-100 text-blue-800 border-blue-200",
      icon: BookOpen,
    },
    Diklat: {
      color: "bg-orange-100 text-orange-800 border-orange-200",
      icon: GraduationCap,
    },
    "Pasca-Diklat": {
      color: "bg-maroon-100 text-maroon-800 border-maroon-200",
      icon: Award,
    },
  }

  const config = jenisConfig[jenis as keyof typeof jenisConfig] || jenisConfig["Pra-Diklat"]
  const IconComponent = config.icon

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {jenis}
    </Badge>
  )
}

export default function DataPendidikanSiswa() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("Semua Status")
  const [hasilFilter, setHasilFilter] = useState("Semua Hasil")
  const [activeTab, setActiveTab] = useState("semua")

  // Filter data based on active tab
  const filteredByTab = useMemo(() => {
    if (activeTab === "semua") return educationData
    const tabMapping = {
      "pra-diklat": "Pra-Diklat",
      diklat: "Diklat",
      "pasca-diklat": "Pasca-Diklat",
    }
    return educationData.filter((item) => item.jenisPendidikan === tabMapping[activeTab as keyof typeof tabMapping])
  }, [activeTab])

  // Apply search and filters
  const filteredData = useMemo(() => {
    return filteredByTab.filter((item) => {
      const matchesSearch =
        item.namaSiswa.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.lpkMitra.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "Semua Status" || item.status === statusFilter

      const matchesHasil =
        hasilFilter === "Semua Hasil" ||
        (hasilFilter === "Belum Ada Hasil" && !item.hasilPendidikan) ||
        item.hasilPendidikan === hasilFilter

      return matchesSearch && matchesStatus && matchesHasil
    })
  }, [filteredByTab, searchTerm, statusFilter, hasilFilter])

  // Calculate statistics
  const getStatistics = (data: typeof educationData) => {
    return {
      total: data.length,
      belumMulai: data.filter((item) => item.status === "Belum Mulai").length,
      berlangsung: data.filter((item) => item.status === "Berlangsung").length,
      selesai: data.filter((item) => item.status === "Selesai").length,
      hasilBaik: data.filter((item) => item.hasilPendidikan === "Baik").length,
      hasilCukup: data.filter((item) => item.hasilPendidikan === "Cukup").length,
      hasilKurang: data.filter((item) => item.hasilPendidikan === "Kurang").length,
      tidakLulus: data.filter((item) => item.hasilPendidikan === "Tidak Lulus").length,
    }
  }

  const stats = getStatistics(filteredByTab)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Data Pendidikan Siswa (Diklat)</h1>
              <p className="text-gray-600 mt-1">Monitor progress pendidikan siswa dalam 3 fase diklat</p>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Pendidikan</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg mr-3">
                  <Calendar className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Berlangsung</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.berlangsung}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg mr-3">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Selesai</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.selesai}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-maroon-100 rounded-lg mr-3">
                  <Award className="h-5 w-5 text-maroon-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Hasil Baik</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.hasilBaik}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid w-full grid-cols-4 bg-white border border-gray-200">
            <TabsTrigger
              value="semua"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-maroon-600 data-[state=active]:text-white"
            >
              Semua Fase
            </TabsTrigger>
            <TabsTrigger
              value="pra-diklat"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-maroon-600 data-[state=active]:text-white"
            >
              <BookOpen className="h-4 w-4 mr-2" />
              Pra-Diklat
            </TabsTrigger>
            <TabsTrigger
              value="diklat"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-maroon-600 data-[state=active]:text-white"
            >
              <GraduationCap className="h-4 w-4 mr-2" />
              Diklat
            </TabsTrigger>
            <TabsTrigger
              value="pasca-diklat"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-maroon-600 data-[state=active]:text-white"
            >
              <Award className="h-4 w-4 mr-2" />
              Pasca-Diklat
            </TabsTrigger>
          </TabsList>

          {/* Filters */}
          <Card className="mt-6">
            <CardHeader className="pb-4">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div className="flex flex-col sm:flex-row gap-4 flex-1">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Cari nama siswa atau LPK..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                    />
                  </div>

                  {/* Status Filter */}
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-[180px] h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Semua Status">Semua Status</SelectItem>
                      <SelectItem value="Belum Mulai">Belum Mulai</SelectItem>
                      <SelectItem value="Berlangsung">Berlangsung</SelectItem>
                      <SelectItem value="Selesai">Selesai</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Hasil Filter */}
                  <Select value={hasilFilter} onValueChange={setHasilFilter}>
                    <SelectTrigger className="w-full sm:w-[180px] h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                      <Award className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Semua Hasil">Semua Hasil</SelectItem>
                      <SelectItem value="Baik">Baik</SelectItem>
                      <SelectItem value="Cukup">Cukup</SelectItem>
                      <SelectItem value="Kurang">Kurang</SelectItem>
                      <SelectItem value="Tidak Lulus">Tidak Lulus</SelectItem>
                      <SelectItem value="Belum Ada Hasil">Belum Ada Hasil</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="border-gray-300 bg-transparent">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                  <Button variant="outline" size="sm" className="border-gray-300 bg-transparent">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Tab Content */}
          <TabsContent value={activeTab} className="mt-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      Data Pendidikan{" "}
                      {activeTab === "semua"
                        ? "Semua Fase"
                        : activeTab === "pra-diklat"
                          ? "Pra-Diklat"
                          : activeTab === "diklat"
                            ? "Diklat"
                            : "Pasca-Diklat"}
                    </CardTitle>
                    <CardDescription>Menampilkan {filteredData.length} data pendidikan</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                        <TableHead className="min-w-[200px] font-semibold">Nama Siswa</TableHead>
                        <TableHead className="min-w-[200px] font-semibold">LPK Mitra</TableHead>
                        <TableHead className="min-w-[150px] font-semibold">Jenis Pendidikan</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Tanggal Mulai</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Tanggal Selesai</TableHead>
                        <TableHead className="min-w-[100px] font-semibold">Durasi</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Status</TableHead>
                        <TableHead className="min-w-[120px] font-semibold">Hasil Pendidikan</TableHead>
                        <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredData.map((item, index) => (
                        <TableRow key={item.id} className="hover:bg-gray-50">
                          <TableCell className="text-center font-medium">{index + 1}</TableCell>
                          <TableCell className="font-medium">{item.namaSiswa}</TableCell>
                          <TableCell className="text-sm">{item.lpkMitra}</TableCell>
                          <TableCell>{getJenisBadge(item.jenisPendidikan)}</TableCell>
                          <TableCell className="text-sm">
                            {new Date(item.tanggalMulai).toLocaleDateString("id-ID", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                            })}
                          </TableCell>
                          <TableCell className="text-sm">
                            {new Date(item.tanggalSelesai).toLocaleDateString("id-ID", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                            })}
                          </TableCell>
                          <TableCell className="text-sm font-medium">{item.durasi}</TableCell>
                          <TableCell>{getStatusBadge(item.status)}</TableCell>
                          <TableCell>{getHasilBadge(item.hasilPendidikan)}</TableCell>
                          <TableCell className="text-center">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => (window.location.href = `/pendidikan/${item.id}`)}
                              className="border-gray-300 bg-transparent hover:bg-gray-50"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Detail
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Empty State */}
                {filteredData.length === 0 && (
                  <div className="text-center py-12">
                    <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada data pendidikan</h3>
                    <p className="text-gray-500">
                      {searchTerm || statusFilter !== "Semua Status" || hasilFilter !== "Semua Hasil"
                        ? "Tidak ada data yang sesuai dengan filter yang dipilih."
                        : "Belum ada data pendidikan untuk fase ini."}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
