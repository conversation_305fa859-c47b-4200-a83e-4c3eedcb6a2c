"use client"
import {
  ArrowLeft,
  Edit,
  GraduationCap,
  BookOpen,
  Award,
  User,
  Calendar,
  Clock,
  FileText,
  CheckCircle,
  Users,
  MapPin,
  TrendingUp,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"

// Sample detailed education data
const educationDetail = {
  id: 1,
  namaSiswa: "Ahmad Rizki Prata<PERSON>",
  lpkMitra: "LPK Sukses Mandiri Jakarta",
  jenisPendidikan: "Diklat",
  tanggalMulai: "2024-02-16",
  tanggalSelesai: "2024-05-16",
  status: "Berlangsung",
  hasilPendidikan: null,
  durasi: "90 hari",
  instruktur: "<PERSON><PERSON>, S.Pd",
  lokasi: "Jakarta Training Center",
  k<PERSON><PERSON>lum: "Kurikulum Standar Magang Jepang 2024",
  targetKompetensi: [
    "Teknik Manufaktur Dasar",
    "Bahasa Jepang Level N4",
    "Budaya Kerja Jepang",
    "Keselamatan Kerja",
    "Quality Control",
  ],
  materiPembelajaran: [
    {
      modul: "Teknik Manufaktur",
      durasi: "30 jam",
      status: "Selesai",
      nilai: 85,
    },
    {
      modul: "Bahasa Jepang Level 2",
      durasi: "40 jam",
      status: "Berlangsung",
      nilai: null,
    },
    {
      modul: "Budaya Kerja Jepang",
      durasi: "20 jam",
      status: "Belum Mulai",
      nilai: null,
    },
    {
      modul: "Keselamatan Kerja",
      durasi: "15 jam",
      status: "Belum Mulai",
      nilai: null,
    },
    {
      modul: "Quality Control",
      durasi: "25 jam",
      status: "Belum Mulai",
      nilai: null,
    },
  ],
  kehadiran: {
    totalHari: 60,
    hadir: 45,
    sakit: 2,
    izin: 1,
    alpha: 0,
    persentase: 96,
  },
  evaluasi: [
    {
      tanggal: "2024-03-01",
      jenis: "Ujian Tengah",
      nilai: 82,
      catatan: "Pemahaman baik, perlu peningkatan di praktik",
    },
    {
      tanggal: "2024-03-15",
      jenis: "Praktik Lapangan",
      nilai: 88,
      catatan: "Sangat baik dalam aplikasi praktis",
    },
  ],
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    "Belum Mulai": {
      color: "bg-gray-100 text-gray-600 border-gray-200",
      dot: "bg-gray-500",
    },
    Berlangsung: {
      color: "bg-blue-100 text-blue-800 border-blue-200",
      dot: "bg-blue-500",
    },
    Selesai: {
      color: "bg-green-100 text-green-800 border-green-200",
      dot: "bg-green-500",
    },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Belum Mulai"]

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
      {status}
    </Badge>
  )
}

const getJenisBadge = (jenis: string) => {
  const jenisConfig = {
    "Pra-Diklat": {
      color: "bg-blue-100 text-blue-800 border-blue-200",
      icon: BookOpen,
    },
    Diklat: {
      color: "bg-orange-100 text-orange-800 border-orange-200",
      icon: GraduationCap,
    },
    "Pasca-Diklat": {
      color: "bg-maroon-100 text-maroon-800 border-maroon-200",
      icon: Award,
    },
  }

  const config = jenisConfig[jenis as keyof typeof jenisConfig] || jenisConfig["Pra-Diklat"]
  const IconComponent = config.icon

  return (
    <Badge variant="outline" className={`${config.color} font-medium text-base px-3 py-1`}>
      <IconComponent className="w-4 h-4 mr-2" />
      {jenis}
    </Badge>
  )
}

const getNilaiBadge = (nilai: number | null) => {
  if (nilai === null) {
    return <span className="text-gray-400 text-sm">Belum dinilai</span>
  }

  let color = "bg-red-100 text-red-800 border-red-200"
  if (nilai >= 80) color = "bg-green-100 text-green-800 border-green-200"
  else if (nilai >= 70) color = "bg-yellow-100 text-yellow-800 border-yellow-200"
  else if (nilai >= 60) color = "bg-orange-100 text-orange-800 border-orange-200"

  return (
    <Badge variant="outline" className={`${color} font-medium`}>
      {nilai}
    </Badge>
  )
}

export default function DetailPendidikan() {
  const progressPercentage = Math.round(
    (educationDetail.materiPembelajaran.filter((m) => m.status === "Selesai").length /
      educationDetail.materiPembelajaran.length) *
      100,
  )

  const currentDate = new Date()
  const startDate = new Date(educationDetail.tanggalMulai)
  const endDate = new Date(educationDetail.tanggalSelesai)
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const elapsedDays = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const timeProgressPercentage = Math.min(Math.max((elapsedDays / totalDays) * 100, 0), 100)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <a href="/pendidikan" className="text-gray-500 hover:text-gray-700">
                  Data Pendidikan
                </a>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Detail Pendidikan</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Detail Pendidikan Siswa</h1>
                  <p className="text-gray-600 mt-1">Informasi lengkap progress pendidikan</p>
                </div>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
              <Edit className="h-4 w-4 mr-2" />
              Edit Data
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl text-gray-800 flex items-center">
                      <User className="h-5 w-5 mr-2 text-orange-600" />
                      {educationDetail.namaSiswa}
                    </CardTitle>
                    <CardDescription className="text-base font-medium text-gray-700 mt-1">
                      {educationDetail.lpkMitra}
                    </CardDescription>
                  </div>
                  {getJenisBadge(educationDetail.jenisPendidikan)}
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Calendar className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Tanggal Mulai</p>
                        <p className="font-semibold">
                          {new Date(educationDetail.tanggalMulai).toLocaleDateString("id-ID", {
                            day: "2-digit",
                            month: "long",
                            year: "numeric",
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Tanggal Selesai</p>
                        <p className="font-semibold">
                          {new Date(educationDetail.tanggalSelesai).toLocaleDateString("id-ID", {
                            day: "2-digit",
                            month: "long",
                            year: "numeric",
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Durasi</p>
                        <p className="font-semibold">{educationDetail.durasi}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Instruktur</p>
                        <p className="font-semibold">{educationDetail.instruktur}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-orange-600 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Lokasi</p>
                        <p className="font-semibold">{educationDetail.lokasi}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-orange-600 mr-3 mt-1" />
                      <div>
                        <p className="text-sm text-gray-500">Status</p>
                        {getStatusBadge(educationDetail.status)}
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <div>
                  <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-orange-600" />
                    Kurikulum
                  </h3>
                  <p className="text-sm text-gray-700 mb-4">{educationDetail.kurikulum}</p>

                  <h4 className="font-medium text-gray-800 mb-2">Target Kompetensi:</h4>
                  <div className="flex flex-wrap gap-2">
                    {educationDetail.targetKompetensi.map((kompetensi, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {kompetensi}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Learning Materials */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Materi Pembelajaran</CardTitle>
                <CardDescription>Progress pembelajaran per modul</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {educationDetail.materiPembelajaran.map((materi, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{materi.modul}</h4>
                        {getStatusBadge(materi.status)}
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>Durasi: {materi.durasi}</span>
                        <span>Nilai: {getNilaiBadge(materi.nilai)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            materi.status === "Selesai"
                              ? "bg-green-500"
                              : materi.status === "Berlangsung"
                                ? "bg-blue-500"
                                : "bg-gray-400"
                          }`}
                          style={{
                            width:
                              materi.status === "Selesai" ? "100%" : materi.status === "Berlangsung" ? "60%" : "0%",
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Evaluations */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Riwayat Evaluasi</CardTitle>
                <CardDescription>Hasil ujian dan penilaian</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {educationDetail.evaluasi.map((evaluation, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{evaluation.jenis}</h4>
                          <p className="text-sm text-gray-500">
                            {new Date(evaluation.tanggal).toLocaleDateString("id-ID", {
                              day: "2-digit",
                              month: "long",
                              year: "numeric",
                            })}
                          </p>
                        </div>
                        {getNilaiBadge(evaluation.nilai)}
                      </div>
                      <p className="text-sm text-gray-700">{evaluation.catatan}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Progress Summary */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
                <CardTitle className="text-lg text-gray-800 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                  Progress Pembelajaran
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">{progressPercentage}%</div>
                  <p className="text-sm text-gray-500 mb-4">Materi selesai</p>
                  <Progress value={progressPercentage} className="h-3" />
                  <p className="text-xs text-gray-500 mt-2">
                    {educationDetail.materiPembelajaran.filter((m) => m.status === "Selesai").length} dari{" "}
                    {educationDetail.materiPembelajaran.length} modul
                  </p>
                </div>

                <Separator className="my-4" />

                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-2">{Math.round(timeProgressPercentage)}%</div>
                  <p className="text-sm text-gray-500 mb-4">Waktu berlalu</p>
                  <Progress value={timeProgressPercentage} className="h-3" />
                  <p className="text-xs text-gray-500 mt-2">
                    {Math.max(elapsedDays, 0)} dari {totalDays} hari
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Attendance */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-gray-800">Kehadiran</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-2xl font-bold text-green-600 mb-1">{educationDetail.kehadiran.persentase}%</div>
                  <p className="text-sm text-gray-500">Tingkat kehadiran</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Hadir</span>
                    <span className="font-medium text-green-600">{educationDetail.kehadiran.hadir} hari</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Sakit</span>
                    <span className="font-medium text-yellow-600">{educationDetail.kehadiran.sakit} hari</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Izin</span>
                    <span className="font-medium text-blue-600">{educationDetail.kehadiran.izin} hari</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Alpha</span>
                    <span className="font-medium text-red-600">{educationDetail.kehadiran.alpha} hari</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center font-medium">
                    <span className="text-gray-800">Total</span>
                    <span className="text-gray-800">{educationDetail.kehadiran.totalHari} hari</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
