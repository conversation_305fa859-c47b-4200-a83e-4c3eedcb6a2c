"use client"

import { useState, useEffect } from "react"
import {
  Users,
  Plane,
  Clock,
  Building2,
  TrendingUp,
  FileText,
  CheckCircle,
  AlertCircle,
  Database,
  Wifi,
  HardDrive,
  UserPlus,
  UsersIcon,
  Briefcase,
  FolderOpen,
  Loader2,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Bar, Bar<PERSON>hart, ResponsiveContainer, XAxis, YAxis, Pie, <PERSON>Chart, Cell } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import Link from "next/link"
import { DashboardService } from "@/lib/services"

// Types for dashboard data
interface DashboardStats {
  siswa: {
    total: number
    approved: number
    pending: number
    rejected: number
  }
  penempatan: {
    total: number
    active: number
    berangkat: number
    selesai: number
    ditempatkan: number
  }
  jobOrder: {
    total: number
    published: number
    closed: number
    draft: number
  }
  lpk: {
    total: number
    active: number
    inactive: number
  }
}

interface MonthlyData {
  month: string
  count: number
}

interface PrefectureData {
  prefecture: string
  count: number
}

interface RecentActivity {
  id: string
  created_at: string
  status_penempatan: string
  siswa: { nama_lengkap: string } | null
  perusahaan_penerima: { nama_perusahaan: string } | null
  job_order: { judul_pekerjaan: string } | null
}

export default function Dashboard() {
  const [currentTime] = useState(new Date())
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Dashboard data states
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([])
  const [prefectureData, setPrefectureData] = useState<PrefectureData[]>([])
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [genderData, setGenderData] = useState<{ gender: string; count: number }[]>([])

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        setError(null)

        const [
          detailedStats,
          monthlyPlacement,
          placementByPrefecture,
          activities,
          genderDistribution,
        ] = await Promise.all([
          DashboardService.getDetailedStats(),
          DashboardService.getMonthlyPlacementData(),
          DashboardService.getPlacementByPrefecture(),
          DashboardService.getRecentActivities(5),
          DashboardService.getGenderDistribution(),
        ])

        setStats(detailedStats)
        setMonthlyData(monthlyPlacement)
        setPrefectureData(placementByPrefecture)
        setRecentActivities(activities)
        setGenderData(genderDistribution)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Gagal memuat data dashboard. Silakan refresh halaman.')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Format recent activities for display
  const formatRecentActivities = (activities: RecentActivity[]) => {
    return activities.map((activity) => ({
      id: activity.id,
      title: `${activity.siswa?.nama_lengkap || 'Siswa'} - ${activity.status_penempatan} di ${activity.perusahaan_penerima?.nama_perusahaan || 'Perusahaan'}`,
      time: new Date(activity.created_at).toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      }),
      icon: getStatusIcon(activity.status_penempatan),
      color: getStatusColor(activity.status_penempatan),
    }))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ditempatkan': return UserPlus
      case 'berangkat': return Plane
      case 'aktif': return CheckCircle
      case 'selesai': return FileText
      default: return AlertCircle
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ditempatkan': return 'text-blue-600'
      case 'berangkat': return 'text-orange-600'
      case 'aktif': return 'text-green-600'
      case 'selesai': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  // Prepare status data for pie chart
  const statusData = stats ? [
    { name: "Aktif di Jepang", value: stats.penempatan.active, color: "#22c55e" },
    { name: "Menunggu Keberangkatan", value: stats.penempatan.berangkat, color: "#f59e0b" },
    { name: "Sudah Selesai", value: stats.penempatan.selesai, color: "#6366f1" },
    { name: "Ditempatkan", value: stats.penempatan.ditempatkan, color: "#ef4444" },
  ] : []

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Memuat data dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Terjadi Kesalahan</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-red-500 hover:bg-red-600"
            >
              Refresh Halaman
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Sistem Magang Jepang</h1>
          <p className="text-gray-600 mt-1">Selamat datang di sistem manajemen penyaluran siswa magang ke Jepang</p>
          <p className="text-sm text-gray-500 mt-2">
            Terakhir diperbarui:{" "}
            {currentTime.toLocaleDateString("id-ID", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
        </div>

        <Link href="/penempatan">
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <TrendingUp className="w-4 h-4 mr-2" />
            Detail Penempatan
          </Button>
        </Link>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Siswa</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats?.siswa.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.siswa.approved || 0} siswa disetujui
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif di Jepang</CardTitle>
            <Plane className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats?.penempatan.active || 0}</div>
            <p className="text-xs text-muted-foreground">Sedang menjalani magang</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Menunggu Keberangkatan</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats?.penempatan.berangkat || 0}</div>
            <p className="text-xs text-muted-foreground">Dalam proses persiapan</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-maroon-600">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Job Order Aktif</CardTitle>
            <Building2 className="h-4 w-4 text-maroon-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-maroon-600">{stats?.jobOrder.published || 0}</div>
            <p className="text-xs text-muted-foreground">Lowongan tersedia</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trend Chart */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Trend Penempatan Bulanan</CardTitle>
            <p className="text-sm text-gray-600">Jumlah penempatan siswa per bulan tahun ini</p>
          </CardHeader>
          <CardContent>
            {monthlyData.length > 0 ? (
              <ChartContainer
                config={{
                  count: { label: "Penempatan", color: "#3b82f6" },
                }}
                className="h-[300px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyData}>
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="count" fill="var(--color-count)" radius={4} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-gray-500">
                <p>Tidak ada data penempatan</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status Distribution Chart */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Distribusi Status Penempatan</CardTitle>
            <p className="text-sm text-gray-600">Komposisi status penempatan siswa saat ini</p>
          </CardHeader>
          <CardContent>
            {statusData.length > 0 && statusData.some(item => item.value > 0) ? (
              <>
                <ChartContainer
                  config={{
                    aktif: { label: "Aktif di Jepang", color: "#22c55e" },
                    menunggu: { label: "Menunggu", color: "#f59e0b" },
                    selesai: { label: "Sudah Selesai", color: "#6366f1" },
                    ditempatkan: { label: "Ditempatkan", color: "#ef4444" },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={statusData.filter(item => item.value > 0)}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {statusData.filter(item => item.value > 0).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <ChartTooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload
                            return (
                              <div className="bg-white p-3 border rounded-lg shadow-lg">
                                <p className="font-medium">{data.name}</p>
                                <p className="text-sm text-gray-600">{data.value} siswa</p>
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>

                {/* Legend */}
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {statusData.filter(item => item.value > 0).map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }} />
                      <span className="text-xs text-gray-600">{item.name} ({item.value})</span>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-gray-500">
                <p>Tidak ada data penempatan</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Activities and Tasks Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Aktivitas Terbaru</CardTitle>
            <p className="text-sm text-gray-600">Penempatan siswa terbaru</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                formatRecentActivities(recentActivities).map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                      <activity.icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Belum ada aktivitas terbaru</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Summary Statistics */}
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Ringkasan Statistik</CardTitle>
            <p className="text-sm text-gray-600">Informasi penting sistem</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 rounded-lg border hover:shadow-sm transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">Siswa Menunggu Persetujuan</h4>
                  <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>Perlu review</span>
                  <span>{stats?.siswa.pending || 0} siswa</span>
                </div>
                <Progress value={stats?.siswa.pending ? (stats.siswa.pending / stats.siswa.total) * 100 : 0} className="h-2" />
              </div>

              <div className="p-3 rounded-lg border hover:shadow-sm transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">Job Order Draft</h4>
                  <Badge className="bg-blue-100 text-blue-800">Draft</Badge>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>Belum dipublikasi</span>
                  <span>{stats?.jobOrder.draft || 0} job order</span>
                </div>
                <Progress value={stats?.jobOrder.draft ? (stats.jobOrder.draft / stats.jobOrder.total) * 100 : 0} className="h-2" />
              </div>

              <div className="p-3 rounded-lg border hover:shadow-sm transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">LPK Mitra Aktif</h4>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span>Kemitraan aktif</span>
                  <span>{stats?.lpk.active || 0} LPK</span>
                </div>
                <Progress value={stats?.lpk.active ? (stats.lpk.active / stats.lpk.total) * 100 : 0} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-l-4 border-l-indigo-500">
        <CardHeader>
          <CardTitle className="text-lg text-gray-800">Aksi Cepat</CardTitle>
          <p className="text-sm text-gray-600">Shortcut ke fitur utama sistem</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/pendaftaran">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-blue-50 hover:border-blue-300 bg-transparent"
              >
                <UserPlus className="w-6 h-6 text-blue-600" />
                <span className="text-sm">Daftar Siswa Baru</span>
              </Button>
            </Link>

            <Link href="/siswa">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-green-50 hover:border-green-300 bg-transparent"
              >
                <UsersIcon className="w-6 h-6 text-green-600" />
                <span className="text-sm">Kelola Data Siswa</span>
              </Button>
            </Link>

            <Link href="/job-order">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-orange-50 hover:border-orange-300 bg-transparent"
              >
                <Briefcase className="w-6 h-6 text-orange-600" />
                <span className="text-sm">Job Order Baru</span>
              </Button>
            </Link>

            <Link href="/dokumen">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-purple-50 hover:border-purple-300 bg-transparent"
              >
                <FolderOpen className="w-6 h-6 text-purple-600" />
                <span className="text-sm">Cek Dokumen</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card className="border-l-4 border-l-gray-500">
        <CardHeader>
          <CardTitle className="text-lg text-gray-800">Status Sistem</CardTitle>
          <p className="text-sm text-gray-600">Monitoring kesehatan sistem</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50">
              <Database className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Database</p>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-xs text-green-600">Normal</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50">
              <Wifi className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">API Status</p>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-xs text-green-600">Online</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 rounded-lg bg-yellow-50">
              <HardDrive className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Backup</p>
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600" />
                  <span className="text-xs text-yellow-600">Scheduled</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
