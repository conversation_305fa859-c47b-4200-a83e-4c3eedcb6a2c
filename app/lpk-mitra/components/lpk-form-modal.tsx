"use client"

import React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { LpkMitra } from "@/lib/types/database"

interface FormData {
  nama_lpk: string
  alamat_lengkap: string
  kota: string
  provinsi: string
  nama_pimpinan: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  tanggal_kerjasama?: string
  catatan?: string
}

interface FormErrors {
  nama_lpk?: string
  alamat_lengkap?: string
  kota?: string
  provinsi?: string
  nama_pimpinan?: string
  kontak_person?: string
  nomor_telepon?: string
  email?: string
  tanggal_kerjasama?: string
}

interface LpkFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: FormData) => Promise<void>
  editingLpk: LpkMitra | null
  formData: FormData
  setFormData: (data: FormData) => void
  formErrors: FormErrors
  isSubmitting: boolean
}

export function LpkFormModal({
  isOpen,
  onClose,
  onSubmit,
  editingLpk,
  formData,
  setFormData,
  formErrors,
  isSubmitting,
}: LpkFormModalProps) {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSubmit(formData)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {editingLpk ? "Edit LPK Mitra" : "Tambah LPK Mitra Baru"}
          </DialogTitle>
          <DialogDescription>
            {editingLpk
              ? "Perbarui informasi LPK Mitra yang sudah ada"
              : "Masukkan informasi LPK Mitra baru untuk didaftarkan ke sistem"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nama LPK */}
            <div className="md:col-span-2">
              <Label htmlFor="nama_lpk" className="text-sm font-medium text-gray-700">
                Nama LPK Mitra <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nama_lpk"
                value={formData.nama_lpk}
                onChange={(e) => setFormData({ ...formData, nama_lpk: e.target.value })}
                placeholder="Masukkan nama lengkap LPK"
                className={formErrors.nama_lpk ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.nama_lpk && <p className="text-sm text-red-600 mt-1">{formErrors.nama_lpk}</p>}
            </div>

            {/* Alamat Lengkap */}
            <div className="md:col-span-2">
              <Label htmlFor="alamat_lengkap" className="text-sm font-medium text-gray-700">
                Alamat Lengkap <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="alamat_lengkap"
                value={formData.alamat_lengkap}
                onChange={(e) => setFormData({ ...formData, alamat_lengkap: e.target.value })}
                placeholder="Masukkan alamat lengkap LPK"
                className={formErrors.alamat_lengkap ? "border-red-500 focus:border-red-500" : ""}
                rows={2}
              />
              {formErrors.alamat_lengkap && <p className="text-sm text-red-600 mt-1">{formErrors.alamat_lengkap}</p>}
            </div>

            {/* Kota */}
            <div>
              <Label htmlFor="kota" className="text-sm font-medium text-gray-700">
                Kota <span className="text-red-500">*</span>
              </Label>
              <Input
                id="kota"
                value={formData.kota}
                onChange={(e) => setFormData({ ...formData, kota: e.target.value })}
                placeholder="Masukkan kota"
                className={formErrors.kota ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.kota && <p className="text-sm text-red-600 mt-1">{formErrors.kota}</p>}
            </div>

            {/* Provinsi */}
            <div>
              <Label htmlFor="provinsi" className="text-sm font-medium text-gray-700">
                Provinsi <span className="text-red-500">*</span>
              </Label>
              <Input
                id="provinsi"
                value={formData.provinsi}
                onChange={(e) => setFormData({ ...formData, provinsi: e.target.value })}
                placeholder="Masukkan provinsi"
                className={formErrors.provinsi ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.provinsi && <p className="text-sm text-red-600 mt-1">{formErrors.provinsi}</p>}
            </div>

            {/* Nama Pimpinan */}
            <div>
              <Label htmlFor="nama_pimpinan" className="text-sm font-medium text-gray-700">
                Nama Pimpinan <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nama_pimpinan"
                value={formData.nama_pimpinan}
                onChange={(e) => setFormData({ ...formData, nama_pimpinan: e.target.value })}
                placeholder="Nama pimpinan LPK"
                className={formErrors.nama_pimpinan ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.nama_pimpinan && <p className="text-sm text-red-600 mt-1">{formErrors.nama_pimpinan}</p>}
            </div>

            {/* Kontak Person */}
            <div>
              <Label htmlFor="kontak_person" className="text-sm font-medium text-gray-700">
                Kontak Person <span className="text-red-500">*</span>
              </Label>
              <Input
                id="kontak_person"
                value={formData.kontak_person}
                onChange={(e) => setFormData({ ...formData, kontak_person: e.target.value })}
                placeholder="Nama penanggung jawab"
                className={formErrors.kontak_person ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.kontak_person && <p className="text-sm text-red-600 mt-1">{formErrors.kontak_person}</p>}
            </div>

            {/* Nomor Telepon */}
            <div>
              <Label htmlFor="nomor_telepon" className="text-sm font-medium text-gray-700">
                Nomor Telepon <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nomor_telepon"
                value={formData.nomor_telepon}
                onChange={(e) => setFormData({ ...formData, nomor_telepon: e.target.value })}
                placeholder="Contoh: 021-1234567"
                className={formErrors.nomor_telepon ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.nomor_telepon && <p className="text-sm text-red-600 mt-1">{formErrors.nomor_telepon}</p>}
            </div>

            {/* Email */}
            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email (Opsional)
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                className={formErrors.email ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.email && <p className="text-sm text-red-600 mt-1">{formErrors.email}</p>}
            </div>

            {/* Website */}
            <div>
              <Label htmlFor="website" className="text-sm font-medium text-gray-700">
                Website (Opsional)
              </Label>
              <Input
                id="website"
                type="url"
                value={formData.website || ""}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                placeholder="https://website.com"
              />
            </div>

            {/* Tanggal Kerjasama */}
            <div>
              <Label htmlFor="tanggal_kerjasama" className="text-sm font-medium text-gray-700">
                Tanggal Kerjasama (Opsional)
              </Label>
              <Input
                id="tanggal_kerjasama"
                type="date"
                value={formData.tanggal_kerjasama || ''}
                onChange={(e) => setFormData({ ...formData, tanggal_kerjasama: e.target.value })}
                className={formErrors.tanggal_kerjasama ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.tanggal_kerjasama && <p className="text-sm text-red-600 mt-1">{formErrors.tanggal_kerjasama}</p>}
            </div>

            {/* Catatan */}
            <div className="md:col-span-2">
              <Label htmlFor="catatan" className="text-sm font-medium text-gray-700">
                Catatan/Keterangan (Opsional)
              </Label>
              <Textarea
                id="catatan"
                value={formData.catatan || ""}
                onChange={(e) => setFormData({ ...formData, catatan: e.target.value })}
                placeholder="Masukkan catatan atau keterangan tambahan tentang LPK"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Batal
            </Button>
            <Button type="submit" disabled={isSubmitting} className="bg-orange-500 hover:bg-orange-600 text-white">
              {isSubmitting ? "Menyimpan..." : editingLpk ? "Perbarui" : "Simpan"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
