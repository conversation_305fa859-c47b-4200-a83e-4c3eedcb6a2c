'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/hooks/use-toast'
import { RBACService } from '@/lib/services/rbac.service'
import type { Role, Permission, CreateRoleRequest, UpdateRoleRequest } from '@/lib/types/rbac'
import { MODULE_NAMES, ACTION_NAMES } from '@/lib/types/rbac'

interface RoleFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  role?: Role | null
  permissions: Permission[]
}

export function RoleFormModal({ 
  isOpen, 
  onClose, 
  onSave, 
  role,
  permissions 
}: RoleFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    description: '',
    is_active: true,
    permissions: [] as string[]
  })

  const [rolePermissions, setRolePermissions] = useState<Record<string, boolean>>({})

  const isEditing = !!role

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        display_name: role.display_name,
        description: role.description || '',
        is_active: role.is_active,
        permissions: []
      })
      
      // Load existing permissions for this role
      loadRolePermissions(role.id)
    } else {
      setFormData({
        name: '',
        display_name: '',
        description: '',
        is_active: true,
        permissions: []
      })
      setRolePermissions({})
    }
  }, [role])

  const loadRolePermissions = async (roleId: string) => {
    try {
      const permissions = await RBACService.getRolePermissions(roleId)
      setRolePermissions(permissions)
    } catch (error) {
      console.error('Error loading role permissions:', error)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePermissionChange = (permissionName: string, checked: boolean) => {
    setRolePermissions(prev => ({
      ...prev,
      [permissionName]: checked
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const selectedPermissions = Object.keys(rolePermissions).filter(
        permName => rolePermissions[permName]
      )

      if (isEditing) {
        const updateData: UpdateRoleRequest = {
          display_name: formData.display_name,
          description: formData.description || undefined,
          is_active: formData.is_active,
          permissions: selectedPermissions
        }
        await RBACService.updateRole(role!.id, updateData)
      } else {
        if (!formData.name || !formData.display_name) {
          toast({
            title: 'Error',
            description: 'Nama role dan display name wajib diisi',
            variant: 'destructive'
          })
          return
        }

        const createData: CreateRoleRequest = {
          name: formData.name,
          display_name: formData.display_name,
          description: formData.description || undefined,
          permissions: selectedPermissions
        }
        await RBACService.createRole(createData)
      }

      onSave()
    } catch (error: any) {
      console.error('Error saving role:', error)
      toast({
        title: 'Error',
        description: error.message || 'Gagal menyimpan role',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Group permissions by module
  const permissionsByModule = permissions.reduce((acc, permission) => {
    if (!acc[permission.module]) {
      acc[permission.module] = []
    }
    acc[permission.module].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Role' : 'Tambah Role Baru'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update informasi role dan permissions'
              : 'Buat role baru dan assign permissions yang sesuai'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informasi Role</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Role Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={isEditing} // Role name cannot be changed after creation
                  placeholder="e.g., administrator, lpk_mitra"
                  required
                />
                <p className="text-xs text-gray-500">
                  Nama role dalam sistem (tidak dapat diubah setelah dibuat)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="display_name">Display Name *</Label>
                <Input
                  id="display_name"
                  value={formData.display_name}
                  onChange={(e) => handleInputChange('display_name', e.target.value)}
                  placeholder="e.g., Administrator, LPK Mitra"
                  required
                />
                <p className="text-xs text-gray-500">
                  Nama yang ditampilkan kepada pengguna
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Deskripsi role dan tanggung jawabnya..."
                rows={3}
              />
            </div>

            {isEditing && (
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                />
                <Label htmlFor="is_active">Role Aktif</Label>
              </div>
            )}
          </div>

          {/* Permissions */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Permissions</h3>
            <p className="text-sm text-gray-600">
              Pilih permissions yang akan diberikan kepada role ini
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(permissionsByModule).map(([module, modulePermissions]) => (
                <Card key={module}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">
                      {MODULE_NAMES[module as keyof typeof MODULE_NAMES] || module}
                    </CardTitle>
                    <CardDescription className="text-xs">
                      {modulePermissions.length} permissions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {modulePermissions
                      .sort((a, b) => {
                        const actionOrder = ['read', 'create', 'update', 'delete']
                        return actionOrder.indexOf(a.action) - actionOrder.indexOf(b.action)
                      })
                      .map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`perm-${permission.id}`}
                            checked={rolePermissions[permission.name] || false}
                            onCheckedChange={(checked) => 
                              handlePermissionChange(permission.name, checked as boolean)
                            }
                          />
                          <div className="flex-1 min-w-0">
                            <Label 
                              htmlFor={`perm-${permission.id}`} 
                              className="text-xs font-medium cursor-pointer"
                            >
                              <Badge 
                                variant="outline" 
                                className={`mr-1 text-xs ${
                                  permission.action === 'create' ? 'bg-green-50 text-green-700' :
                                  permission.action === 'read' ? 'bg-blue-50 text-blue-700' :
                                  permission.action === 'update' ? 'bg-yellow-50 text-yellow-700' :
                                  permission.action === 'delete' ? 'bg-red-50 text-red-700' : ''
                                }`}
                              >
                                {ACTION_NAMES[permission.action as keyof typeof ACTION_NAMES]}
                              </Badge>
                            </Label>
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Menyimpan...' : (isEditing ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
