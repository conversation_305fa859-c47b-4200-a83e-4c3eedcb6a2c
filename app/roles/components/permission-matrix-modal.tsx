'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { toast } from '@/hooks/use-toast'
import { RBACService } from '@/lib/services/rbac.service'
import type { Role, Permission } from '@/lib/types/rbac'
import { MODULE_NAMES, ACTION_NAMES } from '@/lib/types/rbac'

interface PermissionMatrixModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  roles: Role[]
  permissions: Permission[]
}

export function PermissionMatrixModal({ 
  isOpen, 
  onClose, 
  onSave, 
  roles,
  permissions 
}: PermissionMatrixModalProps) {
  const [loading, setLoading] = useState(false)
  const [matrix, setMatrix] = useState<Record<string, Record<string, boolean>>>({})
  const [originalMatrix, setOriginalMatrix] = useState<Record<string, Record<string, boolean>>>({})

  useEffect(() => {
    if (isOpen && roles.length > 0) {
      loadPermissionMatrix()
    }
  }, [isOpen, roles])

  const loadPermissionMatrix = async () => {
    try {
      const matrixData: Record<string, Record<string, boolean>> = {}
      
      for (const role of roles) {
        const rolePermissions = await RBACService.getRolePermissions(role.id)
        matrixData[role.id] = rolePermissions
      }
      
      setMatrix(matrixData)
      setOriginalMatrix(JSON.parse(JSON.stringify(matrixData)))
    } catch (error) {
      console.error('Error loading permission matrix:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat permission matrix',
        variant: 'destructive'
      })
    }
  }

  const handlePermissionChange = (roleId: string, permissionName: string, checked: boolean) => {
    setMatrix(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [permissionName]: checked
      }
    }))
  }

  const handleSelectAllForRole = (roleId: string, checked: boolean) => {
    const rolePermissions: Record<string, boolean> = {}
    permissions.forEach(permission => {
      rolePermissions[permission.name] = checked
    })
    
    setMatrix(prev => ({
      ...prev,
      [roleId]: rolePermissions
    }))
  }

  const handleSelectAllForPermission = (permissionName: string, checked: boolean) => {
    setMatrix(prev => {
      const newMatrix = { ...prev }
      roles.forEach(role => {
        if (!newMatrix[role.id]) newMatrix[role.id] = {}
        newMatrix[role.id][permissionName] = checked
      })
      return newMatrix
    })
  }

  const handleSubmit = async () => {
    setLoading(true)
    
    try {
      // Update permissions for each role that has changes
      for (const role of roles) {
        const currentPermissions = matrix[role.id] || {}
        const originalPermissions = originalMatrix[role.id] || {}
        
        // Check if there are changes for this role
        const hasChanges = Object.keys(currentPermissions).some(
          permName => currentPermissions[permName] !== originalPermissions[permName]
        ) || Object.keys(originalPermissions).some(
          permName => currentPermissions[permName] !== originalPermissions[permName]
        )
        
        if (hasChanges) {
          const selectedPermissions = Object.keys(currentPermissions).filter(
            permName => currentPermissions[permName]
          )
          
          await RBACService.updateRolePermissions(role.id, selectedPermissions)
        }
      }
      
      onSave()
    } catch (error: any) {
      console.error('Error saving permission matrix:', error)
      toast({
        title: 'Error',
        description: error.message || 'Gagal menyimpan permission matrix',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Group permissions by module for better organization
  const permissionsByModule = permissions.reduce((acc, permission) => {
    if (!acc[permission.module]) {
      acc[permission.module] = []
    }
    acc[permission.module].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  const activeRoles = roles.filter(role => role.is_active)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Permission Matrix</DialogTitle>
          <DialogDescription>
            Kelola permissions untuk semua roles dalam satu tampilan. 
            Centang kotak untuk memberikan permission kepada role.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {Object.entries(permissionsByModule).map(([module, modulePermissions]) => (
            <Card key={module}>
              <CardHeader>
                <CardTitle className="text-lg">
                  {MODULE_NAMES[module as keyof typeof MODULE_NAMES] || module}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-48">Permission</TableHead>
                        {activeRoles.map((role) => (
                          <TableHead key={role.id} className="text-center min-w-32">
                            <div className="space-y-1">
                              <div className="font-medium text-xs">
                                {role.display_name}
                              </div>
                              <Checkbox
                                checked={modulePermissions.every(p => 
                                  matrix[role.id]?.[p.name] === true
                                )}
                                onCheckedChange={(checked) => {
                                  modulePermissions.forEach(permission => {
                                    handlePermissionChange(role.id, permission.name, checked as boolean)
                                  })
                                }}
                                title={`Select all ${module} permissions for ${role.display_name}`}
                              />
                            </div>
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {modulePermissions
                        .sort((a, b) => {
                          const actionOrder = ['read', 'create', 'update', 'delete']
                          return actionOrder.indexOf(a.action) - actionOrder.indexOf(b.action)
                        })
                        .map((permission) => (
                          <TableRow key={permission.id}>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${
                                    permission.action === 'create' ? 'bg-green-50 text-green-700' :
                                    permission.action === 'read' ? 'bg-blue-50 text-blue-700' :
                                    permission.action === 'update' ? 'bg-yellow-50 text-yellow-700' :
                                    permission.action === 'delete' ? 'bg-red-50 text-red-700' : ''
                                  }`}
                                >
                                  {ACTION_NAMES[permission.action as keyof typeof ACTION_NAMES]}
                                </Badge>
                                <span className="text-sm font-medium">
                                  {permission.display_name}
                                </span>
                              </div>
                            </TableCell>
                            {activeRoles.map((role) => (
                              <TableCell key={role.id} className="text-center">
                                <Checkbox
                                  checked={matrix[role.id]?.[permission.name] || false}
                                  onCheckedChange={(checked) => 
                                    handlePermissionChange(role.id, permission.name, checked as boolean)
                                  }
                                />
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={() => {
                // Reset to original state
                setMatrix(JSON.parse(JSON.stringify(originalMatrix)))
              }}
            >
              Reset
            </Button>
          </div>
          <div className="flex space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? 'Menyimpan...' : 'Simpan Changes'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
