"use client"

import { useState, useMemo } from "react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts"
import {
  Users,
  UserCheck,
  Plane,
  ArrowLeft,
  Building2,
  MapPin,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  Globe,
  Calendar,
  FileSpreadsheet,
  Eye,
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Sample comprehensive data for Japanese internship placement
const siswaPlacementData = [
  {
    id: 1,
    nama: "<PERSON>",
    gender: "Laki-laki",
    lpk: "LPK Sukses Mandiri Jakarta",
    perusahaanPenerima: "Toyota Motor Corporation",
    kumiai: "Aichi Rodo Kumiai",
    kotaDaerah: "Toyota City",
    prefecture: "Aichi",
    alamatPerusahaan: "1 Toyota-cho, Toyota, Aichi 471-8571",
    blnBrkt: "Maret",
    thnBrkt: 2024,
    blnPlg: "Maret",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "Assembly Line Operator",
    gaji: "¥180,000",
  },
  {
    id: 2,
    nama: "Siti Nurhaliza",
    gender: "Perempuan",
    lpk: "LPK Maju Bersama Bandung",
    perusahaanPenerima: "Honda Manufacturing",
    kumiai: "Saitama Rodo Kumiai",
    kotaDaerah: "Sayama",
    prefecture: "Saitama",
    alamatPerusahaan: "1-1 Honda-cho, Sayama, Saitama 350-1331",
    blnBrkt: "Februari",
    thnBrkt: 2024,
    blnPlg: "Februari",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "Quality Control Inspector",
    gaji: "¥175,000",
  },
  {
    id: 3,
    nama: "Budi Santoso",
    gender: "Laki-laki",
    lpk: "LPK Harapan Bangsa Surabaya",
    perusahaanPenerima: "Panasonic Corporation",
    kumiai: "Osaka Rodo Kumiai",
    kotaDaerah: "Kadoma",
    prefecture: "Osaka",
    alamatPerusahaan: "1006 Oaza Kadoma, Kadoma, Osaka 571-8501",
    blnBrkt: "Januari",
    thnBrkt: 2024,
    blnPlg: "Januari",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "Electronics Assembler",
    gaji: "¥170,000",
  },
  {
    id: 4,
    nama: "Dewi Sartika",
    gender: "Perempuan",
    lpk: "LPK Karya Utama Medan",
    perusahaanPenerima: "Nissan Motor Co.",
    kumiai: "Kanagawa Rodo Kumiai",
    kotaDaerah: "Yokohama",
    prefecture: "Kanagawa",
    alamatPerusahaan: "1-1 Takashima, Nishi-ku, Yokohama 220-8623",
    blnBrkt: "Desember",
    thnBrkt: 2023,
    blnPlg: "Desember",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Production Operator",
    gaji: "¥185,000",
  },
  {
    id: 5,
    nama: "Andi Wijaya",
    gender: "Laki-laki",
    lpk: "LPK Nusantara Makassar",
    perusahaanPenerima: "Mitsubishi Motors",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Minato",
    prefecture: "Tokyo",
    alamatPerusahaan: "33-8 Shiba, Minato-ku, Tokyo 108-8410",
    blnBrkt: "November",
    thnBrkt: 2023,
    blnPlg: "November",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Machine Operator",
    gaji: "¥190,000",
  },
  {
    id: 6,
    nama: "Rini Astuti",
    gender: "Perempuan",
    lpk: "LPK Bina Prestasi Yogyakarta",
    perusahaanPenerima: "Sony Corporation",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Shinagawa",
    prefecture: "Tokyo",
    alamatPerusahaan: "1-7-1 Konan, Minato-ku, Tokyo 108-0075",
    blnBrkt: "Oktober",
    thnBrkt: 2023,
    blnPlg: "Oktober",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Component Inspector",
    gaji: "¥172,000",
  },
  {
    id: 7,
    nama: "Joko Susilo",
    gender: "Laki-laki",
    lpk: "LPK Sukses Mandiri Jakarta",
    perusahaanPenerima: "Subaru Corporation",
    kumiai: "Gunma Rodo Kumiai",
    kotaDaerah: "Ota",
    prefecture: "Gunma",
    alamatPerusahaan: "1-1 Subaru-cho, Ota, Gunma 373-8555",
    blnBrkt: "September",
    thnBrkt: 2023,
    blnPlg: "September",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Welding Specialist",
    gaji: "¥195,000",
  },
  {
    id: 8,
    nama: "Maya Sari",
    gender: "Perempuan",
    lpk: "LPK Maju Bersama Bandung",
    perusahaanPenerima: "Mazda Motor Corporation",
    kumiai: "Hiroshima Rodo Kumiai",
    kotaDaerah: "Hiroshima",
    prefecture: "Hiroshima",
    alamatPerusahaan: "3-1 Shinchi, Fuchu-cho, Aki-gun, Hiroshima 730-8670",
    blnBrkt: "Agustus",
    thnBrkt: 2023,
    blnPlg: "Agustus",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Paint Shop Operator",
    gaji: "¥178,000",
  },
  {
    id: 9,
    nama: "Bambang Hermawan",
    gender: "Laki-laki",
    lpk: "LPK Harapan Bangsa Surabaya",
    perusahaanPenerima: "Yamaha Motor Co.",
    kumiai: "Shizuoka Rodo Kumiai",
    kotaDaerah: "Iwata",
    prefecture: "Shizuoka",
    alamatPerusahaan: "2500 Shingai, Iwata, Shizuoka 438-8501",
    blnBrkt: "Juli",
    thnBrkt: 2023,
    blnPlg: "Juli",
    thnPlg: 2026,
    status: "Aktif",
    posisi: "Engine Assembly",
    gaji: "¥183,000",
  },
  {
    id: 10,
    nama: "Fitri Handayani",
    gender: "Perempuan",
    lpk: "LPK Karya Utama Medan",
    perusahaanPenerima: "Daihatsu Motor Co.",
    kumiai: "Osaka Rodo Kumiai",
    kotaDaerah: "Ikeda",
    prefecture: "Osaka",
    alamatPerusahaan: "1-1 Daihatsu-cho, Ikeda, Osaka 563-8651",
    blnBrkt: "Juni",
    thnBrkt: 2021,
    blnPlg: "Juni",
    thnPlg: 2024,
    status: "Sudah Pulang",
    posisi: "Final Inspection",
    gaji: "¥165,000",
  },
  {
    id: 11,
    nama: "Agus Setiawan",
    gender: "Laki-laki",
    lpk: "LPK Nusantara Makassar",
    perusahaanPenerima: "Isuzu Motors Limited",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Shinagawa",
    prefecture: "Tokyo",
    alamatPerusahaan: "26-1 Minami-oi, Shinagawa-ku, Tokyo 140-8722",
    blnBrkt: "Mei",
    thnBrkt: 2021,
    blnPlg: "Mei",
    thnPlg: 2024,
    status: "Sudah Pulang",
    posisi: "Truck Assembly",
    gaji: "¥188,000",
  },
  {
    id: 12,
    nama: "Lestari Wulandari",
    gender: "Perempuan",
    lpk: "LPK Bina Prestasi Yogyakarta",
    perusahaanPenerima: "Suzuki Motor Corporation",
    kumiai: "Shizuoka Rodo Kumiai",
    kotaDaerah: "Hamamatsu",
    prefecture: "Shizuoka",
    alamatPerusahaan: "300 Takatsuka-cho, Minami-ku, Hamamatsu 432-8611",
    blnBrkt: "April",
    thnBrkt: 2021,
    blnPlg: "April",
    thnPlg: 2024,
    status: "Sudah Pulang",
    posisi: "Motorcycle Assembly",
    gaji: "¥174,000",
  },
  {
    id: 13,
    nama: "Hendra Gunawan",
    gender: "Laki-laki",
    lpk: "LPK Sukses Mandiri Jakarta",
    perusahaanPenerima: "Kawasaki Heavy Industries",
    kumiai: "Hyogo Rodo Kumiai",
    kotaDaerah: "Kobe",
    prefecture: "Hyogo",
    alamatPerusahaan: "1-1 Higashikawasaki-cho, Chuo-ku, Kobe 650-8680",
    blnBrkt: null,
    thnBrkt: null,
    blnPlg: null,
    thnPlg: null,
    status: "Belum Berangkat",
    posisi: "Heavy Machinery",
    gaji: "¥200,000",
  },
  {
    id: 14,
    nama: "Indah Permatasari",
    gender: "Perempuan",
    lpk: "LPK Maju Bersama Bandung",
    perusahaanPenerima: "Mitsubishi Electric Corporation",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Chiyoda",
    prefecture: "Tokyo",
    alamatPerusahaan: "2-7-3 Marunouchi, Chiyoda-ku, Tokyo 100-8310",
    blnBrkt: null,
    thnBrkt: null,
    blnPlg: null,
    thnPlg: null,
    status: "Belum Berangkat",
    posisi: "Electronics Testing",
    gaji: "¥168,000",
  },
  {
    id: 15,
    nama: "Wahyu Pratama",
    gender: "Laki-laki",
    lpk: "LPK Harapan Bangsa Surabaya",
    perusahaanPenerima: "Toshiba Corporation",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Minato",
    prefecture: "Tokyo",
    alamatPerusahaan: "1-1-1 Shibaura, Minato-ku, Tokyo 105-8001",
    blnBrkt: null,
    thnBrkt: null,
    blnPlg: null,
    thnPlg: null,
    status: "Belum Berangkat",
    posisi: "Semiconductor Production",
    gaji: "¥192,000",
  },
  {
    id: 16,
    nama: "Sari Dewi",
    gender: "Perempuan",
    lpk: "LPK Karya Utama Medan",
    perusahaanPenerima: "Canon Inc.",
    kumiai: "Tokyo Rodo Kumiai",
    kotaDaerah: "Ota",
    prefecture: "Tokyo",
    alamatPerusahaan: "30-2 Shimomaruko, Ota-ku, Tokyo 146-8501",
    blnBrkt: "Juni",
    thnBrkt: 2024,
    blnPlg: "Juni",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "Optical Equipment Assembly",
    gaji: "¥176,000",
  },
  {
    id: 17,
    nama: "Dedi Kurniawan",
    gender: "Laki-laki",
    lpk: "LPK Nusantara Makassar",
    perusahaanPenerima: "Fujitsu Limited",
    kumiai: "Kanagawa Rodo Kumiai",
    kotaDaerah: "Kawasaki",
    prefecture: "Kanagawa",
    alamatPerusahaan: "4-1-1 Kamikodanaka, Nakahara-ku, Kawasaki 211-8588",
    blnBrkt: "Mei",
    thnBrkt: 2024,
    blnPlg: "Mei",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "Computer Hardware Assembly",
    gaji: "¥181,000",
  },
  {
    id: 18,
    nama: "Rina Sari",
    gender: "Perempuan",
    lpk: "LPK Bina Prestasi Yogyakarta",
    perusahaanPenerima: "Sharp Corporation",
    kumiai: "Osaka Rodo Kumiai",
    kotaDaerah: "Sakai",
    prefecture: "Osaka",
    alamatPerusahaan: "1 Takumi-cho, Sakai-ku, Sakai, Osaka 590-8522",
    blnBrkt: "April",
    thnBrkt: 2024,
    blnPlg: "April",
    thnPlg: 2027,
    status: "Aktif",
    posisi: "LCD Panel Production",
    gaji: "¥173,000",
  },
]

export default function DashboardPenempatan() {
  const [filterTahun, setFilterTahun] = useState("Semua Tahun")
  const [filterLPK, setFilterLPK] = useState("Semua LPK")
  const [filterKumiai, setFilterKumiai] = useState("Semua Kumiai")

  // Filter data based on selected filters
  const filteredData = useMemo(() => {
    return siswaPlacementData.filter((siswa) => {
      const matchTahun = filterTahun === "Semua Tahun" || siswa.thnBrkt?.toString() === filterTahun
      const matchLPK = filterLPK === "Semua LPK" || siswa.lpk === filterLPK
      const matchKumiai = filterKumiai === "Semua Kumiai" || siswa.kumiai === filterKumiai
      return matchTahun && matchLPK && matchKumiai
    })
  }, [filterTahun, filterLPK, filterKumiai])

  // Calculate comprehensive statistics
  const statistics = useMemo(() => {
    const totalSiswa = filteredData.length
    const siswaWithLPK = filteredData.filter((s) => s.lpk && s.lpk.trim() !== "").length
    const siswaSudahBerangkat = filteredData.filter((s) => s.thnBrkt && s.blnBrkt).length
    const siswaSudahPulang = filteredData.filter((s) => s.status === "Sudah Pulang").length
    const uniquePerusahaan = new Set(filteredData.filter((s) => s.perusahaanPenerima).map((s) => s.perusahaanPenerima))
      .size
    const uniqueKumiai = new Set(filteredData.filter((s) => s.kumiai).map((s) => s.kumiai)).size

    return {
      totalSiswa,
      siswaWithLPK,
      siswaSudahBerangkat,
      siswaSudahPulang,
      uniquePerusahaan,
      uniqueKumiai,
    }
  }, [filteredData])

  // Prepare chart data for year-based departure
  const chartDataTahun = useMemo(() => {
    const tahunCount = filteredData.reduce(
      (acc, siswa) => {
        if (siswa.thnBrkt) {
          acc[siswa.thnBrkt] = (acc[siswa.thnBrkt] || 0) + 1
        }
        return acc
      },
      {} as Record<number, number>,
    )

    return Object.entries(tahunCount)
      .map(([tahun, jumlah]) => ({ tahun, jumlah, fill: "#FFA500" }))
      .sort((a, b) => Number.parseInt(a.tahun) - Number.parseInt(b.tahun))
  }, [filteredData])

  // Prepare chart data for LPK distribution
  const chartDataLPK = useMemo(() => {
    const lpkCount = filteredData.reduce(
      (acc, siswa) => {
        if (siswa.lpk) {
          const lpkName = siswa.lpk.replace("LPK ", "")
          acc[lpkName] = (acc[lpkName] || 0) + 1
        }
        return acc
      },
      {} as Record<string, number>,
    )

    const colors = ["#FFA500", "#FF8C00", "#800000", "#A0522D", "#CD853F", "#8B4513", "#D2691E"]
    return Object.entries(lpkCount).map(([lpk, jumlah], index) => ({
      name: lpk,
      value: jumlah,
      fill: colors[index % colors.length],
    }))
  }, [filteredData])

  // Prepare chart data for gender distribution
  const chartDataGender = useMemo(() => {
    const genderCount = filteredData.reduce(
      (acc, siswa) => {
        acc[siswa.gender] = (acc[siswa.gender] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    return [
      { name: "Laki-laki", value: genderCount["Laki-laki"] || 0, fill: "#FFA500" },
      { name: "Perempuan", value: genderCount["Perempuan"] || 0, fill: "#800000" },
    ]
  }, [filteredData])

  // Get unique values for filters
  const uniqueTahun = [...new Set(siswaPlacementData.map((s) => s.thnBrkt).filter(Boolean))].sort()
  const uniqueLPK = [...new Set(siswaPlacementData.map((s) => s.lpk))]
  const uniqueKumiaiList = [...new Set(siswaPlacementData.map((s) => s.kumiai))]

  // Get latest placement entries (limit to 12)
  const latestPlacements = filteredData
    .filter((s) => s.thnBrkt && s.blnBrkt)
    .sort((a, b) => {
      const dateA = new Date(`${a.blnBrkt} 1, ${a.thnBrkt}`)
      const dateB = new Date(`${b.blnBrkt} 1, ${b.thnBrkt}`)
      return dateB.getTime() - dateA.getTime()
    })
    .slice(0, 12)

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Aktif: { color: "bg-green-100 text-green-800 border-green-200", dot: "bg-green-500" },
      "Sudah Pulang": { color: "bg-blue-100 text-blue-800 border-blue-200", dot: "bg-blue-500" },
      "Belum Berangkat": { color: "bg-orange-100 text-orange-800 border-orange-200", dot: "bg-orange-500" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Belum Berangkat"]

    return (
      <Badge variant="outline" className={`${config.color} font-medium`}>
        <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
        {status}
      </Badge>
    )
  }

  // Prefecture distribution for map
  const prefectureStats = useMemo(() => {
    const prefCount = filteredData.reduce(
      (acc, siswa) => {
        if (siswa.prefecture) {
          acc[siswa.prefecture] = (acc[siswa.prefecture] || 0) + 1
        }
        return acc
      },
      {} as Record<string, number>,
    )

    return Object.entries(prefCount)
      .map(([prefecture, count]) => ({ prefecture, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 6)
  }, [filteredData])

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Penempatan & Keberangkatan Siswa</h1>
            <p className="text-gray-600 mt-1">Monitoring komprehensif penempatan siswa magang di perusahaan Jepang</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center text-sm text-gray-500">
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Data dari Excel Import
            </div>
            <div className="text-sm text-gray-500">
              Update: {new Date().toLocaleDateString("id-ID", { day: "2-digit", month: "short", year: "numeric" })}
            </div>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </div>

        {/* Advanced Filters */}
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-orange-600" />
                <span className="text-base font-semibold text-gray-800">Filter & Analisis Data:</span>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <Select value={filterTahun} onValueChange={setFilterTahun}>
                  <SelectTrigger className="w-full sm:w-[180px] border-orange-200 focus:border-orange-500">
                    <Calendar className="h-4 w-4 mr-2 text-orange-600" />
                    <SelectValue placeholder="Pilih Tahun" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Semua Tahun">Semua Tahun</SelectItem>
                    {uniqueTahun.map((tahun) => (
                      <SelectItem key={tahun} value={tahun.toString()}>
                        {tahun}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filterLPK} onValueChange={setFilterLPK}>
                  <SelectTrigger className="w-full sm:w-[220px] border-orange-200 focus:border-orange-500">
                    <Building2 className="h-4 w-4 mr-2 text-orange-600" />
                    <SelectValue placeholder="Pilih LPK" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Semua LPK">Semua LPK</SelectItem>
                    {uniqueLPK.map((lpk) => (
                      <SelectItem key={lpk} value={lpk}>
                        {lpk.replace("LPK ", "")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filterKumiai} onValueChange={setFilterKumiai}>
                  <SelectTrigger className="w-full sm:w-[220px] border-orange-200 focus:border-orange-500">
                    <Globe className="h-4 w-4 mr-2 text-orange-600" />
                    <SelectValue placeholder="Pilih Kumiai" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Semua Kumiai">Semua Kumiai</SelectItem>
                    {uniqueKumiaiList.map((kumiai) => (
                      <SelectItem key={kumiai} value={kumiai}>
                        {kumiai}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Enhanced Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Siswa Terdaftar</CardTitle>
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
                <Users className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.totalSiswa.toLocaleString()}</div>
              <p className="text-xs text-blue-600 flex items-center mt-1">Siswa dalam database</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Siswa dengan LPK</CardTitle>
              <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
                <UserCheck className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.siswaWithLPK.toLocaleString()}</div>
              <p className="text-xs text-green-600 flex items-center mt-1">
                {Math.round((statistics.siswaWithLPK / statistics.totalSiswa) * 100)}% dari total
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Sudah Berangkat</CardTitle>
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
                <Plane className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.siswaSudahBerangkat.toLocaleString()}</div>
              <p className="text-xs text-orange-600 flex items-center mt-1">
                {Math.round((statistics.siswaSudahBerangkat / statistics.totalSiswa) * 100)}% telah diberangkatkan
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-maroon-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Sudah Pulang</CardTitle>
              <div className="bg-gradient-to-r from-maroon-600 to-maroon-700 p-2 rounded-lg">
                <ArrowLeft className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.siswaSudahPulang.toLocaleString()}</div>
              <p className="text-xs text-maroon-600 flex items-center mt-1">Program selesai</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Perusahaan Penerima</CardTitle>
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-2 rounded-lg">
                <Building2 className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.uniquePerusahaan.toLocaleString()}</div>
              <p className="text-xs text-purple-600 flex items-center mt-1">Perusahaan mitra aktif</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-teal-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Kumiai Unik</CardTitle>
              <div className="bg-gradient-to-r from-teal-500 to-teal-600 p-2 rounded-lg">
                <Globe className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{statistics.uniqueKumiai.toLocaleString()}</div>
              <p className="text-xs text-teal-600 flex items-center mt-1">Organisasi pekerja</p>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Bar Chart - Enhanced */}
          <Card className="lg:col-span-2 border-l-4 border-l-orange-500">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-orange-600" />
                Trend Pemberangkatan per Tahun
              </CardTitle>
              <CardDescription>Analisis distribusi keberangkatan siswa berdasarkan tahun</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  jumlah: {
                    label: "Jumlah Siswa",
                    color: "#FFA500",
                  },
                }}
                className="h-[320px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartDataTahun} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="tahun" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="jumlah" radius={[6, 6, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Pie Chart - Gender Enhanced */}
          <Card className="border-l-4 border-l-maroon-500">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Distribusi Gender</CardTitle>
              <CardDescription>Komposisi siswa berdasarkan jenis kelamin</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  value: {
                    label: "Jumlah",
                  },
                }}
                className="h-[320px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartDataGender}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent, value }) => `${name}\n${value} (${(percent * 100).toFixed(0)}%)`}
                      outerRadius={90}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartDataGender.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* LPK Distribution Chart - Enhanced */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">Distribusi Siswa per LPK</CardTitle>
            <CardDescription>Analisis komposisi siswa berdasarkan Lembaga Pelatihan Kerja mitra</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                value: {
                  label: "Jumlah Siswa",
                },
              }}
              className="h-[400px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartDataLPK}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent, value }) => `${name}\n${value} siswa (${(percent * 100).toFixed(0)}%)`}
                    outerRadius={130}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartDataLPK.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Enhanced Latest Placements Table */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Penempatan Siswa Terbaru</CardTitle>
                <CardDescription>
                  {latestPlacements.length} penempatan terbaru dari {filteredData.length} total data
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Lihat Semua
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-orange-50 to-maroon-50">
                    <TableHead className="font-semibold text-gray-800">Nama Siswa</TableHead>
                    <TableHead className="font-semibold text-gray-800">Gender</TableHead>
                    <TableHead className="font-semibold text-gray-800">LPK</TableHead>
                    <TableHead className="font-semibold text-gray-800">Perusahaan Penerima</TableHead>
                    <TableHead className="font-semibold text-gray-800">Kumiai</TableHead>
                    <TableHead className="font-semibold text-gray-800">Lokasi</TableHead>
                    <TableHead className="font-semibold text-gray-800">Posisi</TableHead>
                    <TableHead className="font-semibold text-gray-800">Gaji</TableHead>
                    <TableHead className="font-semibold text-gray-800">Tgl Berangkat</TableHead>
                    <TableHead className="font-semibold text-gray-800">Tgl Pulang</TableHead>
                    <TableHead className="font-semibold text-gray-800">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {latestPlacements.map((siswa) => (
                    <TableRow key={siswa.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{siswa.nama}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {siswa.gender}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">{siswa.lpk.replace("LPK ", "")}</TableCell>
                      <TableCell className="text-sm font-medium">{siswa.perusahaanPenerima}</TableCell>
                      <TableCell className="text-sm">{siswa.kumiai}</TableCell>
                      <TableCell className="text-sm">
                        <div className="flex flex-col">
                          <span className="font-medium">{siswa.kotaDaerah}</span>
                          <span className="text-xs text-gray-500">{siswa.prefecture}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">{siswa.posisi}</TableCell>
                      <TableCell className="text-sm font-mono">{siswa.gaji}</TableCell>
                      <TableCell className="text-sm">
                        {siswa.blnBrkt && siswa.thnBrkt ? (
                          <div className="flex flex-col">
                            <span>{siswa.blnBrkt}</span>
                            <span className="text-xs text-gray-500">{siswa.thnBrkt}</span>
                          </div>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell className="text-sm">
                        {siswa.blnPlg && siswa.thnPlg ? (
                          <div className="flex flex-col">
                            <span>{siswa.blnPlg}</span>
                            <span className="text-xs text-gray-500">{siswa.thnPlg}</span>
                          </div>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>{getStatusBadge(siswa.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Enhanced Empty State */}
            {latestPlacements.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada data penempatan</h3>
                <p className="text-gray-500">Belum ada siswa yang berangkat dengan filter yang dipilih.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Interactive Map Placeholder */}
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
              <MapPin className="h-5 w-5 mr-2 text-purple-600" />
              Peta Penempatan Siswa di Jepang
            </CardTitle>
            <CardDescription>
              Distribusi geografis siswa berdasarkan prefecture dan lokasi perusahaan penerima
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center border-2 border-dashed border-purple-200">
              <div className="text-center max-w-4xl">
                <MapPin className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-purple-600 mb-2">Peta Interaktif Jepang</h3>
                <p className="text-purple-500 text-sm mb-6">
                  Visualisasi lokasi penempatan siswa berdasarkan prefecture dan alamat perusahaan penerima dengan pin
                  interaktif
                </p>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 text-xs">
                  {prefectureStats.map((stat, index) => {
                    const colors = ["orange-500", "maroon-600", "blue-500", "green-500", "purple-500", "teal-500"]
                    const colorClass = colors[index % colors.length]
                    return (
                      <div key={stat.prefecture} className="bg-white p-3 rounded-lg shadow-sm border">
                        <div className="flex items-center mb-2">
                          <div className={`w-3 h-3 bg-${colorClass} rounded-full mr-2`}></div>
                          <span className="font-medium text-gray-800">{stat.prefecture}</span>
                        </div>
                        <div className="text-center">
                          <span className="text-lg font-bold text-gray-900">{stat.count}</span>
                          <span className="text-gray-600 ml-1">siswa</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
                <div className="mt-6 text-xs text-gray-500">
                  * Peta akan menampilkan pin lokasi dengan detail perusahaan dan jumlah siswa per lokasi
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
