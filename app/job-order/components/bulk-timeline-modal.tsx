"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Loader2, Users, Calendar, CheckCircle, X, AlertTriangle } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { TimelineService } from "@/lib/services"

interface BulkTimelineModalProps {
  isOpen: boolean
  onClose: () => void
  students: Array<{
    id: string
    nama: string
    lpkMitra: string
    statusSeleksi: string
  }>
  onSuccess: () => void
}

const statusOptions = [
  { value: "belum_mulai", label: "Belum Mulai", color: "bg-gray-100 text-gray-600" },
  { value: "berlangsung", label: "Berlangsung", color: "bg-orange-100 text-orange-800" },
  { value: "selesai", label: "Selesai", color: "bg-green-100 text-green-800" },
  { value: "dibatalkan", label: "Tidak Lolos", color: "bg-red-100 text-red-800" },
]

const timelineStages = [
  { value: "seleksi_administrasi", label: "Seleksi Administrasi" },
  { value: "wawancara", label: "Wawancara" },
  { value: "pemberkasan", label: "Pemberkasan" },
  { value: "pemberangkatan", label: "Pemberangkatan" },
]

export default function BulkTimelineModal({ 
  isOpen, 
  onClose, 
  students, 
  onSuccess 
}: BulkTimelineModalProps) {
  const [loading, setLoading] = useState(false)
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [selectedStage, setSelectedStage] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("")
  const [catatan, setCatatan] = useState("")
  const [timelineStageId, setTimelineStageId] = useState("")

  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setSelectedStudents([])
      setSelectedStage("")
      setSelectedStatus("")
      setCatatan("")
      setTimelineStageId("")
    }
  }, [isOpen])

  // Fetch timeline stages to get IDs
  useEffect(() => {
    const fetchStages = async () => {
      try {
        // You would need to implement this in TimelineService
        // For now, we'll use hardcoded stage names
      } catch (error) {
        console.error('Error fetching stages:', error)
      }
    }
    
    if (isOpen) {
      fetchStages()
    }
  }, [isOpen])

  const handleStudentSelection = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudents(prev => [...prev, studentId])
    } else {
      setSelectedStudents(prev => prev.filter(id => id !== studentId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(students.map(student => student.id))
    } else {
      setSelectedStudents([])
    }
  }

  const handleBulkUpdate = async () => {
    if (selectedStudents.length === 0) {
      toast({
        title: "Peringatan",
        description: "Pilih minimal satu siswa untuk diupdate",
        variant: "destructive",
      })
      return
    }

    if (!selectedStage || !selectedStatus) {
      toast({
        title: "Peringatan", 
        description: "Pilih tahapan dan status yang akan diupdate",
        variant: "destructive",
      })
      return
    }

    try {
      setLoading(true)
      
      // Get stage ID based on selected stage
      const stageMap: { [key: string]: string } = {
        "seleksi_administrasi": "Seleksi Administrasi",
        "wawancara": "Wawancara", 
        "pemberkasan": "Pemberkasan",
        "pemberangkatan": "Pemberangkatan ke Jepang"
      }

      const stageName = stageMap[selectedStage]
      
      // For each selected student, update their timeline
      const updatePromises = selectedStudents.map(async (studentId) => {
        try {
          // Get timeline stages first to find the correct stage ID
          const timeline = await TimelineService.getStudentTimeline(studentId)
          const targetStage = timeline.find(stage => stage.nama_stage === stageName)
          
          if (targetStage) {
            const updates: any = {
              status: selectedStatus,
              catatan: catatan || undefined,
            }

            // Set dates based on status
            if (selectedStatus === "berlangsung" || selectedStatus === "selesai") {
              updates.tanggal_mulai = new Date().toISOString().split('T')[0]
            }
            
            if (selectedStatus === "selesai") {
              updates.tanggal_selesai = new Date().toISOString().split('T')[0]
            }

            await TimelineService.updateTimelineProgress(studentId, targetStage.id, updates)
          }
        } catch (error) {
          console.error(`Error updating student ${studentId}:`, error)
          throw error
        }
      })
      
      await Promise.all(updatePromises)
      
      toast({
        title: "Berhasil",
        description: `Timeline berhasil diupdate untuk ${selectedStudents.length} siswa`,
      })
      
      onSuccess()
      onClose()
    } catch (error: any) {
      console.error('Error bulk updating timeline:', error)
      toast({
        title: "Error",
        description: error.message || "Gagal mengupdate timeline siswa",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusOption = statusOptions.find(opt => opt.value === status) || statusOptions[0]
    return (
      <Badge variant="outline" className={statusOption.color}>
        {statusOption.label}
      </Badge>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900">
            Bulk Update Timeline Siswa
          </DialogTitle>
          <p className="text-sm text-gray-600 mt-2">
            Update status timeline untuk beberapa siswa sekaligus
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Update Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Pengaturan Update</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="stage">Tahapan Timeline</Label>
                  <Select value={selectedStage} onValueChange={setSelectedStage}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih tahapan..." />
                    </SelectTrigger>
                    <SelectContent>
                      {timelineStages.map(stage => (
                        <SelectItem key={stage.value} value={stage.value}>
                          {stage.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status">Status Baru</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih status..." />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className={option.color}>
                              {option.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="catatan">Catatan (Opsional)</Label>
                <Textarea
                  id="catatan"
                  value={catatan}
                  onChange={(e) => setCatatan(e.target.value)}
                  placeholder="Tambahkan catatan untuk update ini..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Student Selection */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Pilih Siswa</CardTitle>
                <Badge variant="outline">
                  {selectedStudents.length} dari {students.length} siswa dipilih
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedStudents.length === students.length && students.length > 0}
                          onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                        />
                      </TableHead>
                      <TableHead className="font-semibold">Nama Siswa</TableHead>
                      <TableHead className="font-semibold">LPK</TableHead>
                      <TableHead className="font-semibold">Status Saat Ini</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {students.map((student) => (
                      <TableRow key={student.id} className="hover:bg-gray-50">
                        <TableCell>
                          <Checkbox
                            checked={selectedStudents.includes(student.id)}
                            onCheckedChange={(checked) => handleStudentSelection(student.id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{student.nama}</TableCell>
                        <TableCell className="text-sm">{student.lpkMitra}</TableCell>
                        <TableCell>{getStatusBadge(student.statusSeleksi)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          {selectedStudents.length > 0 && selectedStage && selectedStatus && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  <span className="font-medium text-orange-900">Preview Update</span>
                </div>
                <p className="text-sm text-orange-800">
                  {selectedStudents.length} siswa akan diupdate tahapan{" "}
                  <strong>{timelineStages.find(s => s.value === selectedStage)?.label}</strong>{" "}
                  menjadi status{" "}
                  <strong>{statusOptions.find(s => s.value === selectedStatus)?.label}</strong>
                  {catatan && (
                    <>
                      {" "}dengan catatan: <em>"{catatan}"</em>
                    </>
                  )}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Batal
          </Button>
          <Button 
            onClick={handleBulkUpdate} 
            disabled={loading || selectedStudents.length === 0 || !selectedStage || !selectedStatus}
            className="bg-orange-500 hover:bg-orange-600"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Mengupdate...
              </>
            ) : (
              `Update ${selectedStudents.length} Siswa`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 