"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Loader2, Search, Users, GraduationCap, MapPin, Calendar, CheckCircle, AlertCircle } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { JobOrderService, TimelineService } from "@/lib/services"
import { <PERSON>s<PERSON> } from "@/lib/types/database"
import { TimelineStats } from "@/lib/types/timeline"

interface StudentSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  jobOrderId: string
  onSuccess: () => void
}

interface StudentWithTimeline extends Siswa {
  lpk_mitra?: {
    nama_lpk: string
    kota: string
  }
  timeline_progress?: TimelineStats
}

export default function StudentSelectionModal({ 
  isOpen, 
  onClose, 
  jobOrderId, 
  onSuccess 
}: StudentSelectionModalProps) {
  const [loading, setLoading] = useState(false)
  const [assigning, setAssigning] = useState(false)
  const [availableStudents, setAvailableStudents] = useState<StudentWithTimeline[]>([])
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [jobOrder, setJobOrder] = useState<any>(null)

  useEffect(() => {
    if (isOpen && jobOrderId) {
      fetchData()
    }
  }, [isOpen, jobOrderId])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch job order details
      const jobOrderData = await JobOrderService.getById(jobOrderId)
      setJobOrder(jobOrderData)

      // Fetch available students
      const studentsData = await JobOrderService.getAvailableStudents(jobOrderId)
      
      // Fetch timeline progress for each student
      const studentsWithTimeline = await Promise.all(
        studentsData.map(async (student: Siswa) => {
          try {
            const timelineStats = await TimelineService.getStudentTimelineStats(student.id)
            return {
              ...student,
              timeline_progress: timelineStats
            }
          } catch (error) {
            console.error(`Error fetching timeline for student ${student.id}:`, error)
            return {
              ...student,
              timeline_progress: {
                progress_percentage: 0,
                current_stage: 'Unknown',
                completed_stages: 0,
                total_stages: 9
              }
            }
          }
        })
      )

      setAvailableStudents(studentsWithTimeline)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Gagal memuat data siswa yang tersedia",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredStudents = availableStudents.filter(student =>
    student.nama_lengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.nik.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.lpk_mitra?.nama_lpk.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleStudentSelection = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedStudents(prev => [...prev, studentId])
    } else {
      setSelectedStudents(prev => prev.filter(id => id !== studentId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(filteredStudents.map(student => student.id))
    } else {
      setSelectedStudents([])
    }
  }

  const handleAssignStudents = async () => {
    if (selectedStudents.length === 0) {
      toast({
        title: "Peringatan",
        description: "Pilih minimal satu siswa untuk ditugaskan",
        variant: "destructive",
      })
      return
    }

    try {
      setAssigning(true)
      
      await JobOrderService.assignStudents(jobOrderId, selectedStudents)
      
      toast({
        title: "Berhasil",
        description: `${selectedStudents.length} siswa berhasil ditugaskan ke job order`,
      })
      
      onSuccess()
      onClose()
    } catch (error: any) {
      console.error('Error assigning students:', error)
      toast({
        title: "Error",
        description: error.message || "Gagal menugaskan siswa ke job order",
        variant: "destructive",
      })
    } finally {
      setAssigning(false)
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500"
    if (percentage >= 60) return "bg-yellow-500"
    if (percentage >= 40) return "bg-orange-500"
    return "bg-red-500"
  }

  const getStageColor = (stage: string) => {
    const stageColors: { [key: string]: string } = {
      'Pre-seleksi': 'bg-blue-100 text-blue-800',
      'Pendidikan Pra-Diklat': 'bg-purple-100 text-purple-800',
      'Pendidikan Diklat': 'bg-purple-100 text-purple-800',
      'Seleksi Administrasi': 'bg-yellow-100 text-yellow-800',
      'Wawancara': 'bg-orange-100 text-orange-800',
      'Pemberkasan': 'bg-green-100 text-green-800',
      'Pendidikan Pasca-Diklat': 'bg-indigo-100 text-indigo-800',
      'Surat Rekomendasi Disnaker': 'bg-teal-100 text-teal-800',
      'Pemberangkatan ke Jepang': 'bg-red-100 text-red-800'
    }
    return stageColors[stage] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900">
            Pilih Siswa untuk Job Order
          </DialogTitle>
          {jobOrder && (
            <div className="text-sm text-gray-600 mt-2">
              <p><strong>Job Order:</strong> {jobOrder.judul_pekerjaan}</p>
              <p><strong>Perusahaan:</strong> {jobOrder.perusahaan_penerima?.nama_perusahaan}</p>
              <p><strong>Kuota Tersedia:</strong> {jobOrder.jumlah_kuota - jobOrder.kuota_terisi} dari {jobOrder.jumlah_kuota}</p>
            </div>
          )}
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Summary */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Cari siswa berdasarkan nama, NIK, atau LPK..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Total: {filteredStudents.length} siswa</span>
              <span>Dipilih: {selectedStudents.length}</span>
            </div>
          </div>

          {/* Students Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-orange-600" />
                Siswa yang Tersedia
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
                  <span className="ml-2">Memuat data siswa...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedStudents.length === filteredStudents.length && filteredStudents.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="font-semibold">Siswa</TableHead>
                        <TableHead className="font-semibold">LPK</TableHead>
                        <TableHead className="font-semibold">Pendidikan</TableHead>
                        <TableHead className="font-semibold">Timeline Progress</TableHead>
                        <TableHead className="font-semibold">Current Stage</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.length > 0 ? (
                        filteredStudents.map((student) => (
                          <TableRow key={student.id} className="hover:bg-gray-50">
                            <TableCell>
                                                             <Checkbox
                                 checked={selectedStudents.includes(student.id)}
                                 onCheckedChange={(checked) => handleStudentSelection(student.id, checked as boolean)}
                               />
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium text-gray-900">{student.nama_lengkap}</div>
                                <div className="text-sm text-gray-500">NIK: {student.nik}</div>
                                <div className="text-sm text-gray-500">
                                  {student.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'} • 
                                  {student.kota_kabupaten}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium text-gray-900">
                                  {student.lpk_mitra?.nama_lpk || 'N/A'}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {student.lpk_mitra?.kota || ''}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div className="font-medium">{student.pendidikan_terakhir}</div>
                                {student.nama_sekolah && (
                                  <div className="text-gray-500">{student.nama_sekolah}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <Progress 
                                    value={student.timeline_progress?.progress_percentage || 0} 
                                    className="h-2 flex-1"
                                  />
                                  <span className="text-sm font-medium">
                                    {student.timeline_progress?.progress_percentage || 0}%
                                  </span>
                                </div>
                                <div className="text-xs text-gray-500">
                                  {student.timeline_progress?.completed_stages || 0} dari {student.timeline_progress?.total_stages || 9} tahap
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className={getStageColor(student.timeline_progress?.current_stage || '')}>
                                {student.timeline_progress?.current_stage || 'Unknown'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-12">
                            <div className="flex flex-col items-center gap-2">
                              <AlertCircle className="h-12 w-12 text-gray-400" />
                              <h3 className="text-lg font-medium text-gray-900">
                                {searchTerm ? 'Tidak ada siswa yang sesuai' : 'Tidak ada siswa yang tersedia'}
                              </h3>
                              <p className="text-gray-500">
                                {searchTerm 
                                  ? 'Coba ubah kata kunci pencarian'
                                  : 'Semua siswa sudah berangkat ke Jepang atau belum memenuhi syarat'
                                }
                              </p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selected Students Summary */}
          {selectedStudents.length > 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-orange-600" />
                    <span className="font-medium text-orange-900">
                      {selectedStudents.length} siswa dipilih
                    </span>
                  </div>
                  <div className="text-sm text-orange-700">
                    {jobOrder && selectedStudents.length > (jobOrder.jumlah_kuota - jobOrder.kuota_terisi) && (
                      <span className="text-red-600">
                        ⚠️ Melebihi kuota yang tersedia ({jobOrder.jumlah_kuota - jobOrder.kuota_terisi})
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Batal
          </Button>
          <Button 
            onClick={handleAssignStudents} 
            disabled={assigning || selectedStudents.length === 0}
            className="bg-orange-500 hover:bg-orange-600"
          >
            {assigning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Menugaskan...
              </>
            ) : (
              `Tugaskan ${selectedStudents.length} Siswa`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 