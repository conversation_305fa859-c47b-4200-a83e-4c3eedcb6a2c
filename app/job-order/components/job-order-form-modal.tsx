"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Loader2, Building2, Users, DollarSign, Calendar, Clock, MapPin, Star } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { JobOrderService, PerusahaanService, KumiaiService } from "@/lib/services"
import { JobOrder, PerusahaanPenerima, Kumiai, JobStatus, EducationLevel, GenderType } from "@/lib/types/database"

interface JobOrderFormModalProps {
  isOpen: boolean
  onClose: () => void
  jobOrder?: JobOrder | null
  onSuccess: () => void
}

interface JobOrderFormData {
  perusahaan_id: string
  kumiai_id: string
  judul_pekerjaan: string
  deskripsi_pekerjaan: string
  posisi: string
  bidang_kerja: string
  jenis_kelamin: string
  usia_min: number
  usia_max: number
  pendidikan_min: EducationLevel
  pengalaman_kerja: string
  keahlian_khusus: string
  gaji_pokok: number
  tunjangan: number
  jam_kerja_per_hari: number
  hari_kerja_per_minggu: number
  overtime_available: boolean
  akomodasi: string
  transportasi: string
  asuransi: string
  fasilitas_lain: string
  jumlah_kuota: number
  status: JobStatus
  tanggal_buka: string
  tanggal_tutup: string
}

export default function JobOrderFormModal({ isOpen, onClose, jobOrder, onSuccess }: JobOrderFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<JobOrderFormData>({
    perusahaan_id: "",
    kumiai_id: "",
    judul_pekerjaan: "",
    deskripsi_pekerjaan: "",
    posisi: "",
    bidang_kerja: "",
    jenis_kelamin: "",
    usia_min: 18,
    usia_max: 35,
    pendidikan_min: "SMA" as EducationLevel,
    pengalaman_kerja: "",
    keahlian_khusus: "",
    gaji_pokok: 0,
    tunjangan: 0,
    jam_kerja_per_hari: 8,
    hari_kerja_per_minggu: 5,
    overtime_available: false,
    akomodasi: "",
    transportasi: "",
    asuransi: "",
    fasilitas_lain: "",
    jumlah_kuota: 1,
    status: "draft" as JobStatus,
    tanggal_buka: "",
    tanggal_tutup: "",
  })

  const [perusahaanList, setPerusahaanList] = useState<PerusahaanPenerima[]>([])
  const [kumiaiList, setKumiaiList] = useState<Kumiai[]>([])
  const [selectedPerusahaan, setSelectedPerusahaan] = useState<PerusahaanPenerima | null>(null)
  const [selectedKumiai, setSelectedKumiai] = useState<Kumiai | null>(null)

  useEffect(() => {
    if (isOpen) {
      fetchData()
      if (jobOrder) {
        populateFormData(jobOrder)
      } else {
        resetForm()
      }
    }
  }, [isOpen, jobOrder])

  const fetchData = async () => {
    try {
      const [perusahaanData, kumiaiData] = await Promise.all([
        PerusahaanService.getAll({ status: 'aktif' }),
        KumiaiService.getAll({ status: 'aktif' })
      ])
      setPerusahaanList(perusahaanData)
      setKumiaiList(kumiaiData)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Gagal memuat data perusahaan dan kumiai",
        variant: "destructive",
      })
    }
  }

  const populateFormData = (data: JobOrder) => {
    setFormData({
      perusahaan_id: data.perusahaan_id,
      kumiai_id: data.kumiai_id,
      judul_pekerjaan: data.judul_pekerjaan,
      deskripsi_pekerjaan: data.deskripsi_pekerjaan,
      posisi: data.posisi,
      bidang_kerja: data.bidang_kerja,
      jenis_kelamin: data.jenis_kelamin,
      usia_min: data.usia_min,
      usia_max: data.usia_max,
      pendidikan_min: data.pendidikan_min,
      pengalaman_kerja: data.pengalaman_kerja || "",
      keahlian_khusus: data.keahlian_khusus || "",
      gaji_pokok: data.gaji_pokok,
      tunjangan: data.tunjangan,
      jam_kerja_per_hari: data.jam_kerja_per_hari,
      hari_kerja_per_minggu: data.hari_kerja_per_minggu,
      overtime_available: data.overtime_available,
      akomodasi: data.akomodasi || "",
      transportasi: data.transportasi || "",
      asuransi: data.asuransi || "",
      fasilitas_lain: data.fasilitas_lain || "",
      jumlah_kuota: data.jumlah_kuota,
      status: data.status,
      tanggal_buka: data.tanggal_buka,
      tanggal_tutup: data.tanggal_tutup,
    })
  }

  const resetForm = () => {
    setFormData({
      perusahaan_id: "",
      kumiai_id: "",
      judul_pekerjaan: "",
      deskripsi_pekerjaan: "",
      posisi: "",
      bidang_kerja: "",
      jenis_kelamin: "",
      usia_min: 18,
      usia_max: 35,
      pendidikan_min: "SMA",
      pengalaman_kerja: "",
      keahlian_khusus: "",
      gaji_pokok: 0,
      tunjangan: 0,
      jam_kerja_per_hari: 8,
      hari_kerja_per_minggu: 5,
      overtime_available: false,
      akomodasi: "",
      transportasi: "",
      asuransi: "",
      fasilitas_lain: "",
      jumlah_kuota: 1,
      status: "draft",
      tanggal_buka: "",
      tanggal_tutup: "",
    })
    setSelectedPerusahaan(null)
    setSelectedKumiai(null)
  }

  const handlePerusahaanChange = (perusahaanId: string) => {
    setFormData(prev => ({ ...prev, perusahaan_id: perusahaanId }))
    const perusahaan = perusahaanList.find(p => p.id === perusahaanId)
    setSelectedPerusahaan(perusahaan || null)
    
    if (perusahaan) {
      setFormData(prev => ({ ...prev, kumiai_id: perusahaan.kumiai_id }))
      const kumiai = kumiaiList.find(k => k.id === perusahaan.kumiai_id)
      setSelectedKumiai(kumiai || null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (jobOrder) {
        // Update existing job order
        await JobOrderService.update(jobOrder.id, formData)
        toast({
          title: "Berhasil",
          description: "Job Order berhasil diperbarui",
        })
      } else {
        // Create new job order
        await JobOrderService.create({
          ...formData,
          kuota_terisi: 0
        })
        toast({
          title: "Berhasil",
          description: "Job Order berhasil dibuat",
        })
      }
      
      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error saving job order:', error)
      toast({
        title: "Error",
        description: "Gagal menyimpan Job Order",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900">
            {jobOrder ? "Edit Job Order" : "Tambah Job Order Baru"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Company Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Building2 className="h-5 w-5 text-orange-600" />
                Informasi Perusahaan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="perusahaan_id">Perusahaan Penerima *</Label>
                  <Select value={formData.perusahaan_id} onValueChange={handlePerusahaanChange} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih perusahaan..." />
                    </SelectTrigger>
                    <SelectContent>
                      {perusahaanList.map(perusahaan => (
                        <SelectItem key={perusahaan.id} value={perusahaan.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{perusahaan.nama_perusahaan}</span>
                            <span className="text-sm text-gray-500">{perusahaan.prefektur}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="kumiai_id">Kumiai</Label>
                  <Select value={formData.kumiai_id} onValueChange={(value) => setFormData(prev => ({ ...prev, kumiai_id: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih kumiai..." />
                    </SelectTrigger>
                    <SelectContent>
                      {kumiaiList.map(kumiai => (
                        <SelectItem key={kumiai.id} value={kumiai.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{kumiai.nama_kumiai}</span>
                            <span className="text-sm text-gray-500">{kumiai.kode_kumiai}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {selectedPerusahaan && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Detail Perusahaan</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>Alamat:</strong> {selectedPerusahaan.alamat_jepang}</p>
                    <p><strong>Kota:</strong> {selectedPerusahaan.kota_jepang}</p>
                    <p><strong>Bidang Usaha:</strong> {selectedPerusahaan.bidang_usaha}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Job Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-orange-600" />
                Informasi Pekerjaan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="judul_pekerjaan">Judul Pekerjaan *</Label>
                  <Input
                    id="judul_pekerjaan"
                    value={formData.judul_pekerjaan}
                    onChange={(e) => setFormData(prev => ({ ...prev, judul_pekerjaan: e.target.value }))}
                    required
                    placeholder="Masukkan judul pekerjaan"
                  />
                </div>

                <div>
                  <Label htmlFor="posisi">Posisi *</Label>
                  <Input
                    id="posisi"
                    value={formData.posisi}
                    onChange={(e) => setFormData(prev => ({ ...prev, posisi: e.target.value }))}
                    required
                    placeholder="Masukkan posisi"
                  />
                </div>

                <div>
                  <Label htmlFor="bidang_kerja">Bidang Kerja *</Label>
                  <Input
                    id="bidang_kerja"
                    value={formData.bidang_kerja}
                    onChange={(e) => setFormData(prev => ({ ...prev, bidang_kerja: e.target.value }))}
                    required
                    placeholder="Masukkan bidang kerja"
                  />
                </div>

                <div>
                  <Label htmlFor="jumlah_kuota">Jumlah Kuota *</Label>
                  <Input
                    id="jumlah_kuota"
                    type="number"
                    min="1"
                    value={formData.jumlah_kuota}
                    onChange={(e) => setFormData(prev => ({ ...prev, jumlah_kuota: parseInt(e.target.value) }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="deskripsi_pekerjaan">Deskripsi Pekerjaan *</Label>
                <Textarea
                  id="deskripsi_pekerjaan"
                  value={formData.deskripsi_pekerjaan}
                  onChange={(e) => setFormData(prev => ({ ...prev, deskripsi_pekerjaan: e.target.value }))}
                  required
                  rows={3}
                  placeholder="Masukkan deskripsi pekerjaan"
                />
              </div>
            </CardContent>
          </Card>

          {/* Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Star className="h-5 w-5 text-orange-600" />
                Persyaratan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="jenis_kelamin">Jenis Kelamin *</Label>
                  <Select value={formData.jenis_kelamin} onValueChange={(value) => setFormData(prev => ({ ...prev, jenis_kelamin: value }))} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih jenis kelamin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="L">Laki-laki</SelectItem>
                      <SelectItem value="P">Perempuan</SelectItem>
                      <SelectItem value="L/P">Laki-laki/Perempuan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="usia_min">Usia Min *</Label>
                  <Input
                    id="usia_min"
                    type="number"
                    min="17"
                    max="40"
                    value={formData.usia_min}
                    onChange={(e) => setFormData(prev => ({ ...prev, usia_min: parseInt(e.target.value) }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="usia_max">Usia Max *</Label>
                  <Input
                    id="usia_max"
                    type="number"
                    min="17"
                    max="40"
                    value={formData.usia_max}
                    onChange={(e) => setFormData(prev => ({ ...prev, usia_max: parseInt(e.target.value) }))}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pendidikan_min">Pendidikan Minimal *</Label>
                  <Select value={formData.pendidikan_min} onValueChange={(value) => setFormData(prev => ({ ...prev, pendidikan_min: value }))} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih pendidikan minimal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SD">SD</SelectItem>
                      <SelectItem value="SMP">SMP</SelectItem>
                      <SelectItem value="SMA">SMA</SelectItem>
                      <SelectItem value="SMK">SMK</SelectItem>
                      <SelectItem value="D3">D3</SelectItem>
                      <SelectItem value="S1">S1</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="pengalaman_kerja">Pengalaman Kerja</Label>
                  <Input
                    id="pengalaman_kerja"
                    value={formData.pengalaman_kerja}
                    onChange={(e) => setFormData(prev => ({ ...prev, pengalaman_kerja: e.target.value }))}
                    placeholder="Masukkan pengalaman kerja"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="keahlian_khusus">Keahlian Khusus</Label>
                <Textarea
                  id="keahlian_khusus"
                  value={formData.keahlian_khusus}
                  onChange={(e) => setFormData(prev => ({ ...prev, keahlian_khusus: e.target.value }))}
                  rows={2}
                  placeholder="Masukkan keahlian khusus yang dibutuhkan"
                />
              </div>
            </CardContent>
          </Card>

          {/* Compensation & Benefits */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-orange-600" />
                Gaji & Fasilitas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="gaji_pokok">Gaji Pokok (¥) *</Label>
                  <Input
                    id="gaji_pokok"
                    type="number"
                    min="0"
                    value={formData.gaji_pokok}
                    onChange={(e) => setFormData(prev => ({ ...prev, gaji_pokok: parseInt(e.target.value) }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="tunjangan">Tunjangan (¥)</Label>
                  <Input
                    id="tunjangan"
                    type="number"
                    min="0"
                    value={formData.tunjangan}
                    onChange={(e) => setFormData(prev => ({ ...prev, tunjangan: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="jam_kerja_per_hari">Jam Kerja/Hari *</Label>
                  <Input
                    id="jam_kerja_per_hari"
                    type="number"
                    min="1"
                    max="12"
                    value={formData.jam_kerja_per_hari}
                    onChange={(e) => setFormData(prev => ({ ...prev, jam_kerja_per_hari: parseInt(e.target.value) }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="hari_kerja_per_minggu">Hari Kerja/Minggu *</Label>
                  <Input
                    id="hari_kerja_per_minggu"
                    type="number"
                    min="1"
                    max="7"
                    value={formData.hari_kerja_per_minggu}
                    onChange={(e) => setFormData(prev => ({ ...prev, hari_kerja_per_minggu: parseInt(e.target.value) }))}
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="overtime_available"
                    checked={formData.overtime_available}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, overtime_available: checked }))}
                  />
                  <Label htmlFor="overtime_available">Overtime Available</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="akomodasi">Akomodasi</Label>
                  <Input
                    id="akomodasi"
                    value={formData.akomodasi}
                    onChange={(e) => setFormData(prev => ({ ...prev, akomodasi: e.target.value }))}
                    placeholder="Masukkan fasilitas akomodasi"
                  />
                </div>

                <div>
                  <Label htmlFor="transportasi">Transportasi</Label>
                  <Input
                    id="transportasi"
                    value={formData.transportasi}
                    onChange={(e) => setFormData(prev => ({ ...prev, transportasi: e.target.value }))}
                    placeholder="Masukkan fasilitas transportasi"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="asuransi">Asuransi</Label>
                  <Input
                    id="asuransi"
                    value={formData.asuransi}
                    onChange={(e) => setFormData(prev => ({ ...prev, asuransi: e.target.value }))}
                    placeholder="Masukkan fasilitas asuransi"
                  />
                </div>

                <div>
                  <Label htmlFor="fasilitas_lain">Fasilitas Lain</Label>
                  <Input
                    id="fasilitas_lain"
                    value={formData.fasilitas_lain}
                    onChange={(e) => setFormData(prev => ({ ...prev, fasilitas_lain: e.target.value }))}
                    placeholder="Masukkan fasilitas lain"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status & Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5 text-orange-600" />
                Status & Jadwal
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="status">Status *</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="tanggal_buka">Tanggal Buka *</Label>
                  <Input
                    id="tanggal_buka"
                    type="date"
                    value={formData.tanggal_buka}
                    onChange={(e) => setFormData(prev => ({ ...prev, tanggal_buka: e.target.value }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="tanggal_tutup">Tanggal Tutup *</Label>
                  <Input
                    id="tanggal_tutup"
                    type="date"
                    value={formData.tanggal_tutup}
                    onChange={(e) => setFormData(prev => ({ ...prev, tanggal_tutup: e.target.value }))}
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit" disabled={loading} className="bg-orange-500 hover:bg-orange-600">
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                jobOrder ? "Update Job Order" : "Simpan Job Order"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 