"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import {
  ArrowLeft,
  Edit,
  Building2,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  Briefcase,
  FileText,
  Eye,
  TrendingUp,
  Award,
  Globe,
  Plus,
  Trash2,
  AlertTriangle,
  Loader2,
  User,
  Target,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import { JobOrderService } from "@/lib/services"
import { JobOrder } from "@/lib/types/database"
import StudentSelectionModal from "../components/student-selection-modal"
import BulkTimelineModal from "../components/bulk-timeline-modal"

export default function DetailJobOrder() {
  const params = useParams()
  const jobOrderId = params.id as string
  
  const [loading, setLoading] = useState(true)
  const [jobOrder, setJobOrder] = useState<any>(null)
  const [jobOrderStats, setJobOrderStats] = useState<any>(null)
  const [isStudentSelectionOpen, setIsStudentSelectionOpen] = useState(false)
  const [isBulkTimelineOpen, setIsBulkTimelineOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    if (jobOrderId) {
      fetchJobOrderData()
    }
  }, [jobOrderId, refreshKey])

  const fetchJobOrderData = async () => {
    try {
      setLoading(true)
      const data = await JobOrderService.getJobOrderWithTimelineStats(jobOrderId)
      setJobOrder(data.jobOrder)
      setJobOrderStats(data)
    } catch (error) {
      console.error('Error fetching job order:', error)
      toast({
        title: "Error",
        description: "Gagal memuat data job order",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveStudent = async (siswaId: string) => {
    try {
      await JobOrderService.removeStudent(jobOrderId, siswaId)
      toast({
        title: "Berhasil",
        description: "Siswa berhasil dihapus dari job order",
      })
      setRefreshKey(prev => prev + 1)
    } catch (error: any) {
      console.error('Error removing student:', error)
      toast({
        title: "Error",
        description: error.message || "Gagal menghapus siswa dari job order",
        variant: "destructive",
      })
    }
  }

  const handleStudentAssignmentSuccess = () => {
    setRefreshKey(prev => prev + 1)
    setIsStudentSelectionOpen(false)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Lolos Administrasi": { color: "bg-blue-100 text-blue-800 border-blue-200", dot: "bg-blue-500" },
      "Lolos Wawancara": { color: "bg-purple-100 text-purple-800 border-purple-200", dot: "bg-purple-500" },
      Pemberkasan: { color: "bg-orange-100 text-orange-800 border-orange-200", dot: "bg-orange-500" },
      "Siap Berangkat": { color: "bg-green-100 text-green-800 border-green-200", dot: "bg-green-500" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Lolos Administrasi"]

    return (
      <Badge variant="outline" className={`${config.color} font-medium`}>
        <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
        {status}
      </Badge>
    )
  }

  const getJobOrderStatusBadge = (status: string) => {
    return status === "published" ? (
      <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-100">
        <CheckCircle className="w-3 h-3 mr-1" />
        Terbuka
      </Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-100">
        <Clock className="w-3 h-3 mr-1" />
        Ditutup
      </Badge>
    )
  }

  const [activeTab, setActiveTab] = useState("overview")

  const progressPercentage = jobOrder ? Math.round((jobOrder.kuota_terisi / jobOrder.jumlah_kuota) * 100) : 0

  // Compute statistics data from timeline stats
  const statisticsData = jobOrderStats?.timelineStats ? [
    {
      title: "Seleksi Administrasi",
      description: "Siswa yang lolos administrasi",
      value: jobOrderStats.timelineStats.filter((stat: any) => stat.current_stage === "Seleksi Administrasi").length,
      icon: FileText,
      color: "bg-blue-500"
    },
    {
      title: "Wawancara",
      description: "Siswa yang sedang wawancara",
      value: jobOrderStats.timelineStats.filter((stat: any) => stat.current_stage === "Wawancara").length,
      icon: Users,
      color: "bg-purple-500"
    },
    {
      title: "Pemberkasan",
      description: "Siswa dalam proses pemberkasan",
      value: jobOrderStats.timelineStats.filter((stat: any) => stat.current_stage === "Pemberkasan").length,
      icon: FileText,
      color: "bg-orange-500"
    },
    {
      title: "Siap Berangkat",
      description: "Siswa siap diberangkatkan",
      value: jobOrderStats.timelineStats.filter((stat: any) => stat.current_stage === "Siap Berangkat").length,
      icon: CheckCircle,
      color: "bg-green-500"
    }
  ] : []

  // Compute students data from assigned students
  const studentsData = jobOrderStats?.assignedStudents ? jobOrderStats.assignedStudents.map((placement: any) => {
    const timelineStat = jobOrderStats.timelineStats?.find((stat: any) => stat.siswa_id === placement.siswa_id)
    return {
      id: placement.siswa_id,
      nama: placement.siswa?.nama_lengkap || "Unknown",
      lpkMitra: placement.siswa?.lpk_mitra?.nama_lpk || "Unknown",
      statusSeleksi: timelineStat?.current_stage || "Belum dimulai",
      nilaiWawancara: null, // This would need to be implemented if interview scores are tracked
      tanggalDaftar: placement.created_at || new Date().toISOString()
    }
  }) : []

  const handleOpenTimelineUpdate = (studentId: string) => {
    // Navigate to student detail page for timeline editing
    window.open(`/siswa/${studentId}`, '_blank')
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <Button
                variant="outline"
                size="sm"
                className="mr-4 border-gray-300 bg-transparent"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali
              </Button>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <Briefcase className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Detail Job Order</h1>
                  <p className="text-gray-600 mt-1">ID: {jobOrder?.id}</p>
                </div>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg">
              <Edit className="h-4 w-4 mr-2" />
              Edit Job Order
            </Button>
          </div>
        </div>

        {/* Job Order Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Main Information */}
          <Card className="lg:col-span-2">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl text-gray-800 flex items-center">
                    <Building2 className="h-5 w-5 mr-2 text-orange-600" />
                    {jobOrder?.perusahaan_penerima?.nama_perusahaan}
                  </CardTitle>
                  <CardDescription className="text-lg font-medium text-gray-700 mt-1">
                    {jobOrder?.posisi}
                  </CardDescription>
                </div>
                {jobOrder && getJobOrderStatusBadge(jobOrder.status)}
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Jumlah Kuota</p>
                      <p className="font-semibold text-lg">{jobOrder?.jumlah_kuota} Orang</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Tanggal Input</p>
                      <p className="font-semibold">
                        {jobOrder?.created_at && new Date(jobOrder.created_at).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "long",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Lokasi Kerja</p>
                      <p className="font-semibold">{jobOrder?.perusahaan_penerima?.kota_jepang}, {jobOrder?.perusahaan_penerima?.prefektur}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Globe className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Gaji per Bulan</p>
                      <p className="font-semibold">¥{jobOrder?.gaji_pokok?.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Jam Kerja</p>
                      <p className="font-semibold">{jobOrder?.jam_kerja_per_hari} jam/hari, {jobOrder?.hari_kerja_per_minggu} hari/minggu</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Award className="h-5 w-5 text-orange-600 mr-3 mt-1" />
                    <div>
                      <p className="text-sm text-gray-500">Fasilitas</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {jobOrder?.fasilitas_lain && (
                          <Badge variant="secondary" className="text-xs">
                            {jobOrder.fasilitas_lain}
                          </Badge>
                        )}
                        {jobOrder?.akomodasi && (
                          <Badge variant="secondary" className="text-xs">
                            {jobOrder.akomodasi}
                          </Badge>
                        )}
                        {jobOrder?.transportasi && (
                          <Badge variant="secondary" className="text-xs">
                            {jobOrder.transportasi}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-orange-600" />
                  Syarat Khusus
                </h3>
                <ul className="space-y-2">
                  {jobOrder?.keahlian_khusus && (
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{jobOrder.keahlian_khusus}</span>
                    </li>
                  )}
                  {jobOrder?.pengalaman_kerja && (
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">Pengalaman: {jobOrder.pengalaman_kerja}</span>
                    </li>
                  )}
                </ul>
              </div>

              {jobOrder?.deskripsi_pekerjaan && (
                <>
                  <Separator className="my-6" />
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">Deskripsi Tambahan</h3>
                    <p className="text-sm text-gray-700 leading-relaxed">{jobOrder.deskripsi_pekerjaan}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Progress Card */}
          <Card>
            <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 border-b">
              <CardTitle className="text-lg text-gray-800 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                Progress Rekrutmen
              </CardTitle>
              <CardDescription>Status pemenuhan kuota job order</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {jobOrderStats?.assignedStudents?.length || 0}/{jobOrder?.jumlah_kuota || 0}
                </div>
                <p className="text-sm text-gray-500 mb-4">Siswa siap diberangkatkan</p>
                <Progress value={progressPercentage} className="h-3" />
                <p className="text-xs text-gray-500 mt-2">{progressPercentage}% dari target</p>
              </div>

              <div className="space-y-4">
                {statisticsData.map((stat, index) => {
                  const IconComponent = stat.icon
                  return (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className={`${stat.color} p-2 rounded-lg mr-3`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800">{stat.title}</p>
                          <p className="text-xs text-gray-500">{stat.description}</p>
                        </div>
                      </div>
                      <div className="text-lg font-bold text-gray-900">{stat.value}</div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Daftar Siswa Terdaftar</CardTitle>
                <CardDescription>Siswa yang mendaftar untuk job order ini</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-sm">
                  {jobOrderStats?.assignedStudents?.length || 0} Siswa
                </Badge>
                <Button
                  onClick={() => setIsStudentSelectionOpen(true)}
                  className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white"
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Siswa
                </Button>
                {studentsData.length > 0 && (
                  <Button
                    onClick={() => setIsBulkTimelineOpen(true)}
                    variant="outline"
                    size="sm"
                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Update Timeline
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">Nama Siswa</TableHead>
                    <TableHead className="min-w-[200px] font-semibold">LPK Mitra</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">Status Seleksi</TableHead>
                    <TableHead className="text-center font-semibold">Nilai Wawancara</TableHead>
                    <TableHead className="font-semibold">Tanggal Daftar</TableHead>
                    <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentsData.map((student: any, index: number) => (
                    <TableRow key={student.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{index + 1}</TableCell>
                      <TableCell className="font-medium">{student.nama}</TableCell>
                      <TableCell className="text-sm">{student.lpkMitra}</TableCell>
                      <TableCell>{getStatusBadge(student.statusSeleksi)}</TableCell>
                      <TableCell className="text-center">
                        {student.nilaiWawancara ? (
                          <Badge
                            variant="outline"
                            className={
                              student.nilaiWawancara >= 80
                                ? "bg-green-100 text-green-800 border-green-200"
                                : student.nilaiWawancara >= 70
                                  ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                                  : "bg-red-100 text-red-800 border-red-200"
                            }
                          >
                            {student.nilaiWawancara}
                          </Badge>
                        ) : (
                          <span className="text-gray-400 text-sm">Belum</span>
                        )}
                      </TableCell>
                      <TableCell className="text-sm">
                        {new Date(student.tanggalDaftar).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                            onClick={() => handleRemoveStudent(student.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                            onClick={() => handleOpenTimelineUpdate(student.id)}
                            title="Update Timeline"
                          >
                            <Calendar className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {studentsData.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada siswa terdaftar</h3>
                <p className="text-gray-500">Siswa akan muncul di sini setelah mendaftar untuk job order ini.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Student Selection Modal */}
      <StudentSelectionModal
        isOpen={isStudentSelectionOpen}
        onClose={() => setIsStudentSelectionOpen(false)}
        jobOrderId={jobOrderId}
        onSuccess={handleStudentAssignmentSuccess}
      />

      {/* Bulk Timeline Update Modal */}
      <BulkTimelineModal
        isOpen={isBulkTimelineOpen}
        onClose={() => setIsBulkTimelineOpen(false)}
        students={studentsData}
        onSuccess={() => {
          setRefreshKey(prev => prev + 1)
          setIsBulkTimelineOpen(false)
        }}
      />
    </div>
  )
}
