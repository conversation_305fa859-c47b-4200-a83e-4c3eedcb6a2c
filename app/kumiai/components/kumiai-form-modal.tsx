"use client"

import React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Kumiai } from "@/lib/types/database"

interface FormData {
  nama_kumiai: string
  kode_kumiai: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  keterangan?: string
  status: "aktif" | "nonaktif" | "suspended"
}

interface FormErrors {
  nama_kumiai?: string
  kode_kumiai?: string
  alamat_jepang?: string
  kota_jepang?: string
  prefektur?: string
  kontak_person?: string
  nomor_telepon?: string
  email?: string
}

interface KumiaiFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: FormData) => Promise<void>
  editingKumiai: Kumiai | null
  formData: FormData
  setFormData: (data: FormData) => void
  formErrors: FormErrors
  isSubmitting: boolean
}

// Daftar prefektur di Jepang
const PREFEKTUR_OPTIONS = [
  "Hokkaido", "Aomori", "Iwate", "Miyagi", "Akita", "Yamagata", "Fukushima",
  "Ibaraki", "Tochigi", "Gunma", "Saitama", "Chiba", "Tokyo", "Kanagawa",
  "Niigata", "Toyama", "Ishikawa", "Fukui", "Yamanashi", "Nagano", "Gifu",
  "Shizuoka", "Aichi", "Mie", "Shiga", "Kyoto", "Osaka", "Hyogo", "Nara",
  "Wakayama", "Tottori", "Shimane", "Okayama", "Hiroshima", "Yamaguchi",
  "Tokushima", "Kagawa", "Ehime", "Kochi", "Fukuoka", "Saga", "Nagasaki",
  "Kumamoto", "Oita", "Miyazaki", "Kagoshima", "Okinawa"
]

export function KumiaiFormModal({
  isOpen,
  onClose,
  onSubmit,
  editingKumiai,
  formData,
  setFormData,
  formErrors,
  isSubmitting,
}: KumiaiFormModalProps) {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSubmit(formData)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {editingKumiai ? "Edit Kumiai" : "Tambah Kumiai Baru"}
          </DialogTitle>
          <DialogDescription>
            {editingKumiai
              ? "Perbarui informasi Kumiai yang sudah ada"
              : "Masukkan informasi Kumiai baru untuk didaftarkan ke sistem"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nama Kumiai */}
            <div className="md:col-span-2">
              <Label htmlFor="nama_kumiai" className="text-sm font-medium text-gray-700">
                Nama Kumiai <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nama_kumiai"
                value={formData.nama_kumiai}
                onChange={(e) => setFormData({ ...formData, nama_kumiai: e.target.value })}
                placeholder="Masukkan nama lengkap Kumiai"
                className={formErrors.nama_kumiai ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.nama_kumiai && <p className="text-sm text-red-600 mt-1">{formErrors.nama_kumiai}</p>}
            </div>

            {/* Kode Kumiai */}
            <div>
              <Label htmlFor="kode_kumiai" className="text-sm font-medium text-gray-700">
                Kode Kumiai <span className="text-red-500">*</span>
              </Label>
              <Input
                id="kode_kumiai"
                value={formData.kode_kumiai}
                onChange={(e) => setFormData({ ...formData, kode_kumiai: e.target.value.toUpperCase() })}
                placeholder="Contoh: KUM001"
                className={formErrors.kode_kumiai ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.kode_kumiai && <p className="text-sm text-red-600 mt-1">{formErrors.kode_kumiai}</p>}
            </div>

            {/* Status */}
            <div>
              <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                Status <span className="text-red-500">*</span>
              </Label>
              <Select value={formData.status} onValueChange={(value: "aktif" | "nonaktif" | "suspended") => setFormData({ ...formData, status: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="nonaktif">Nonaktif</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Alamat Jepang */}
            <div className="md:col-span-2">
              <Label htmlFor="alamat_jepang" className="text-sm font-medium text-gray-700">
                Alamat di Jepang <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="alamat_jepang"
                value={formData.alamat_jepang}
                onChange={(e) => setFormData({ ...formData, alamat_jepang: e.target.value })}
                placeholder="Masukkan alamat lengkap di Jepang"
                className={formErrors.alamat_jepang ? "border-red-500 focus:border-red-500" : ""}
                rows={2}
              />
              {formErrors.alamat_jepang && <p className="text-sm text-red-600 mt-1">{formErrors.alamat_jepang}</p>}
            </div>

            {/* Kota Jepang */}
            <div>
              <Label htmlFor="kota_jepang" className="text-sm font-medium text-gray-700">
                Kota di Jepang <span className="text-red-500">*</span>
              </Label>
              <Input
                id="kota_jepang"
                value={formData.kota_jepang}
                onChange={(e) => setFormData({ ...formData, kota_jepang: e.target.value })}
                placeholder="Masukkan nama kota"
                className={formErrors.kota_jepang ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.kota_jepang && <p className="text-sm text-red-600 mt-1">{formErrors.kota_jepang}</p>}
            </div>

            {/* Prefektur */}
            <div>
              <Label htmlFor="prefektur" className="text-sm font-medium text-gray-700">
                Prefektur <span className="text-red-500">*</span>
              </Label>
              <Select value={formData.prefektur} onValueChange={(value) => setFormData({ ...formData, prefektur: value })}>
                <SelectTrigger className={formErrors.prefektur ? "border-red-500 focus:border-red-500" : ""}>
                  <SelectValue placeholder="Pilih prefektur" />
                </SelectTrigger>
                <SelectContent>
                  {PREFEKTUR_OPTIONS.map((prefektur) => (
                    <SelectItem key={prefektur} value={prefektur}>
                      {prefektur}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.prefektur && <p className="text-sm text-red-600 mt-1">{formErrors.prefektur}</p>}
            </div>

            {/* Kontak Person */}
            <div>
              <Label htmlFor="kontak_person" className="text-sm font-medium text-gray-700">
                Kontak Person <span className="text-red-500">*</span>
              </Label>
              <Input
                id="kontak_person"
                value={formData.kontak_person}
                onChange={(e) => setFormData({ ...formData, kontak_person: e.target.value })}
                placeholder="Nama penanggung jawab"
                className={formErrors.kontak_person ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.kontak_person && <p className="text-sm text-red-600 mt-1">{formErrors.kontak_person}</p>}
            </div>

            {/* Nomor Telepon */}
            <div>
              <Label htmlFor="nomor_telepon" className="text-sm font-medium text-gray-700">
                Nomor Telepon <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nomor_telepon"
                value={formData.nomor_telepon}
                onChange={(e) => setFormData({ ...formData, nomor_telepon: e.target.value })}
                placeholder="Contoh: +81-3-1234-5678"
                className={formErrors.nomor_telepon ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.nomor_telepon && <p className="text-sm text-red-600 mt-1">{formErrors.nomor_telepon}</p>}
            </div>

            {/* Email */}
            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                className={formErrors.email ? "border-red-500 focus:border-red-500" : ""}
              />
              {formErrors.email && <p className="text-sm text-red-600 mt-1">{formErrors.email}</p>}
            </div>

            {/* Website */}
            <div>
              <Label htmlFor="website" className="text-sm font-medium text-gray-700">
                Website (Opsional)
              </Label>
              <Input
                id="website"
                type="url"
                value={formData.website || ""}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                placeholder="https://website.com"
              />
            </div>

            {/* Keterangan */}
            <div className="md:col-span-2">
              <Label htmlFor="keterangan" className="text-sm font-medium text-gray-700">
                Keterangan (Opsional)
              </Label>
              <Textarea
                id="keterangan"
                value={formData.keterangan || ""}
                onChange={(e) => setFormData({ ...formData, keterangan: e.target.value })}
                placeholder="Masukkan keterangan atau catatan tambahan tentang Kumiai"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Batal
            </Button>
            <Button type="submit" disabled={isSubmitting} className="bg-orange-500 hover:bg-orange-600 text-white">
              {isSubmitting ? "Menyimpan..." : editingKumiai ? "Perbarui" : "Simpan"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 