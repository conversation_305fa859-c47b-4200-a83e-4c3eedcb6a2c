"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Users, FileText, Briefcase, GraduationCap, MapPin, Building2, Building, Menu, X, Shield, UserCog, LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { AuthProvider, useAuth } from "@/lib/auth/auth-context"
import { PermissionGuard } from "@/components/auth/permission-guard"
import { AuthLoading, AuthRedirect } from "@/components/auth/auth-loading"

const navigation = [
  { name: "Dashboard", href: "/", icon: Home, permission: { module: "dashboard", action: "read" } },
  { name: "Pendaftaran", href: "/pendaftaran", icon: Users, permission: { module: "pendaftaran", action: "read" } },
  { name: "<PERSON> Siswa", href: "/siswa", icon: Users, permission: { module: "siswa", action: "read" } },
  { name: "Job Order", href: "/job-order", icon: Briefcase, permission: { module: "job_order", action: "read" } },
  { name: "Dokumen", href: "/dokumen", icon: FileText, permission: { module: "dokumen", action: "read" } },
  { name: "Pendidikan", href: "/pendidikan", icon: GraduationCap, permission: { module: "pendidikan", action: "read" } },
  { name: "Penempatan & Keberangkatan", href: "/penempatan", icon: MapPin, permission: { module: "penempatan", action: "read" } },
  { name: "Data Master Kumiai", href: "/kumiai", icon: Building2, permission: { module: "kumiai", action: "read" } },
  { name: "Data Master LPK Mitra", href: "/lpk-mitra", icon: Building, permission: { module: "lpk_mitra", action: "read" } },
  { name: "User Management", href: "/users", icon: UserCog, permission: { module: "users", action: "read" } },
  { name: "Role Management", href: "/roles", icon: Shield, permission: { module: "roles", action: "read" } },
]

function ClientLayoutInner({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const { hasPermission, user, userProfile, signOut, loading } = useAuth()

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut()
      window.location.href = '/login'
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Show loading screen while checking auth
  if (loading) {
    return <AuthLoading message="Loading dashboard..." />
  }

  // Redirect to login if not authenticated
  if (!user) {
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
    return <AuthRedirect />
  }

  // Filter navigation items based on user permissions
  const filteredNavigation = navigation.filter(item => {
    if (!item.permission) return true
    return hasPermission(item.permission.module, item.permission.action)
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={cn("fixed inset-0 z-50 lg:hidden", sidebarOpen ? "block" : "hidden")}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 border-b">
            <h1 className="text-lg font-semibold text-gray-900">Magang Jepang</h1>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-orange-100 text-orange-900 border-r-2 border-orange-500"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0",
                      isActive ? "text-orange-500" : "text-gray-400 group-hover:text-gray-500",
                    )}
                  />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          <div className="flex h-16 items-center px-4 border-b bg-gradient-to-r from-orange-500 to-maroon-600">
            <h1 className="text-lg font-bold text-white">Magang Jepang</h1>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                    isActive
                      ? "bg-orange-100 text-orange-900 border-r-2 border-orange-500"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                  )}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0",
                      isActive ? "text-orange-500" : "text-gray-400 group-hover:text-gray-500",
                    )}
                  />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <Button variant="ghost" size="sm" className="lg:hidden" onClick={() => setSidebarOpen(true)}>
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1 items-center">
              <h2 className="text-lg font-semibold text-gray-900">
                {filteredNavigation.find((item) => item.href === pathname)?.name || "Dashboard"}
              </h2>
            </div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" />
              <div className="flex items-center gap-x-4">
                <div className="flex items-center gap-x-2">
                  <div className="text-sm text-gray-500">
                    {userProfile?.full_name || user?.email || 'User'}
                  </div>
                  <div className="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {userProfile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                    </span>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="text-gray-500 hover:text-red-600"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">{children}</main>
      </div>
    </div>
  )
}

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AuthProvider>
      <ClientLayoutInner>{children}</ClientLayoutInner>
    </AuthProvider>
  )
}
