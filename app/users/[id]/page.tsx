'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Edit, 
  UserCheck, 
  UserX,
  Shield,
  Building,
  Phone,
  Mail,
  Calendar,
  Clock
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from '@/hooks/use-toast'
import { RBACService } from '@/lib/services/rbac.service'
import { withAuth } from '@/lib/auth/auth-context'
import { PermissionGuard } from '@/components/auth/permission-guard'
import type { UserProfile } from '@/lib/types/rbac'

function UserDetailPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params.id as string
  
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (userId) {
      fetchUserDetail()
    }
  }, [userId])

  const fetchUserDetail = async () => {
    try {
      setLoading(true)
      const userData = await RBACService.getUserById(userId)
      setUser(userData)
    } catch (error) {
      console.error('Error fetching user detail:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat detail user',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleToggleUserStatus = async () => {
    if (!user) return
    
    try {
      await RBACService.updateUser(user.id, { is_active: !user.is_active })
      await fetchUserDetail()
      toast({
        title: 'Success',
        description: `User berhasil ${user.is_active ? 'dinonaktifkan' : 'diaktifkan'}`
      })
    } catch (error) {
      console.error('Error toggling user status:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengubah status user',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">User Not Found</h1>
          <p className="text-gray-600 mb-4">User yang Anda cari tidak ditemukan.</p>
          <Link href="/users">
            <Button>Kembali ke User Management</Button>
          </Link>
        </div>
      </div>
    )
  }

  const activeRoles = user.role_assignments?.filter(ra => ra.is_active) || []

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/users">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{user.full_name}</h1>
            <p className="text-muted-foreground">
              Detail informasi user dan role assignments
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <PermissionGuard module="users" action="update">
            <Button 
              variant="outline"
              onClick={handleToggleUserStatus}
            >
              {user.is_active ? (
                <>
                  <UserX className="h-4 w-4 mr-2" />
                  Nonaktifkan
                </>
              ) : (
                <>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Aktifkan
                </>
              )}
            </Button>
            <Button onClick={() => router.push(`/users?edit=${user.id}`)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit User
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Informasi Dasar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                  <p className="text-lg font-medium">{user.full_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Username</label>
                  <p className="text-lg">{user.username || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-lg flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    {user.email || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Nomor Telepon</label>
                  <p className="text-lg flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    {user.phone || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Posisi/Jabatan</label>
                  <p className="text-lg">{user.position || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Departemen</label>
                  <p className="text-lg">{user.department || '-'}</p>
                </div>
              </div>
              
              {user.lpk_mitra && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">LPK Mitra</label>
                    <p className="text-lg flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      {user.lpk_mitra.nama_lpk}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Role Assignments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Assignments
              </CardTitle>
              <CardDescription>
                Roles yang diberikan kepada user ini
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeRoles.length > 0 ? (
                <div className="space-y-4">
                  {activeRoles.map((assignment, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Badge variant="default">
                            {assignment.role?.display_name}
                          </Badge>
                          {assignment.lpk_mitra && (
                            <Badge variant="outline">
                              {assignment.lpk_mitra.nama_lpk}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          Assigned: {new Date(assignment.assigned_at).toLocaleDateString('id-ID')}
                        </p>
                        {assignment.expires_at && (
                          <p className="text-sm text-red-600">
                            Expires: {new Date(assignment.expires_at).toLocaleDateString('id-ID')}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  User ini belum memiliki role assignment
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Account Status</span>
                <Badge variant={user.is_active ? "default" : "secondary"}>
                  {user.is_active ? 'Aktif' : 'Tidak Aktif'}
                </Badge>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4" />
                  <span>Created: {new Date(user.created_at).toLocaleDateString('id-ID')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>Updated: {new Date(user.updated_at).toLocaleDateString('id-ID')}</span>
                </div>
                {user.last_login && (
                  <div className="flex items-center gap-2 text-sm">
                    <UserCheck className="h-4 w-4" />
                    <span>Last Login: {new Date(user.last_login).toLocaleDateString('id-ID')}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Roles</span>
                <Badge variant="outline">{activeRoles.length}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Assignments</span>
                <Badge variant="outline">{user.role_assignments?.length || 0}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default withAuth(UserDetailPage, { module: 'users', action: 'read' })
