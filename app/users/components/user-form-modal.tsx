'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from '@/hooks/use-toast'
import { RBACService } from '@/lib/services/rbac.service'
import type { UserProfile, Role, CreateUserRequest, UpdateUserRequest } from '@/lib/types/rbac'
import type { LpkMitra } from '@/lib/types/database'

interface UserFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  user?: UserProfile | null
  roles: Role[]
  lpkMitras: LpkMitra[]
}

export function UserFormModal({ 
  isOpen, 
  onClose, 
  onSave, 
  user, 
  roles,
  lpkMitras 
}: UserFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    full_name: '',
    username: '',
    phone: '',
    position: '',
    department: '',
    lpk_mitra_id: '',
    is_active: true,
    roles: [] as string[]
  })

  const isEditing = !!user

  useEffect(() => {
    if (user) {
      setFormData({
        email: user.email || '',
        password: '', // Never pre-fill password
        full_name: user.full_name,
        username: user.username || '',
        phone: user.phone || '',
        position: user.position || '',
        department: user.department || '',
        lpk_mitra_id: user.lpk_mitra_id || '',
        is_active: user.is_active,
        roles: user.role_assignments?.filter(ra => ra.is_active).map(ra => ra.role?.name).filter(Boolean) as string[] || []
      })
    } else {
      setFormData({
        email: '',
        password: '',
        full_name: '',
        username: '',
        phone: '',
        position: '',
        department: '',
        lpk_mitra_id: '',
        is_active: true,
        roles: []
      })
    }
  }, [user])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleRoleChange = (roleName: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      roles: checked 
        ? [...prev.roles, roleName]
        : prev.roles.filter(r => r !== roleName)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (isEditing) {
        const updateData: UpdateUserRequest = {
          full_name: formData.full_name,
          username: formData.username || undefined,
          phone: formData.phone || undefined,
          position: formData.position || undefined,
          department: formData.department || undefined,
          lpk_mitra_id: formData.lpk_mitra_id || undefined,
          is_active: formData.is_active,
          roles: formData.roles
        }
        await RBACService.updateUser(user!.id, updateData)
      } else {
        if (!formData.email || !formData.password || !formData.full_name) {
          toast({
            title: 'Error',
            description: 'Email, password, dan nama lengkap wajib diisi',
            variant: 'destructive'
          })
          return
        }

        const createData: CreateUserRequest = {
          email: formData.email,
          password: formData.password,
          full_name: formData.full_name,
          username: formData.username || undefined,
          phone: formData.phone || undefined,
          position: formData.position || undefined,
          department: formData.department || undefined,
          lpk_mitra_id: formData.lpk_mitra_id || undefined,
          roles: formData.roles
        }
        await RBACService.createUser(createData)
      }

      onSave()
    } catch (error: any) {
      console.error('Error saving user:', error)
      toast({
        title: 'Error',
        description: error.message || 'Gagal menyimpan user',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit User' : 'Tambah User Baru'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update informasi user dan role assignments'
              : 'Buat user baru dan assign role yang sesuai'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informasi Dasar</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={isEditing} // Email cannot be changed after creation
                  required
                />
              </div>

              {!isEditing && (
                <div className="space-y-2">
                  <Label htmlFor="password">Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    required
                    minLength={6}
                  />
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="full_name">Nama Lengkap *</Label>
                <Input
                  id="full_name"
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Posisi/Jabatan</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="department">Departemen</Label>
                <Input
                  id="department"
                  value={formData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lpk_mitra_id">LPK Mitra</Label>
                <Select 
                  value={formData.lpk_mitra_id} 
                  onValueChange={(value) => handleInputChange('lpk_mitra_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih LPK Mitra" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tidak ada</SelectItem>
                    {lpkMitras.map((lpk) => (
                      <SelectItem key={lpk.id} value={lpk.id}>
                        {lpk.nama_lpk}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {isEditing && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                />
                <Label htmlFor="is_active">User Aktif</Label>
              </div>
            )}
          </div>

          {/* Role Assignment */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Role Assignment</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {roles.map((role) => (
                <div key={role.id} className="flex items-start space-x-2">
                  <Checkbox
                    id={`role-${role.id}`}
                    checked={formData.roles.includes(role.name)}
                    onCheckedChange={(checked) => handleRoleChange(role.name, checked as boolean)}
                  />
                  <div className="space-y-1">
                    <Label htmlFor={`role-${role.id}`} className="text-sm font-medium">
                      {role.display_name}
                    </Label>
                    {role.description && (
                      <p className="text-xs text-gray-500">{role.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Menyimpan...' : (isEditing ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
