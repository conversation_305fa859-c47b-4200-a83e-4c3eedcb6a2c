"use client"

import { useState, useMemo } from "react"
import {
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Filter,
  Download,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Sample data
const sampleStudents = [
  {
    id: 1,
    namaLengkap: "Ahmad Rizki Pratama",
    gender: "Laki-laki",
    nik: "3201234567890123",
    kotaKabupaten: "Jakarta Selatan",
    lpkMitra: "LPK Sukses Mandiri Jakarta",
    statusProses: "Wawancara",
    tanggalTerdaftar: "2024-01-15",
  },
  {
    id: 2,
    namaLengkap: "Siti Nurhaliza",
    gender: "Perempuan",
    nik: "3301234567890124",
    kotaKabupaten: "Bandung",
    lpkMitra: "LPK Maju Bersama Bandung",
    statusProses: "Pemberkasan",
    tanggalTerdaftar: "2024-01-14",
  },
  {
    id: 3,
    namaLengkap: "Budi Santoso",
    gender: "Laki-laki",
    nik: "3501234567890125",
    kotaKabupaten: "Surabaya",
    lpkMitra: "LPK Harapan Bangsa Surabaya",
    statusProses: "Siap Berangkat",
    tanggalTerdaftar: "2024-01-13",
  },
  {
    id: 4,
    namaLengkap: "Dewi Sartika",
    gender: "Perempuan",
    nik: "1201234567890126",
    kotaKabupaten: "Medan",
    lpkMitra: "LPK Karya Utama Medan",
    statusProses: "Pre-seleksi",
    tanggalTerdaftar: "2024-01-12",
  },
  {
    id: 5,
    namaLengkap: "Andi Wijaya",
    gender: "Laki-laki",
    nik: "7301234567890127",
    kotaKabupaten: "Makassar",
    lpkMitra: "LPK Nusantara Makassar",
    statusProses: "Sudah Berangkat",
    tanggalTerdaftar: "2024-01-11",
  },
]

const statusOptions = [
  "Semua Status",
  "Pre-seleksi",
  "Seleksi Administrasi",
  "Wawancara",
  "Pemberkasan",
  "Siap Berangkat",
  "Sudah Berangkat",
  "Pulang",
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    "Pre-seleksi": { color: "bg-blue-100 text-blue-800 border-blue-200", dot: "bg-blue-500" },
    "Seleksi Administrasi": { color: "bg-yellow-100 text-yellow-800 border-yellow-200", dot: "bg-yellow-500" },
    Wawancara: { color: "bg-purple-100 text-purple-800 border-purple-200", dot: "bg-purple-500" },
    Pemberkasan: { color: "bg-orange-100 text-orange-800 border-orange-200", dot: "bg-orange-500" },
    "Siap Berangkat": { color: "bg-green-100 text-green-800 border-green-200", dot: "bg-green-500" },
    "Sudah Berangkat": { color: "bg-emerald-100 text-emerald-800 border-emerald-200", dot: "bg-emerald-500" },
    Pulang: { color: "bg-gray-100 text-gray-800 border-gray-200", dot: "bg-gray-500" },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Pre-seleksi"]

  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      <div className={`w-2 h-2 rounded-full ${config.dot} mr-2`}></div>
      {status}
    </Badge>
  )
}

type SortField = "namaLengkap" | "tanggalTerdaftar" | "kotaKabupaten" | "statusProses"
type SortDirection = "asc" | "desc"

export default function DataSiswa() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("Semua Status")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [sortField, setSortField] = useState<SortField>("tanggalTerdaftar")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")

  // Filter and search logic
  const filteredStudents = useMemo(() => {
    return sampleStudents.filter((student) => {
      const matchesSearch =
        student.namaLengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.nik.includes(searchTerm) ||
        student.kotaKabupaten.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.lpkMitra.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "Semua Status" || student.statusProses === statusFilter

      return matchesSearch && matchesStatus
    })
  }, [searchTerm, statusFilter])

  // Sort logic
  const sortedStudents = useMemo(() => {
    return [...filteredStudents].sort((a, b) => {
      let aValue = a[sortField]
      let bValue = b[sortField]

      if (sortField === "tanggalTerdaftar") {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })
  }, [filteredStudents, sortField, sortDirection])

  // Pagination logic
  const totalPages = Math.ceil(sortedStudents.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedStudents = sortedStudents.slice(startIndex, startIndex + itemsPerPage)

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />
    return sortDirection === "asc" ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
  }

  const handleDelete = (id: number) => {
    console.log("Delete student with id:", id)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center mb-4 sm:mb-0">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Data Siswa Terdaftar</h1>
                <p className="text-gray-600 mt-1">Kelola dan monitor data siswa magang ke Jepang</p>
              </div>
            </div>
            <Button
              className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white shadow-lg"
              onClick={() => (window.location.href = "/pendaftaran")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Daftarkan Siswa Baru
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari nama, NIK, kota, atau LPK..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>

                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-[200px] h-10 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="border-gray-300 bg-transparent">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button variant="outline" size="sm" className="border-gray-300 bg-transparent">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Daftar Siswa</CardTitle>
                <CardDescription>
                  Menampilkan {paginatedStudents.length} dari {sortedStudents.length} siswa
                </CardDescription>
              </div>
              <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                <SelectTrigger className="w-[100px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[60px] text-center font-semibold">No.</TableHead>
                    <TableHead className="min-w-[200px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("namaLengkap")}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                      >
                        Nama Lengkap
                        {getSortIcon("namaLengkap")}
                      </Button>
                    </TableHead>
                    <TableHead className="text-center font-semibold">Gender</TableHead>
                    <TableHead className="min-w-[150px] font-semibold">NIK</TableHead>
                    <TableHead className="min-w-[150px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("kotaKabupaten")}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                      >
                        Kota/Kabupaten
                        {getSortIcon("kotaKabupaten")}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[200px] font-semibold">LPK Mitra</TableHead>
                    <TableHead className="min-w-[150px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("statusProses")}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                      >
                        Status Proses
                        {getSortIcon("statusProses")}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[120px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort("tanggalTerdaftar")}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                      >
                        Tanggal Terdaftar
                        {getSortIcon("tanggalTerdaftar")}
                      </Button>
                    </TableHead>
                    <TableHead className="w-[100px] text-center font-semibold">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedStudents.map((student, index) => (
                    <TableRow key={student.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">{startIndex + index + 1}</TableCell>
                      <TableCell className="font-medium">{student.namaLengkap}</TableCell>
                      <TableCell className="text-center">{student.gender}</TableCell>
                      <TableCell className="font-mono text-sm">{student.nik}</TableCell>
                      <TableCell>{student.kotaKabupaten}</TableCell>
                      <TableCell className="text-sm">{student.lpkMitra}</TableCell>
                      <TableCell>{getStatusBadge(student.statusProses)}</TableCell>
                      <TableCell className="text-sm">
                        {new Date(student.tanggalTerdaftar).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => (window.location.href = `/siswa/${student.id}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              Lihat Detail
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Hapus
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Hapus Data Siswa</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Apakah Anda yakin ingin menghapus data siswa "{student.namaLengkap}"? Tindakan ini
                                    tidak dapat dibatalkan.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Batal</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDelete(student.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Hapus
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Empty State */}
            {paginatedStudents.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada data siswa</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || statusFilter !== "Semua Status"
                    ? "Tidak ada siswa yang sesuai dengan filter yang dipilih."
                    : "Belum ada siswa yang terdaftar dalam sistem."}
                </p>
                {!searchTerm && statusFilter === "Semua Status" && (
                  <Button
                    className="bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700"
                    onClick={() => (window.location.href = "/pendaftaran")}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Daftarkan Siswa Pertama
                  </Button>
                )}
              </div>
            )}
          </CardContent>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-gray-700">
                Menampilkan {startIndex + 1} - {Math.min(startIndex + itemsPerPage, sortedStudents.length)} dari{" "}
                {sortedStudents.length} siswa
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={() => setCurrentPage(1)} disabled={currentPage === 1}>
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium px-3 py-1 bg-gray-100 rounded">
                  {currentPage} dari {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
