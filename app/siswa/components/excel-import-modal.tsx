'use client'

import { useState, useRef } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Upload, 
  FileSpreadsheet, 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  BarChart3,
  Download,
  <PERSON>,
  Play
} from 'lucide-react'
import { toast } from 'sonner'
import { ExcelImportService } from '@/lib/services'
import { 
  ImportPreviewData, 
  ImportProcessingStatus, 
  ImportResult,
  ImportValidationError
} from '@/lib/types/excel-import'

interface ExcelImportModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete: () => void
}

export function ExcelImportModal({ open, onOpenChange, onImportComplete }: ExcelImportModalProps) {
  const [file, setFile] = useState<File | null>(null)
  const [previewData, setPreviewData] = useState<ImportPreviewData | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStatus, setProcessingStatus] = useState<ImportProcessingStatus>({
    status: 'idle',
    progress: 0,
    currentStep: '',
    processedRows: 0,
    totalRows: 0,
    errors: []
  })
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [currentTab, setCurrentTab] = useState('upload')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (selectedFile: File) => {
    if (!selectedFile) return

    if (!selectedFile.name.match(/\.(xlsx|xls)$/i)) {
      toast.error('File harus berformat Excel (.xlsx atau .xls)')
      return
    }

    setFile(selectedFile)
    setIsProcessing(true)
    setCurrentTab('preview')

    try {
      const preview = await ExcelImportService.createPreviewData(selectedFile)
      setPreviewData(preview)
      
      if (preview.statistics.errorRows > 0) {
        toast.warning(`${preview.statistics.errorRows} baris memiliki error yang perlu diperbaiki`)
      } else {
        toast.success(`${preview.statistics.validRows} baris siap untuk diimport`)
      }
    } catch (error) {
      toast.error(`Error membaca file: ${error}`)
      setCurrentTab('upload')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleImport = async () => {
    if (!previewData) return

    setIsProcessing(true)
    setCurrentTab('result')
    setProcessingStatus({
      status: 'processing',
      progress: 0,
      currentStep: 'Memulai import...',
      processedRows: 0,
      totalRows: previewData.statistics.totalRows,
      errors: []
    })

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProcessingStatus(prev => ({
          ...prev,
          progress: Math.min(prev.progress + Math.random() * 10, 90),
          currentStep: prev.progress < 30 ? 'Memproses LPK...' :
                      prev.progress < 60 ? 'Memproses Siswa...' :
                      'Memproses Penempatan...'
        }))
      }, 500)

      const result = await ExcelImportService.importData(previewData)
      
      clearInterval(progressInterval)
      setProcessingStatus({
        status: 'completed',
        progress: 100,
        currentStep: 'Import selesai',
        processedRows: result.successfulImports,
        totalRows: result.totalRows,
        errors: result.errors
      })
      
      setImportResult(result)
      
      if (result.success) {
        toast.success(result.message)
        setTimeout(() => {
          onImportComplete()
          handleClose()
        }, 3000)
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      setProcessingStatus({
        status: 'error',
        progress: 0,
        currentStep: 'Import gagal',
        processedRows: 0,
        totalRows: previewData.statistics.totalRows,
        errors: [{
          row: 0,
          column: 'general',
          field: 'nama_lengkap',
          value: '',
          error: `Error: ${error}`,
          severity: 'error'
        }]
      })
      toast.error(`Import gagal: ${error}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleClose = () => {
    setFile(null)
    setPreviewData(null)
    setImportResult(null)
    setCurrentTab('upload')
    setProcessingStatus({
      status: 'idle',
      progress: 0,
      currentStep: '',
      processedRows: 0,
      totalRows: 0,
      errors: []
    })
    onOpenChange(false)
  }

  const downloadTemplate = () => {
    // Use the service method to download Excel template
    ExcelImportService.downloadTemplate()
  }

  const renderErrorsTable = (errors: ImportValidationError[]) => (
    <ScrollArea className="h-64">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Baris</TableHead>
            <TableHead>Field</TableHead>
            <TableHead>Nilai</TableHead>
            <TableHead>Error</TableHead>
            <TableHead>Tingkat</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {errors.map((error, index) => (
            <TableRow key={index}>
              <TableCell>{error.row}</TableCell>
              <TableCell className="font-mono text-sm">{error.field}</TableCell>
              <TableCell className="truncate max-w-32" title={String(error.value)}>
                {String(error.value)}
              </TableCell>
              <TableCell>{error.error}</TableCell>
              <TableCell>
                <Badge variant={error.severity === 'error' ? 'destructive' : 'secondary'}>
                  {error.severity === 'error' ? 'Error' : 'Warning'}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </ScrollArea>
  )

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Import Data Siswa dari Excel
          </DialogTitle>
          <DialogDescription>
            Upload file Excel untuk mengimport data siswa secara massal. Jika tidak ada informasi LPK, sistem akan menggunakan LPK default. 
            Sistem juga dapat membuat Kumiai dan Perusahaan baru jika data tersedia.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" disabled={isProcessing}>
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </TabsTrigger>
            <TabsTrigger value="preview" disabled={!previewData || isProcessing}>
              <Eye className="h-4 w-4 mr-2" />
              Preview Data
            </TabsTrigger>
            <TabsTrigger value="result" disabled={!importResult && processingStatus.status === 'idle'}>
              <BarChart3 className="h-4 w-4 mr-2" />
              Hasil Import
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Upload File Excel</CardTitle>
                  <CardDescription>
                    Pilih file Excel (.xlsx atau .xls) yang berisi data siswa untuk diimport
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="excel-file">File Excel</Label>
                    <Input
                      ref={fileInputRef}
                      id="excel-file"
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileSelect(file)
                      }}
                    />
                  </div>
                  
                  {file && (
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        File terpilih: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Template Excel</CardTitle>
                  <CardDescription>
                    Download template Excel berisi kolom-kolom lengkap termasuk data siswa, LPK, Kumiai, 
                    Perusahaan, dan Penempatan. Template sudah dilengkapi dengan contoh data.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" onClick={downloadTemplate}>
                    <Download className="h-4 w-4 mr-2" />
                    Download Template Excel
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {previewData && (
              <div className="space-y-4">
                <div className="grid grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold">{previewData.statistics.totalRows}</div>
                      <div className="text-xs text-muted-foreground">Total Baris</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold text-green-600">{previewData.statistics.validRows}</div>
                      <div className="text-xs text-muted-foreground">Baris Valid</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold text-red-600">{previewData.statistics.errorRows}</div>
                      <div className="text-xs text-muted-foreground">Baris Error</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold text-yellow-600">{previewData.statistics.warningRows}</div>
                      <div className="text-xs text-muted-foreground">Baris Warning</div>
                    </CardContent>
                  </Card>
                </div>

                {previewData.errors.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertCircle className="h-5 w-5 text-yellow-500" />
                        Validasi Data ({previewData.errors.length} masalah)
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {renderErrorsTable(previewData.errors)}
                    </CardContent>
                  </Card>
                )}

                <Card>
                  <CardHeader>
                    <CardTitle>Preview Data (5 baris pertama)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Nama</TableHead>
                            <TableHead>NIK</TableHead>
                            <TableHead>Gender</TableHead>
                            <TableHead>Tempat Lahir</TableHead>
                            <TableHead>LPK</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {previewData.data.slice(0, 5).map((row, index) => (
                            <TableRow key={index}>
                              <TableCell>{row.nama_lengkap}</TableCell>
                              <TableCell className="font-mono">{row.nik}</TableCell>
                              <TableCell>{row.jenis_kelamin}</TableCell>
                              <TableCell>{row.tempat_lahir}</TableCell>
                              <TableCell>{row.nama_lpk}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {row.status_pendaftaran || 'approved'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="result" className="space-y-4">
            {processingStatus.status === 'processing' && (
              <Card>
                <CardHeader>
                  <CardTitle>Proses Import Berlangsung...</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Progress value={processingStatus.progress} className="w-full" />
                  <div className="text-sm text-muted-foreground">
                    {processingStatus.currentStep} ({processingStatus.processedRows}/{processingStatus.totalRows})
                  </div>
                </CardContent>
              </Card>
            )}

            {importResult && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {importResult.success ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      Hasil Import
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-lg font-medium mb-4">{importResult.message}</div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{importResult.successfulImports}</div>
                        <div className="text-sm text-muted-foreground">Siswa Berhasil</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{importResult.createdEntities.lpk}</div>
                        <div className="text-sm text-muted-foreground">LPK Baru</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{importResult.createdEntities.kumiai}</div>
                        <div className="text-sm text-muted-foreground">Kumiai Baru</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{importResult.createdEntities.perusahaan}</div>
                        <div className="text-sm text-muted-foreground">Perusahaan Baru</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-600">{importResult.createdEntities.penempatan}</div>
                        <div className="text-sm text-muted-foreground">Penempatan Baru</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {(importResult.errors.length > 0 || importResult.warnings.length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Error & Warning</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {renderErrorsTable([...importResult.errors, ...importResult.warnings])}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isProcessing}>
            {importResult?.success ? 'Tutup' : 'Batal'}
          </Button>
          
          {currentTab === 'preview' && previewData && (
            <Button 
              onClick={handleImport} 
              disabled={previewData.statistics.errorRows > 0 || isProcessing}
            >
              <Play className="h-4 w-4 mr-2" />
              Mulai Import ({previewData.statistics.validRows} data)
            </Button>
          )}
          
          {currentTab === 'upload' && !file && (
            <Button onClick={() => fileInputRef.current?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              Pilih File Excel
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 