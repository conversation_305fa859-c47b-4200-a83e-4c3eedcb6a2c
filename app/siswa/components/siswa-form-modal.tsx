"use client"

import React, { use<PERSON>tate, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Check,
  User,
  MapPin,
  Ruler,
  GraduationCap,
  Users,
  Heart,
  Building2,
  AlertCircle,
  Share2,
} from "lucide-react"
import { 
  SiswaWithRelations, 
  SiswaFormData,
  SiswaPendid<PERSON><PERSON>,
  SiswaPengalamanKerja,
  SiswaKeluarga,
  SiswaSocialMedia,
  MaritalStatus,
  BloodType,
  AvailabilityStatus,
  RelationshipType,
  EducationLevel 
} from "@/lib/types/database"
import { MapPicker } from "@/components/ui/map-picker"
import { SocialMediaForm } from "@/components/ui/social-media-form"

interface SiswaFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: SiswaFormData) => Promise<void>
  editingSiswa: SiswaWithRelations | null
  lpkOptions: Array<{ id: string; nama_lpk: string }>
  // Legacy props for compatibility
  formData?: any
  setFormData?: any
  formErrors?: any
  isSubmitting?: boolean
}

interface FormErrors {
  [key: string]: string
}

const FORM_STEPS = [
  { id: 1, title: 'Data Pribadi', icon: User },
  { id: 2, title: 'Kontak & Alamat', icon: MapPin },
  { id: 3, title: 'Lokasi & Media Sosial', icon: MapPin },
  { id: 4, title: 'Data Fisik', icon: Ruler },
  { id: 5, title: 'Pendidikan & Pengalaman', icon: GraduationCap },
  { id: 6, title: 'Data Keluarga', icon: Users },
  { id: 7, title: 'Khusus Jepang', icon: Heart },
  { id: 8, title: 'LPK & Ketersediaan', icon: Building2 },
]

const PROVINCES = [
  "Aceh", "Sumatera Utara", "Sumatera Barat", "Riau", "Kepulauan Riau", "Jambi",
  "Sumatera Selatan", "Bangka Belitung", "Bengkulu", "Lampung", "DKI Jakarta",
  "Jawa Barat", "Jawa Tengah", "DI Yogyakarta", "Jawa Timur", "Banten",
  "Bali", "Nusa Tenggara Barat", "Nusa Tenggara Timur", "Kalimantan Barat",
  "Kalimantan Tengah", "Kalimantan Selatan", "Kalimantan Timur", "Kalimantan Utara",
  "Sulawesi Utara", "Sulawesi Tengah", "Sulawesi Selatan", "Sulawesi Tenggara",
  "Gorontalo", "Sulawesi Barat", "Maluku", "Maluku Utara", "Papua", "Papua Barat",
  "Papua Selatan", "Papua Tengah", "Papua Pegunungan", "Papua Barat Daya"
]

const BLOOD_TYPES: BloodType[] = ['A', 'B', 'AB', 'O']
const EDUCATION_LEVELS: EducationLevel[] = ['SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1']
const MARITAL_STATUS: MaritalStatus[] = ['lajang', 'menikah', 'bercerai', 'duda_janda']
const AVAILABILITY_STATUS: AvailabilityStatus[] = ['siap', 'belum_siap', 'kondisional']
const RELATIONSHIP_TYPES: RelationshipType[] = ['ayah', 'ibu', 'saudara', 'pasangan', 'anak', 'lainnya']

export function SiswaFormModal({
  isOpen,
  onClose,
  onSubmit,
  editingSiswa,
  lpkOptions,
}: SiswaFormModalProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})

  // Helper function to safely convert undefined to empty string for inputs
  const safeString = (value: any): string => {
    return value !== undefined && value !== null ? String(value) : ''
  }

  // Form data state
  const [formData, setFormData] = useState<SiswaFormData>({
    // Basic Info
    nama_lengkap: '',
    nama_lengkap_jepang: '',
    nik: '',
    tempat_lahir: '',
    tanggal_lahir: '',
    jenis_kelamin: 'L',
    agama: '',
    status_pernikahan: 'lajang',

    // Contact & Address
    alamat_lengkap: '',
    kelurahan: '',
    kecamatan: '',
    kota_kabupaten: '',
    provinsi: '',
    kode_pos: '',
    nomor_hp: '',
    email: '',
    nomor_wa: '',
    
    // Location Coordinates
    latitude: undefined,
    longitude: undefined,
    alamat_koordinat: '',

    // Physical Info
    tinggi_badan: undefined,
    berat_badan: undefined,
    ukuran_sepatu: undefined,
    ukuran_baju: '',
    ukuran_celana: undefined,
    lingkar_kepala: undefined,
    lingkar_pinggang: undefined,
    golongan_darah: undefined,
    riwayat_penyakit: '',
    alergi: '',

    // Education
    pendidikan_terakhir: 'SMA',
    nama_sekolah: '',
    tahun_lulus: undefined,
    jurusan: '',
    ipk: undefined,
    sertifikat_keahlian: '',

    // Family
    nama_ayah: '',
    nama_ibu: '',
    alamat_keluarga: '',
    nomor_hp_keluarga: '',
    pekerjaan_ayah: '',
    pekerjaan_ibu: '',

    // Japanese-specific
    hobi: '',
    bakat_khusus: '',
    minat_kerja: '',
    pengalaman_organisasi: '',
    tujuan_ke_jepang: '',
    target_kerja_jepang: '',
    rencana_setelah_jepang: '',

    // LPK
    lpk_id: '',
    tanggal_masuk_lpk: '',
    lama_belajar_bulan: undefined,
    level_bahasa_jepang: '',
    sertifikat_bahasa: '',
    nilai_ujian_masuk: undefined,

    // Availability
    ketersediaan: 'belum_siap',
    tanggal_ketersediaan: '',
    keterangan_ketersediaan: '',

                    // Related data
        pendidikan: [],
        pengalaman_kerja: [],
        keluarga: [],
        social_media: [],
  })

  // Initialize form data when editing
  useEffect(() => {
    if (editingSiswa) {
      console.log('Initializing form with editingSiswa data:', editingSiswa)
      
      // Map siswa_pendidikan to pendidikan format expected by form
      const mappedPendidikan = (editingSiswa as any).siswa_pendidikan?.map((p: any) => ({
        jenjang: p.jenjang,
        nama_sekolah: p.nama_sekolah,
        jurusan: p.jurusan || '',
        tahun_masuk: p.tahun_masuk || new Date().getFullYear() - 4,
        tahun_lulus: p.tahun_lulus || new Date().getFullYear(),
        ipk: p.ipk,
        keterangan: p.keterangan || '',
      })) || []

      // Map siswa_pengalaman_kerja to pengalaman_kerja format expected by form  
      const mappedPengalamanKerja = (editingSiswa as any).siswa_pengalaman_kerja?.map((p: any) => ({
        nama_perusahaan: p.nama_perusahaan,
        posisi: p.posisi,
        tahun_mulai: p.tahun_mulai,
        tahun_selesai: p.tahun_selesai,
        deskripsi_pekerjaan: p.deskripsi_pekerjaan || '',
        gaji: p.gaji,
        alasan_berhenti: p.alasan_berhenti || '',
      })) || []

      // Map siswa_keluarga to keluarga format expected by form
      const mappedKeluarga = (editingSiswa as any).siswa_keluarga?.map((k: any) => ({
        nama_lengkap: k.nama_lengkap,
        hubungan: k.hubungan,
        tanggal_lahir: k.tanggal_lahir || '',
        pekerjaan: k.pekerjaan || '',
        nomor_hp: k.nomor_hp || '',
        alamat: k.alamat || '',
      })) || []

      // Map siswa_social_media to social_media format expected by form
      const mappedSocialMedia = (editingSiswa as any).siswa_social_media?.map((s: any) => ({
        platform: s.platform,
        username: s.username,
        url: s.url || '',
        is_active: s.is_active !== undefined ? s.is_active : true,
      })) || []

      console.log('Mapped pendidikan:', mappedPendidikan)
      console.log('Mapped pengalaman_kerja:', mappedPengalamanKerja)
      console.log('Mapped keluarga:', mappedKeluarga)
      console.log('Mapped social_media:', mappedSocialMedia)

      setFormData({
        // Basic Info
        nama_lengkap: editingSiswa.nama_lengkap || '',
        nama_lengkap_jepang: editingSiswa.nama_lengkap_jepang || '',
        nik: editingSiswa.nik || '',
        tempat_lahir: editingSiswa.tempat_lahir || '',
        tanggal_lahir: editingSiswa.tanggal_lahir || '',
        jenis_kelamin: editingSiswa.jenis_kelamin || 'L',
        agama: editingSiswa.agama || '',
        status_pernikahan: editingSiswa.status_pernikahan || 'lajang',

        // Contact & Address
        alamat_lengkap: editingSiswa.alamat_lengkap || '',
        kelurahan: editingSiswa.kelurahan || '',
        kecamatan: editingSiswa.kecamatan || '',
        kota_kabupaten: editingSiswa.kota_kabupaten || '',
        provinsi: editingSiswa.provinsi || '',
        kode_pos: editingSiswa.kode_pos || '',
        nomor_hp: editingSiswa.nomor_hp || '',
        email: editingSiswa.email || '',
        nomor_wa: editingSiswa.nomor_wa || '',
        
        // Location Coordinates
        latitude: editingSiswa.latitude,
        longitude: editingSiswa.longitude,
        alamat_koordinat: editingSiswa.alamat_koordinat || '',

        // Physical Info - NOW PROPERLY LOADED
        tinggi_badan: editingSiswa.tinggi_badan,
        berat_badan: editingSiswa.berat_badan,
        ukuran_sepatu: editingSiswa.ukuran_sepatu,
        ukuran_baju: editingSiswa.ukuran_baju || '',
        ukuran_celana: editingSiswa.ukuran_celana,
        lingkar_kepala: editingSiswa.lingkar_kepala,
        lingkar_pinggang: editingSiswa.lingkar_pinggang,
        golongan_darah: editingSiswa.golongan_darah,
        riwayat_penyakit: editingSiswa.riwayat_penyakit || '',
        alergi: editingSiswa.alergi || '',

        // Education
        pendidikan_terakhir: editingSiswa.pendidikan_terakhir || 'SMA',
        nama_sekolah: editingSiswa.nama_sekolah || '',
        tahun_lulus: editingSiswa.tahun_lulus,
        jurusan: editingSiswa.jurusan || '',
        ipk: editingSiswa.ipk,
        sertifikat_keahlian: editingSiswa.sertifikat_keahlian || '',

        // Family
        nama_ayah: editingSiswa.nama_ayah || '',
        nama_ibu: editingSiswa.nama_ibu || '',
        alamat_keluarga: editingSiswa.alamat_keluarga || '',
        nomor_hp_keluarga: editingSiswa.nomor_hp_keluarga || '',
        pekerjaan_ayah: editingSiswa.pekerjaan_ayah || '',
        pekerjaan_ibu: editingSiswa.pekerjaan_ibu || '',

        // Japanese-specific - NOW PROPERLY LOADED
        hobi: editingSiswa.hobi || '',
        bakat_khusus: editingSiswa.bakat_khusus || '',
        minat_kerja: editingSiswa.minat_kerja || '',
        pengalaman_organisasi: editingSiswa.pengalaman_organisasi || '',
        tujuan_ke_jepang: editingSiswa.tujuan_ke_jepang || '',
        target_kerja_jepang: editingSiswa.target_kerja_jepang || '',
        rencana_setelah_jepang: editingSiswa.rencana_setelah_jepang || '',

        // LPK - NOW PROPERLY LOADED
        lpk_id: editingSiswa.lpk_id || '',
        tanggal_masuk_lpk: editingSiswa.tanggal_masuk_lpk || '',
        lama_belajar_bulan: editingSiswa.lama_belajar_bulan,
        level_bahasa_jepang: editingSiswa.level_bahasa_jepang || '',
        sertifikat_bahasa: editingSiswa.sertifikat_bahasa || '',
        nilai_ujian_masuk: editingSiswa.nilai_ujian_masuk,

        // Availability - NOW PROPERLY LOADED
        ketersediaan: editingSiswa.ketersediaan || 'belum_siap',
        tanggal_ketersediaan: editingSiswa.tanggal_ketersediaan || '',
        keterangan_ketersediaan: editingSiswa.keterangan_ketersediaan || '',

        // Related data with proper mapping
        pendidikan: mappedPendidikan,
        pengalaman_kerja: mappedPengalamanKerja,
        keluarga: mappedKeluarga,
        social_media: mappedSocialMedia,
      })
    } else {
      // Reset form for new siswa
      setFormData({
        // Basic Info
        nama_lengkap: '',
        nama_lengkap_jepang: '',
        nik: '',
        tempat_lahir: '',
        tanggal_lahir: '',
        jenis_kelamin: 'L',
        agama: '',
        status_pernikahan: 'lajang',

        // Contact & Address
        alamat_lengkap: '',
        kelurahan: '',
        kecamatan: '',
        kota_kabupaten: '',
        provinsi: '',
        kode_pos: '',
        nomor_hp: '',
        email: '',
        nomor_wa: '',
        
        // Location Coordinates
        latitude: undefined,
        longitude: undefined,
        alamat_koordinat: '',

        // Physical Info
        tinggi_badan: undefined,
        berat_badan: undefined,
        ukuran_sepatu: undefined,
        ukuran_baju: '',
        ukuran_celana: undefined,
        lingkar_kepala: undefined,
        lingkar_pinggang: undefined,
        golongan_darah: undefined,
        riwayat_penyakit: '',
        alergi: '',

        // Education
        pendidikan_terakhir: 'SMA',
        nama_sekolah: '',
        tahun_lulus: undefined,
        jurusan: '',
        ipk: undefined,
        sertifikat_keahlian: '',

        // Family
        nama_ayah: '',
        nama_ibu: '',
        alamat_keluarga: '',
        nomor_hp_keluarga: '',
        pekerjaan_ayah: '',
        pekerjaan_ibu: '',

        // Japanese-specific
        hobi: '',
        bakat_khusus: '',
        minat_kerja: '',
        pengalaman_organisasi: '',
        tujuan_ke_jepang: '',
        target_kerja_jepang: '',
        rencana_setelah_jepang: '',

        // LPK
        lpk_id: '',
        tanggal_masuk_lpk: '',
        lama_belajar_bulan: undefined,
        level_bahasa_jepang: '',
        sertifikat_bahasa: '',
        nilai_ujian_masuk: undefined,

        // Availability
        ketersediaan: 'belum_siap',
        tanggal_ketersediaan: '',
        keterangan_ketersediaan: '',

        // Related data
        pendidikan: [],
        pengalaman_kerja: [],
        keluarga: [],
      })
    }
  }, [editingSiswa])

  // Validation for current step
  const validateStep = (step: number): boolean => {
    const newErrors: FormErrors = {}

    switch (step) {
      case 1: // Data Pribadi
        if (!formData.nama_lengkap.trim()) newErrors.nama_lengkap = 'Nama lengkap wajib diisi'
        if (!formData.nik.trim()) newErrors.nik = 'NIK wajib diisi'
        else if (!/^\d{16}$/.test(formData.nik)) newErrors.nik = 'NIK harus 16 digit'
        if (!formData.tempat_lahir.trim()) newErrors.tempat_lahir = 'Tempat lahir wajib diisi'
        if (!formData.tanggal_lahir.trim()) newErrors.tanggal_lahir = 'Tanggal lahir wajib diisi'
        if (!formData.agama.trim()) newErrors.agama = 'Agama wajib diisi'
        break

      case 2: // Kontak & Alamat
        if (!formData.alamat_lengkap.trim()) newErrors.alamat_lengkap = 'Alamat lengkap wajib diisi'
        if (!formData.kelurahan.trim()) newErrors.kelurahan = 'Kelurahan wajib diisi'
        if (!formData.kecamatan.trim()) newErrors.kecamatan = 'Kecamatan wajib diisi'
        if (!formData.kota_kabupaten.trim()) newErrors.kota_kabupaten = 'Kota/Kabupaten wajib diisi'
        if (!formData.provinsi.trim()) newErrors.provinsi = 'Provinsi wajib diisi'
        if (!formData.nomor_hp.trim()) newErrors.nomor_hp = 'Nomor HP wajib diisi'
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          newErrors.email = 'Format email tidak valid'
        }
        break

      case 5: // Pendidikan & Pengalaman
        if (!formData.nama_sekolah?.trim()) newErrors.nama_sekolah = 'Nama sekolah wajib diisi'
        if (!formData.tahun_lulus) newErrors.tahun_lulus = 'Tahun lulus wajib diisi'
        break

      case 8: // LPK & Ketersediaan
        if (!formData.lpk_id) newErrors.lpk_id = 'LPK Mitra wajib dipilih'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, FORM_STEPS.length))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Dynamic section handlers
  const addPendidikan = () => {
    setFormData(prev => ({
      ...prev,
      pendidikan: [...(prev.pendidikan || []), {
        jenjang: 'SMA' as EducationLevel,
        nama_sekolah: '',
        jurusan: '',
        tahun_masuk: new Date().getFullYear() - 4,
        tahun_lulus: new Date().getFullYear(),
        ipk: undefined,
        keterangan: '',
      }]
    }))
  }

  const removePendidikan = (index: number) => {
    setFormData(prev => ({
      ...prev,
      pendidikan: prev.pendidikan?.filter((_, i) => i !== index) || []
    }))
  }

  const updatePendidikan = (index: number, field: keyof SiswaPendidikan, value: any) => {
    setFormData(prev => ({
      ...prev,
      pendidikan: prev.pendidikan?.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ) || []
    }))
  }

  const addPengalamanKerja = () => {
    setFormData(prev => ({
      ...prev,
      pengalaman_kerja: [...(prev.pengalaman_kerja || []), {
        nama_perusahaan: '',
        posisi: '',
        tahun_mulai: new Date().getFullYear() - 2,
        tahun_selesai: new Date().getFullYear(),
        deskripsi_pekerjaan: '',
        gaji: undefined,
        alasan_berhenti: '',
      }]
    }))
  }

  const removePengalamanKerja = (index: number) => {
    setFormData(prev => ({
      ...prev,
      pengalaman_kerja: prev.pengalaman_kerja?.filter((_, i) => i !== index) || []
    }))
  }

  const updatePengalamanKerja = (index: number, field: keyof SiswaPengalamanKerja, value: any) => {
    setFormData(prev => ({
      ...prev,
      pengalaman_kerja: prev.pengalaman_kerja?.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ) || []
    }))
  }

  const addKeluarga = () => {
    setFormData(prev => ({
      ...prev,
      keluarga: [...(prev.keluarga || []), {
        nama_lengkap: '',
        hubungan: 'saudara' as RelationshipType,
        tanggal_lahir: '',
        pekerjaan: '',
        nomor_hp: '',
        alamat: '',
      }]
    }))
  }

  const removeKeluarga = (index: number) => {
    setFormData(prev => ({
      ...prev,
      keluarga: prev.keluarga?.filter((_, i) => i !== index) || []
    }))
  }

  const updateKeluarga = (index: number, field: keyof SiswaKeluarga, value: any) => {
    setFormData(prev => ({
      ...prev,
      keluarga: prev.keluarga?.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ) || []
    }))
  }

  const handleLocationChange = (lat: number, lng: number, address: string) => {
    setFormData(prev => ({
      ...prev,
      latitude: lat,
      longitude: lng,
      alamat_koordinat: address
    }))
  }

  const handleSocialMediaChange = (socialMedia: any[]) => {
    setFormData(prev => ({
      ...prev,
      social_media: socialMedia
    }))
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1: // Data Pribadi
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="nama_lengkap">Nama Lengkap <span className="text-red-500">*</span></Label>
                <Input
                  id="nama_lengkap"
                  value={formData.nama_lengkap}
                  onChange={(e) => setFormData(prev => ({ ...prev, nama_lengkap: e.target.value }))}
                  placeholder="Nama lengkap siswa"
                  className={errors.nama_lengkap ? "border-red-500" : ""}
                />
                {errors.nama_lengkap && <p className="text-sm text-red-600 mt-1">{errors.nama_lengkap}</p>}
              </div>

              <div>
                <Label htmlFor="nama_lengkap_jepang">Nama Jepang (氏名)</Label>
                <Input
                  id="nama_lengkap_jepang"
                  value={formData.nama_lengkap_jepang || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, nama_lengkap_jepang: e.target.value }))}
                  placeholder="Nama dalam huruf Jepang"
                />
              </div>

              <div>
                <Label htmlFor="nik">NIK <span className="text-red-500">*</span></Label>
                <Input
                  id="nik"
                  value={formData.nik}
                  onChange={(e) => setFormData(prev => ({ ...prev, nik: e.target.value }))}
                  placeholder="16 digit NIK"
                  maxLength={16}
                  className={errors.nik ? "border-red-500" : ""}
                />
                {errors.nik && <p className="text-sm text-red-600 mt-1">{errors.nik}</p>}
              </div>

              <div>
                <Label htmlFor="tempat_lahir">Tempat Lahir <span className="text-red-500">*</span></Label>
                <Input
                  id="tempat_lahir"
                  value={formData.tempat_lahir}
                  onChange={(e) => setFormData(prev => ({ ...prev, tempat_lahir: e.target.value }))}
                  placeholder="Kota tempat lahir"
                  className={errors.tempat_lahir ? "border-red-500" : ""}
                />
                {errors.tempat_lahir && <p className="text-sm text-red-600 mt-1">{errors.tempat_lahir}</p>}
              </div>

              <div>
                <Label htmlFor="tanggal_lahir">Tanggal Lahir <span className="text-red-500">*</span></Label>
                <Input
                  id="tanggal_lahir"
                  type="date"
                  value={formData.tanggal_lahir}
                  onChange={(e) => setFormData(prev => ({ ...prev, tanggal_lahir: e.target.value }))}
                  className={errors.tanggal_lahir ? "border-red-500" : ""}
                />
                {errors.tanggal_lahir && <p className="text-sm text-red-600 mt-1">{errors.tanggal_lahir}</p>}
              </div>

              <div>
                <Label htmlFor="jenis_kelamin">Jenis Kelamin <span className="text-red-500">*</span></Label>
                <Select value={formData.jenis_kelamin} onValueChange={(value: 'L' | 'P') => 
                  setFormData(prev => ({ ...prev, jenis_kelamin: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="agama">Agama <span className="text-red-500">*</span></Label>
                <Select value={formData.agama} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, agama: value }))}>
                  <SelectTrigger className={errors.agama ? "border-red-500" : ""}>
                    <SelectValue placeholder="Pilih agama" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Islam">Islam</SelectItem>
                    <SelectItem value="Kristen">Kristen</SelectItem>
                    <SelectItem value="Katolik">Katolik</SelectItem>
                    <SelectItem value="Hindu">Hindu</SelectItem>
                    <SelectItem value="Buddha">Buddha</SelectItem>
                    <SelectItem value="Khonghucu">Khonghucu</SelectItem>
                  </SelectContent>
                </Select>
                {errors.agama && <p className="text-sm text-red-600 mt-1">{errors.agama}</p>}
              </div>

              <div>
                <Label htmlFor="status_pernikahan">Status Pernikahan</Label>
                <Select value={formData.status_pernikahan} onValueChange={(value: MaritalStatus) => 
                  setFormData(prev => ({ ...prev, status_pernikahan: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih status pernikahan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="lajang">Lajang</SelectItem>
                    <SelectItem value="menikah">Menikah</SelectItem>
                    <SelectItem value="bercerai">Bercerai</SelectItem>
                    <SelectItem value="duda_janda">Duda/Janda</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )

      case 2: // Kontak & Alamat
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="alamat_lengkap">Alamat Lengkap <span className="text-red-500">*</span></Label>
              <Textarea
                id="alamat_lengkap"
                value={formData.alamat_lengkap}
                onChange={(e) => setFormData(prev => ({ ...prev, alamat_lengkap: e.target.value }))}
                placeholder="Alamat lengkap tempat tinggal"
                className={errors.alamat_lengkap ? "border-red-500" : ""}
                rows={3}
              />
              {errors.alamat_lengkap && <p className="text-sm text-red-600 mt-1">{errors.alamat_lengkap}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="kelurahan">Kelurahan <span className="text-red-500">*</span></Label>
                <Input
                  id="kelurahan"
                  value={formData.kelurahan}
                  onChange={(e) => setFormData(prev => ({ ...prev, kelurahan: e.target.value }))}
                  placeholder="Nama kelurahan"
                  className={errors.kelurahan ? "border-red-500" : ""}
                />
                {errors.kelurahan && <p className="text-sm text-red-600 mt-1">{errors.kelurahan}</p>}
              </div>

              <div>
                <Label htmlFor="kecamatan">Kecamatan <span className="text-red-500">*</span></Label>
                <Input
                  id="kecamatan"
                  value={formData.kecamatan}
                  onChange={(e) => setFormData(prev => ({ ...prev, kecamatan: e.target.value }))}
                  placeholder="Nama kecamatan"
                  className={errors.kecamatan ? "border-red-500" : ""}
                />
                {errors.kecamatan && <p className="text-sm text-red-600 mt-1">{errors.kecamatan}</p>}
              </div>

              <div>
                <Label htmlFor="kota_kabupaten">Kota/Kabupaten <span className="text-red-500">*</span></Label>
                <Input
                  id="kota_kabupaten"
                  value={formData.kota_kabupaten}
                  onChange={(e) => setFormData(prev => ({ ...prev, kota_kabupaten: e.target.value }))}
                  placeholder="Nama kota/kabupaten"
                  className={errors.kota_kabupaten ? "border-red-500" : ""}
                />
                {errors.kota_kabupaten && <p className="text-sm text-red-600 mt-1">{errors.kota_kabupaten}</p>}
              </div>

              <div>
                <Label htmlFor="provinsi">Provinsi <span className="text-red-500">*</span></Label>
                <Select value={formData.provinsi} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, provinsi: value }))}>
                  <SelectTrigger className={errors.provinsi ? "border-red-500" : ""}>
                    <SelectValue placeholder="Pilih provinsi" />
                  </SelectTrigger>
                  <SelectContent>
                    {PROVINCES.map(province => (
                      <SelectItem key={province} value={province}>{province}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.provinsi && <p className="text-sm text-red-600 mt-1">{errors.provinsi}</p>}
              </div>

              <div>
                <Label htmlFor="kode_pos">Kode Pos</Label>
                <Input
                  id="kode_pos"
                  value={formData.kode_pos || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, kode_pos: e.target.value }))}
                  placeholder="Kode pos"
                  maxLength={5}
                />
              </div>

              <div>
                <Label htmlFor="nomor_hp">Nomor HP <span className="text-red-500">*</span></Label>
                <Input
                  id="nomor_hp"
                  value={formData.nomor_hp}
                  onChange={(e) => setFormData(prev => ({ ...prev, nomor_hp: e.target.value }))}
                  placeholder="08xxxxxxxxxx"
                  className={errors.nomor_hp ? "border-red-500" : ""}
                />
                {errors.nomor_hp && <p className="text-sm text-red-600 mt-1">{errors.nomor_hp}</p>}
              </div>

              <div>
                <Label htmlFor="nomor_wa">Nomor WhatsApp</Label>
                <Input
                  id="nomor_wa"
                  value={formData.nomor_wa || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, nomor_wa: e.target.value }))}
                  placeholder="08xxxxxxxxxx"
                />
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && <p className="text-sm text-red-600 mt-1">{errors.email}</p>}
              </div>
            </div>
          </div>
        )

      case 3: // Lokasi & Media Sosial
        return (
          <div className="space-y-6">
            {/* Map Picker */}
            <MapPicker
              latitude={formData.latitude}
              longitude={formData.longitude}
              alamat_koordinat={formData.alamat_koordinat}
              onLocationChange={handleLocationChange}
            />

            {/* Social Media Form */}
            <SocialMediaForm
              socialMedia={formData.social_media || []}
              onChange={handleSocialMediaChange}
            />
          </div>
        )

      case 4: // Data Fisik
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="tinggi_badan">Tinggi Badan (cm)</Label>
                <Input
                  id="tinggi_badan"
                  type="number"
                  value={safeString(formData.tinggi_badan)}
                  onChange={(e) => setFormData(prev => ({ ...prev, tinggi_badan: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="170"
                />
              </div>

              <div>
                <Label htmlFor="berat_badan">Berat Badan (kg)</Label>
                <Input
                  id="berat_badan"
                  type="number"
                  value={safeString(formData.berat_badan)}
                  onChange={(e) => setFormData(prev => ({ ...prev, berat_badan: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="65"
                />
              </div>

              <div>
                <Label htmlFor="golongan_darah">Golongan Darah</Label>
                                  <Select value={formData.golongan_darah || undefined} onValueChange={(value: BloodType) => 
                  setFormData(prev => ({ ...prev, golongan_darah: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih golongan darah" />
                  </SelectTrigger>
                  <SelectContent>
                    {BLOOD_TYPES.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="ukuran_sepatu">Ukuran Sepatu</Label>
                <Input
                  id="ukuran_sepatu"
                  type="number"
                  value={safeString(formData.ukuran_sepatu)}
                  onChange={(e) => setFormData(prev => ({ ...prev, ukuran_sepatu: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="42"
                />
              </div>

              <div>
                <Label htmlFor="ukuran_baju">Ukuran Baju</Label>
                <Select value={formData.ukuran_baju || ''} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, ukuran_baju: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih ukuran baju" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="XS">XS</SelectItem>
                    <SelectItem value="S">S</SelectItem>
                    <SelectItem value="M">M</SelectItem>
                    <SelectItem value="L">L</SelectItem>
                    <SelectItem value="XL">XL</SelectItem>
                    <SelectItem value="XXL">XXL</SelectItem>
                    <SelectItem value="XXXL">XXXL</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="ukuran_celana">Ukuran Celana</Label>
                <Input
                  id="ukuran_celana"
                  type="number"
                  value={safeString(formData.ukuran_celana)}
                  onChange={(e) => setFormData(prev => ({ ...prev, ukuran_celana: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="32"
                />
              </div>

              <div>
                <Label htmlFor="lingkar_kepala">Lingkar Kepala (cm)</Label>
                <Input
                  id="lingkar_kepala"
                  type="number"
                  value={safeString(formData.lingkar_kepala)}
                  onChange={(e) => setFormData(prev => ({ ...prev, lingkar_kepala: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="58"
                />
              </div>

              <div>
                <Label htmlFor="lingkar_pinggang">Lingkar Pinggang (cm)</Label>
                <Input
                  id="lingkar_pinggang"
                  type="number"
                  value={safeString(formData.lingkar_pinggang)}
                  onChange={(e) => setFormData(prev => ({ ...prev, lingkar_pinggang: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="80"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="riwayat_penyakit">Riwayat Penyakit</Label>
              <Textarea
                id="riwayat_penyakit"
                value={formData.riwayat_penyakit || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, riwayat_penyakit: e.target.value }))}
                placeholder="Riwayat penyakit yang pernah diderita"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="alergi">Alergi</Label>
              <Textarea
                id="alergi"
                value={formData.alergi || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, alergi: e.target.value }))}
                placeholder="Alergi makanan, obat, atau lainnya"
                rows={2}
              />
            </div>
          </div>
        )

      case 5: // Pendidikan & Pengalaman
        return (
          <div className="space-y-6">
            {/* Pendidikan Utama */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Pendidikan Terakhir</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="pendidikan_terakhir">Jenjang Pendidikan <span className="text-red-500">*</span></Label>
                  <Select value={formData.pendidikan_terakhir} onValueChange={(value: EducationLevel) => 
                    setFormData(prev => ({ ...prev, pendidikan_terakhir: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih jenjang" />
                    </SelectTrigger>
                    <SelectContent>
                      {EDUCATION_LEVELS.map(level => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="nama_sekolah">Nama Sekolah <span className="text-red-500">*</span></Label>
                  <Input
                    id="nama_sekolah"
                    value={formData.nama_sekolah || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, nama_sekolah: e.target.value }))}
                    placeholder="Nama institusi pendidikan"
                    className={errors.nama_sekolah ? "border-red-500" : ""}
                  />
                  {errors.nama_sekolah && <p className="text-sm text-red-600 mt-1">{errors.nama_sekolah}</p>}
                </div>

                <div>
                  <Label htmlFor="jurusan">Jurusan</Label>
                  <Input
                    id="jurusan"
                    value={formData.jurusan || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, jurusan: e.target.value }))}
                    placeholder="Jurusan/program studi"
                  />
                </div>

                <div>
                  <Label htmlFor="tahun_lulus">Tahun Lulus <span className="text-red-500">*</span></Label>
                  <Input
                    id="tahun_lulus"
                    type="number"
                    value={safeString(formData.tahun_lulus)}
                    onChange={(e) => setFormData(prev => ({ ...prev, tahun_lulus: e.target.value ? parseInt(e.target.value) : undefined }))}
                    placeholder="2020"
                    className={errors.tahun_lulus ? "border-red-500" : ""}
                  />
                  {errors.tahun_lulus && <p className="text-sm text-red-600 mt-1">{errors.tahun_lulus}</p>}
                </div>

                <div>
                  <Label htmlFor="ipk">IPK/Nilai</Label>
                  <Input
                    id="ipk"
                    type="number"
                    step="0.01"
                    value={safeString(formData.ipk)}
                    onChange={(e) => setFormData(prev => ({ ...prev, ipk: e.target.value ? parseFloat(e.target.value) : undefined }))}
                    placeholder="3.50"
                  />
                </div>

                <div>
                  <Label htmlFor="sertifikat_keahlian">Sertifikat Keahlian</Label>
                  <Input
                    id="sertifikat_keahlian"
                    value={formData.sertifikat_keahlian || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, sertifikat_keahlian: e.target.value }))}
                    placeholder="Sertifikat yang dimiliki"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Riwayat Pendidikan Tambahan */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Riwayat Pendidikan Lainnya</h4>
                <Button type="button" variant="outline" size="sm" onClick={addPendidikan}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pendidikan
                </Button>
              </div>

              {formData.pendidikan?.map((edu, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Pendidikan #{index + 1}</CardTitle>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removePendidikan(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Jenjang</Label>
                        <Select value={edu.jenjang} onValueChange={(value: EducationLevel) => 
                          updatePendidikan(index, 'jenjang', value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {EDUCATION_LEVELS.map(level => (
                              <SelectItem key={level} value={level}>{level}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Nama Institusi</Label>
                        <Input
                          value={edu.nama_sekolah}
                          onChange={(e) => updatePendidikan(index, 'nama_sekolah', e.target.value)}
                          placeholder="Nama sekolah/universitas"
                        />
                      </div>
                      <div>
                        <Label>Tahun Masuk</Label>
                        <Input
                          type="number"
                          value={edu.tahun_masuk}
                          onChange={(e) => updatePendidikan(index, 'tahun_masuk', parseInt(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label>Tahun Lulus</Label>
                        <Input
                          type="number"
                          value={edu.tahun_lulus}
                          onChange={(e) => updatePendidikan(index, 'tahun_lulus', parseInt(e.target.value))}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Separator />

            {/* Pengalaman Kerja */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Pengalaman Kerja</h4>
                <Button type="button" variant="outline" size="sm" onClick={addPengalamanKerja}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pengalaman
                </Button>
              </div>

              {formData.pengalaman_kerja?.map((exp, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Pengalaman #{index + 1}</CardTitle>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removePengalamanKerja(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Nama Perusahaan</Label>
                        <Input
                          value={exp.nama_perusahaan}
                          onChange={(e) => updatePengalamanKerja(index, 'nama_perusahaan', e.target.value)}
                          placeholder="Nama perusahaan"
                        />
                      </div>
                      <div>
                        <Label>Posisi</Label>
                        <Input
                          value={exp.posisi}
                          onChange={(e) => updatePengalamanKerja(index, 'posisi', e.target.value)}
                          placeholder="Jabatan/posisi"
                        />
                      </div>
                      <div>
                        <Label>Tahun Mulai</Label>
                        <Input
                          type="number"
                          value={exp.tahun_mulai}
                          onChange={(e) => updatePengalamanKerja(index, 'tahun_mulai', parseInt(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label>Tahun Selesai</Label>
                        <Input
                          type="number"
                          value={safeString(exp.tahun_selesai)}
                          onChange={(e) => updatePengalamanKerja(index, 'tahun_selesai', e.target.value ? parseInt(e.target.value) : undefined)}
                          placeholder="Kosongkan jika masih bekerja"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )

      case 6: // Data Keluarga
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nama_ayah">Nama Ayah</Label>
                <Input
                  id="nama_ayah"
                  value={formData.nama_ayah || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, nama_ayah: e.target.value }))}
                  placeholder="Nama lengkap ayah"
                />
              </div>

              <div>
                <Label htmlFor="nama_ibu">Nama Ibu</Label>
                <Input
                  id="nama_ibu"
                  value={formData.nama_ibu || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, nama_ibu: e.target.value }))}
                  placeholder="Nama lengkap ibu"
                />
              </div>

              <div>
                <Label htmlFor="pekerjaan_ayah">Pekerjaan Ayah</Label>
                <Input
                  id="pekerjaan_ayah"
                  value={formData.pekerjaan_ayah || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, pekerjaan_ayah: e.target.value }))}
                  placeholder="Pekerjaan ayah"
                />
              </div>

              <div>
                <Label htmlFor="pekerjaan_ibu">Pekerjaan Ibu</Label>
                <Input
                  id="pekerjaan_ibu"
                  value={formData.pekerjaan_ibu || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, pekerjaan_ibu: e.target.value }))}
                  placeholder="Pekerjaan ibu"
                />
              </div>

              <div>
                <Label htmlFor="alamat_keluarga">Alamat Keluarga</Label>
                <Input
                  id="alamat_keluarga"
                  value={formData.alamat_keluarga || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, alamat_keluarga: e.target.value }))}
                  placeholder="Alamat orang tua"
                />
              </div>

              <div>
                <Label htmlFor="nomor_hp_keluarga">Nomor HP Keluarga</Label>
                <Input
                  id="nomor_hp_keluarga"
                  value={formData.nomor_hp_keluarga || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, nomor_hp_keluarga: e.target.value }))}
                  placeholder="Nomor HP orang tua"
                />
              </div>
            </div>

            <Separator />

            {/* Anggota Keluarga Lainnya */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Anggota Keluarga Lainnya</h4>
                <Button type="button" variant="outline" size="sm" onClick={addKeluarga}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Keluarga
                </Button>
              </div>

              {formData.keluarga?.map((family, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">Keluarga #{index + 1}</CardTitle>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeKeluarga(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Nama Lengkap</Label>
                        <Input
                          value={family.nama_lengkap}
                          onChange={(e) => updateKeluarga(index, 'nama_lengkap', e.target.value)}
                          placeholder="Nama lengkap"
                        />
                      </div>
                      <div>
                        <Label>Hubungan</Label>
                        <Select value={family.hubungan} onValueChange={(value: RelationshipType) => 
                          updateKeluarga(index, 'hubungan', value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {RELATIONSHIP_TYPES.map(type => (
                              <SelectItem key={type} value={type}>
                                {type.charAt(0).toUpperCase() + type.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Pekerjaan</Label>
                        <Input
                          value={family.pekerjaan || ''}
                          onChange={(e) => updateKeluarga(index, 'pekerjaan', e.target.value)}
                          placeholder="Pekerjaan"
                        />
                      </div>
                      <div>
                        <Label>Nomor HP</Label>
                        <Input
                          value={family.nomor_hp || ''}
                          onChange={(e) => updateKeluarga(index, 'nomor_hp', e.target.value)}
                          placeholder="Nomor HP"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )

      case 7: // Khusus Jepang
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="hobi">Hobi</Label>
              <Textarea
                id="hobi"
                value={formData.hobi || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, hobi: e.target.value }))}
                placeholder="Hobi dan kegemaran"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="bakat_khusus">Bakat Khusus</Label>
              <Textarea
                id="bakat_khusus"
                value={formData.bakat_khusus || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, bakat_khusus: e.target.value }))}
                placeholder="Bakat atau kemampuan khusus"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="minat_kerja">Minat Kerja</Label>
              <Textarea
                id="minat_kerja"
                value={formData.minat_kerja || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, minat_kerja: e.target.value }))}
                placeholder="Jenis pekerjaan yang diminati"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="pengalaman_organisasi">Pengalaman Organisasi</Label>
              <Textarea
                id="pengalaman_organisasi"
                value={formData.pengalaman_organisasi || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, pengalaman_organisasi: e.target.value }))}
                placeholder="Pengalaman berorganisasi"
                rows={2}
              />
            </div>

            <div>
                                      <Label htmlFor="tujuan_ke_jepang">Tujuan ke Jepang</Label>
                        <Textarea
                          id="tujuan_ke_jepang"
                          value={formData.tujuan_ke_jepang || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, tujuan_ke_jepang: e.target.value }))}
                placeholder="Motivasi dan alasan ingin ke Jepang"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="target_kerja_jepang">Target Kerja di Jepang</Label>
              <Textarea
                id="target_kerja_jepang"
                value={formData.target_kerja_jepang || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, target_kerja_jepang: e.target.value }))}
                placeholder="Target dan tujuan selama bekerja di Jepang"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="rencana_setelah_jepang">Rencana Setelah Pulang</Label>
              <Textarea
                id="rencana_setelah_jepang"
                value={formData.rencana_setelah_jepang || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, rencana_setelah_jepang: e.target.value }))}
                placeholder="Rencana setelah kembali dari Jepang"
                rows={3}
              />
            </div>
          </div>
        )

      case 8: // LPK & Ketersediaan
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="lpk_id">LPK Mitra <span className="text-red-500">*</span></Label>
                <Select value={formData.lpk_id} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, lpk_id: value }))}>
                  <SelectTrigger className={errors.lpk_id ? "border-red-500" : ""}>
                    <SelectValue placeholder="Pilih LPK Mitra" />
                  </SelectTrigger>
                  <SelectContent>
                    {lpkOptions.map(lpk => (
                      <SelectItem key={lpk.id} value={lpk.id}>{lpk.nama_lpk}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.lpk_id && <p className="text-sm text-red-600 mt-1">{errors.lpk_id}</p>}
              </div>

              <div>
                <Label htmlFor="tanggal_masuk_lpk">Tanggal Masuk LPK</Label>
                <Input
                  id="tanggal_masuk_lpk"
                  type="date"
                  value={formData.tanggal_masuk_lpk || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, tanggal_masuk_lpk: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="lama_belajar_bulan">Lama Belajar (bulan)</Label>
                <Input
                  id="lama_belajar_bulan"
                  type="number"
                  value={safeString(formData.lama_belajar_bulan)}
                  onChange={(e) => setFormData(prev => ({ ...prev, lama_belajar_bulan: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="12"
                />
              </div>

              <div>
                <Label htmlFor="level_bahasa_jepang">Level Bahasa Jepang</Label>
                                  <Select value={formData.level_bahasa_jepang || undefined} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, level_bahasa_jepang: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="N5">N5 (Dasar)</SelectItem>
                    <SelectItem value="N4">N4 (Menengah Awal)</SelectItem>
                    <SelectItem value="N3">N3 (Menengah)</SelectItem>
                    <SelectItem value="N2">N2 (Menengah Atas)</SelectItem>
                    <SelectItem value="N1">N1 (Mahir)</SelectItem>
                    <SelectItem value="Belum Ada">Belum Ada</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="sertifikat_bahasa">Sertifikat Bahasa</Label>
                <Input
                  id="sertifikat_bahasa"
                  value={formData.sertifikat_bahasa || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, sertifikat_bahasa: e.target.value }))}
                  placeholder="JLPT N5, JLPT N4, dll"
                />
              </div>

              <div>
                <Label htmlFor="nilai_ujian_masuk">Nilai Ujian Masuk</Label>
                <Input
                  id="nilai_ujian_masuk"
                  type="number"
                  value={safeString(formData.nilai_ujian_masuk)}
                  onChange={(e) => setFormData(prev => ({ ...prev, nilai_ujian_masuk: e.target.value ? parseInt(e.target.value) : undefined }))}
                  placeholder="85"
                />
              </div>

              <div>
                <Label htmlFor="ketersediaan">Status Ketersediaan</Label>
                <Select value={formData.ketersediaan} onValueChange={(value: AvailabilityStatus) => 
                  setFormData(prev => ({ ...prev, ketersediaan: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="siap">Siap</SelectItem>
                    <SelectItem value="belum_siap">Belum Siap</SelectItem>
                    <SelectItem value="kondisional">Kondisional</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="tanggal_ketersediaan">Tanggal Ketersediaan</Label>
                <Input
                  id="tanggal_ketersediaan"
                  type="date"
                  value={formData.tanggal_ketersediaan || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, tanggal_ketersediaan: e.target.value }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="keterangan_ketersediaan">Keterangan Ketersediaan</Label>
              <Textarea
                id="keterangan_ketersediaan"
                value={formData.keterangan_ketersediaan || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, keterangan_ketersediaan: e.target.value }))}
                placeholder="Keterangan tambahan tentang ketersediaan"
                rows={3}
              />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {editingSiswa ? "Edit Data Siswa" : "Tambah Siswa Baru"}
          </DialogTitle>
          <DialogDescription>
            {editingSiswa ? "Perbarui informasi siswa" : "Lengkapi informasi siswa untuk program magang Jepang"}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              {FORM_STEPS.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div
                      className={`
                        flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                        ${isActive 
                          ? 'bg-blue-600 text-white' 
                          : isCompleted 
                            ? 'bg-green-600 text-white' 
                            : 'bg-gray-200 text-gray-600'
                        }
                      `}
                    >
                      {isCompleted ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Icon className="h-4 w-4" />
                      )}
                    </div>
                    {index < FORM_STEPS.length - 1 && (
                      <div className={`h-0.5 w-8 mx-2 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">
                {FORM_STEPS[currentStep - 1]?.title}
              </h3>
              <Badge variant="secondary">
                {currentStep} / {FORM_STEPS.length}
              </Badge>
            </div>
            <Progress value={(currentStep / FORM_STEPS.length) * 100} className="h-2" />
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderStep()}
        </div>

        {/* Navigation */}
        <DialogFooter className="border-t p-6">
          <div className="flex items-center justify-between w-full">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Sebelumnya
            </Button>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Batal
              </Button>
              
              {currentStep < FORM_STEPS.length ? (
                <Button onClick={handleNext} className="flex items-center gap-2">
                  Selanjutnya
                  <ChevronRight className="h-4 w-4" />
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmit} 
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4" />
                      {editingSiswa ? "Perbarui Data" : "Simpan Data"}
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 