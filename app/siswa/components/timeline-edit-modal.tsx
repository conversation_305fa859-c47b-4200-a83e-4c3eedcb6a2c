"use client"

import { useState } from "react"
import { Calendar, Check<PERSON>ircle, Clock, Play, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import type { TimelineStageWithProgress, TimelineStatus } from "@/lib/types/timeline"

interface TimelineEditModalProps {
  isOpen: boolean
  onClose: () => void
  stage: TimelineStageWithProgress | null
  onSave: (updates: {
    status: string
    tanggal_mulai?: string
    tanggal_selesai?: string
    catatan?: string
  }) => Promise<void>
  loading?: boolean
}

const statusOptions = [
  { value: "belum_mulai", label: "Belum Mulai", color: "bg-gray-100 text-gray-600" },
  { value: "berlangsung", label: "Berlangsung", color: "bg-orange-100 text-orange-800" },
  { value: "selesai", label: "Selesai", color: "bg-green-100 text-green-800" },
  { value: "dibatalkan", label: "Dibatalkan", color: "bg-red-100 text-red-800" },
]

export function TimelineEditModal({
  isOpen,
  onClose,
  stage,
  onSave,
  loading = false
}: TimelineEditModalProps) {
  const [formData, setFormData] = useState<{
    status: TimelineStatus
    tanggal_mulai: string
    tanggal_selesai: string
    catatan: string
  }>({
    status: (stage?.progress?.status as TimelineStatus) || "belum_mulai",
    tanggal_mulai: stage?.progress?.tanggal_mulai || "",
    tanggal_selesai: stage?.progress?.tanggal_selesai || "",
    catatan: stage?.progress?.catatan || "",
  })

  const handleSave = async () => {
    if (!stage) return

    const updates: any = {
      status: formData.status,
      catatan: formData.catatan || undefined,
    }

    // Set dates based on status
    if (formData.status === "berlangsung" || formData.status === "selesai") {
      updates.tanggal_mulai = formData.tanggal_mulai || new Date().toISOString().split('T')[0]
    }
    
    if (formData.status === "selesai") {
      updates.tanggal_selesai = formData.tanggal_selesai || new Date().toISOString().split('T')[0]
    }

    try {
      await onSave(updates)
      onClose()
    } catch (error) {
      console.error("Error saving timeline update:", error)
    }
  }

  const getCurrentStatus = () => {
    return statusOptions.find(opt => opt.value === formData.status) || statusOptions[0]
  }

  const handleStatusChange = (newStatus: string) => {
    const updates = { ...formData, status: newStatus as TimelineStatus }
    
    // Auto-set dates based on status
    if (newStatus === "berlangsung" && !updates.tanggal_mulai) {
      updates.tanggal_mulai = new Date().toISOString().split('T')[0]
    }
    
    if (newStatus === "selesai") {
      if (!updates.tanggal_mulai) {
        updates.tanggal_mulai = new Date().toISOString().split('T')[0]
      }
      if (!updates.tanggal_selesai) {
        updates.tanggal_selesai = new Date().toISOString().split('T')[0]
      }
    }

    // Clear end date if not completed
    if (newStatus !== "selesai") {
      updates.tanggal_selesai = ""
    }

    setFormData(updates)
  }

  if (!stage) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-orange-600" />
            Update Progress: {stage.nama_stage}
          </DialogTitle>
          <DialogDescription>
            Update status dan informasi progress untuk tahapan ini
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Stage Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{stage.nama_stage}</CardTitle>
              <CardDescription>{stage.deskripsi}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Status saat ini:</span>
                <Badge className={getCurrentStatus().color}>
                  {getCurrentStatus().label}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Form */}
          <div className="space-y-4">
            {/* Status Selection */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={handleStatusChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={option.color}>
                          {option.label}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(formData.status === "berlangsung" || formData.status === "selesai") && (
                <div className="space-y-2">
                  <Label htmlFor="tanggal_mulai">Tanggal Mulai</Label>
                  <Input
                    type="date"
                    id="tanggal_mulai"
                    value={formData.tanggal_mulai}
                    onChange={(e) => setFormData(prev => ({ ...prev, tanggal_mulai: e.target.value }))}
                  />
                </div>
              )}

              {formData.status === "selesai" && (
                <div className="space-y-2">
                  <Label htmlFor="tanggal_selesai">Tanggal Selesai</Label>
                  <Input
                    type="date"
                    id="tanggal_selesai"
                    value={formData.tanggal_selesai}
                    onChange={(e) => setFormData(prev => ({ ...prev, tanggal_selesai: e.target.value }))}
                  />
                </div>
              )}
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="catatan">Catatan</Label>
              <Textarea
                id="catatan"
                placeholder="Tambahkan catatan atau keterangan untuk tahapan ini..."
                rows={3}
                value={formData.catatan}
                onChange={(e) => setFormData(prev => ({ ...prev, catatan: e.target.value }))}
              />
            </div>
          </div>

          {/* Quick Actions */}
          <Card className="bg-gray-50">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {formData.status === "belum_mulai" && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange("berlangsung")}
                    className="text-orange-600 border-orange-300 hover:bg-orange-50"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Mulai Tahapan
                  </Button>
                )}
                
                {formData.status === "berlangsung" && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange("selesai")}
                    className="text-green-600 border-green-300 hover:bg-green-50"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Selesaikan
                  </Button>
                )}

                {(formData.status === "berlangsung" || formData.status === "selesai") && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange("dibatalkan")}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Batalkan
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Batal
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Menyimpan...
              </>
            ) : (
              "Simpan"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 