"use client"

import { useState, useEffect, useRef } from "react"
import { useParams, useSearchParams } from "next/navigation"
import {
  ArrowLeft,
  Edit,
  User,
  MapPin,
  Building2,
  BadgeIcon as IdCard,
  CheckCircle,
  Clock,
  Circle,
  Calendar,
  FileText,
  MessageSquare,
  GraduationCap,
  Award,
  Plane,
  BookOpen,
  ClipboardCheck,
  FileCheck,
  Loader2,
  Phone,
  Mail,
  Home,
  Users,
  Heart,
  Target,
  Star,
  Shield,
  ShieldCheck,
  AlertCircle,
  X,
  Download,
  Upload,
  Trash2,
  Eye,
  Plus,
  Printer,
  UserCheck,
} from "lucide-react"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { toast } from "@/hooks/use-toast"
import { SiswaService, TimelineService } from "@/lib/services"
import type { Siswa } from "@/lib/types/database"
import type { TimelineStageWithProgress, TimelineStats, TimelineStatus } from "@/lib/types/timeline"
import { TimelineEditModal } from "../components/timeline-edit-modal"
import { CVGenerator } from "@/components/ui/cv-generator"

// Helper functions for social media
const SOCIAL_MEDIA_PLATFORMS = [
  { value: 'instagram', label: 'Instagram', icon: '📷' },
  { value: 'facebook', label: 'Facebook', icon: '📘' },
  { value: 'twitter', label: 'Twitter/X', icon: '🐦' },
  { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
  { value: 'tiktok', label: 'TikTok', icon: '🎵' },
  { value: 'youtube', label: 'YouTube', icon: '📺' },
  { value: 'whatsapp', label: 'WhatsApp', icon: '💬' },
  { value: 'telegram', label: 'Telegram', icon: '📱' },
  { value: 'line', label: 'Line', icon: '💚' },
  { value: 'wechat', label: 'WeChat', icon: '💬' },
  { value: 'other', label: 'Lainnya', icon: '🔗' }
]

const getPlatformInfo = (platform: string) => {
  return SOCIAL_MEDIA_PLATFORMS.find(p => p.value === platform) || 
         { value: platform, label: platform, icon: '🔗' }
}

const generateUrl = (platform: string, username: string) => {
  const cleanUsername = username.replace('@', '')
  
  switch (platform) {
    case 'instagram':
      return `https://instagram.com/${cleanUsername}`
    case 'facebook':
      return `https://facebook.com/${cleanUsername}`
    case 'twitter':
      return `https://twitter.com/${cleanUsername}`
    case 'linkedin':
      return `https://linkedin.com/in/${cleanUsername}`
    case 'tiktok':
      return `https://tiktok.com/@${cleanUsername}`
    case 'youtube':
      return `https://youtube.com/@${cleanUsername}`
    case 'whatsapp':
      return `https://wa.me/${cleanUsername.replace(/\D/g, '')}`
    case 'telegram':
      return `https://t.me/${cleanUsername}`
    case 'line':
      return `https://line.me/ti/p/${cleanUsername}`
    default:
      return username.startsWith('http') ? username : ''
  }
}

// Map Display Component
function MapDisplay({ latitude, longitude, address }: { latitude: number; longitude: number; address?: string }) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const markerRef = useRef<any>(null)
  const [mapLoading, setMapLoading] = useState(true)
  const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN

  useEffect(() => {
    if (!mapRef.current || !mapboxToken) return

    const initializeMap = async () => {
      try {
        const mapboxgl = await import('mapbox-gl')
        
        if (typeof window !== 'undefined') {
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
          document.head.appendChild(link)
        }

        mapboxgl.default.accessToken = mapboxToken

        if (mapRef.current) {
          mapInstanceRef.current = new mapboxgl.default.Map({
            container: mapRef.current,
            style: 'mapbox://styles/mapbox/streets-v12',
            center: [longitude, latitude],
            zoom: 15,
            attributionControl: false
          })
        }

        mapInstanceRef.current.on('load', () => {
          setMapLoading(false)
          
          // Add marker
          const markerElement = document.createElement('div')
          markerElement.className = 'custom-marker'
          markerElement.style.cssText = `
            width: 30px;
            height: 30px;
            background-color: #3b82f6;
            border: 3px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          `
          markerElement.innerHTML = '📍'

          markerRef.current = new mapboxgl.default.Marker(markerElement)
            .setLngLat([longitude, latitude])
            .addTo(mapInstanceRef.current)

          // Add popup
          const popup = new mapboxgl.default.Popup({ offset: 25 })
            .setLngLat([longitude, latitude])
            .setHTML(`
              <div class="p-2">
                <div class="font-medium">Lokasi Alamat</div>
                <div class="text-sm text-gray-600">${address || `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`}</div>
              </div>
            `)
            .addTo(mapInstanceRef.current)
        })

      } catch (error) {
        console.error('Failed to initialize map:', error)
        setMapLoading(false)
      }
    }

    initializeMap()

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
      }
    }
  }, [latitude, longitude, address, mapboxToken])

  if (!mapboxToken) {
    return (
      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <MapPin className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">Mapbox token tidak tersedia</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full h-full">
      <div ref={mapRef} className="w-full h-full" />
      {mapLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
            <p className="text-gray-600 text-sm">Memuat peta...</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Icon mapping for dynamic icons
const iconMap = {
  ClipboardCheck,
  BookOpen,
  GraduationCap,
  FileCheck,
  MessageSquare,
  FileText,
  Award,
  Plane,
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case "selesai":
      return {
        color: "bg-maroon-600",
        textColor: "text-maroon-600",
        bgColor: "bg-maroon-50",
        borderColor: "border-maroon-200",
        icon: CheckCircle,
        badge: "bg-maroon-100 text-maroon-800 border-maroon-200",
        label: "Selesai",
      }
    case "berlangsung":
      return {
        color: "bg-orange-500",
        textColor: "text-orange-500",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
        icon: Clock,
        badge: "bg-orange-100 text-orange-800 border-orange-200",
        label: "Berlangsung",
      }
    case "dibatalkan":
      return {
        color: "bg-red-500",
        textColor: "text-red-500",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
        icon: Circle,
        badge: "bg-red-100 text-red-800 border-red-200",
        label: "Dibatalkan",
      }
    default:
      return {
        color: "bg-gray-400",
        textColor: "text-gray-400",
        bgColor: "bg-gray-50",
        borderColor: "border-gray-200",
        icon: Circle,
        badge: "bg-gray-100 text-gray-600 border-gray-200",
        label: "Belum Mulai",
      }
  }
}

interface ProfileCompletenessItem {
  label: string
  completed: boolean
  required: boolean
}

export default function DetailSiswa() {
  const params = useParams()
  const searchParams = useSearchParams()
  const siswaId = params.id as string
  
  // Initialize activeTab from searchParams but make it stateful
  const [activeTab, setActiveTab] = useState(searchParams.get("tab") || "biodata")
  const [loading, setLoading] = useState(true)
  const [siswaData, setSiswaData] = useState<Siswa | null>(null)
  const [timelineData, setTimelineData] = useState<TimelineStageWithProgress[]>([])
  const [timelineStats, setTimelineStats] = useState<TimelineStats | null>(null)
  const [editingStage, setEditingStage] = useState<TimelineStageWithProgress | null>(null)
  const [isTimelineModalOpen, setIsTimelineModalOpen] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        
        console.log('🔍 Fetching data for siswa ID:', siswaId)
        
        // Fetch student data
        const { data: siswa, error: siswaError } = await SiswaService.getById(siswaId)
        console.log('📊 Siswa response:', { data: siswa, error: siswaError })
        
        if (siswaError) {
          console.error('❌ Siswa fetch error:', siswaError)
          throw siswaError
        }
        
        if (!siswa) {
          console.warn('⚠️ No siswa data returned')
          setSiswaData(null)
          return
        }
        
        console.log('✅ Siswa data loaded:', siswa.nama_lengkap)
        setSiswaData(siswa)

        // Fetch timeline data
        console.log('🔍 Fetching timeline data...')
        const timeline = await TimelineService.getStudentTimeline(siswaId)
        console.log('📊 Timeline response:', timeline)
        setTimelineData(timeline)

        // Fetch timeline stats
        const stats = await TimelineService.getStudentTimelineStats(siswaId)
        console.log('📊 Timeline stats:', stats)
        setTimelineStats(stats)

      } catch (error) {
        console.error("❌ Error fetching data:", error)
        toast({
          title: "Error",
          description: `Terjadi kesalahan saat memuat data siswa: ${error?.message || 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (siswaId) {
      fetchData()
    }
  }, [siswaId])

  const handleEditStage = (stage: TimelineStageWithProgress) => {
    setEditingStage(stage)
    setIsTimelineModalOpen(true)
  }

  const handleSaveTimelineProgress = async (updates: {
    status: string
    tanggal_mulai?: string
    tanggal_selesai?: string
    catatan?: string
  }) => {
    if (!editingStage) return

    try {
      await TimelineService.updateTimelineProgress(siswaId, editingStage.id, {
        ...updates,
        status: updates.status as TimelineStatus
      })
      
      // Refresh timeline data
      const timeline = await TimelineService.getStudentTimeline(siswaId)
      setTimelineData(timeline)
      
      // Refresh timeline stats
      const stats = await TimelineService.getStudentTimelineStats(siswaId)
      setTimelineStats(stats)

      toast({
        title: "Berhasil",
        description: "Progress timeline berhasil diperbarui",
      })
    } catch (error) {
      console.error("Error updating timeline:", error)
      toast({
        title: "Error",
        description: "Gagal memperbarui progress timeline",
        variant: "destructive",
      })
      throw error
    }
  }

  // Calculate profile completeness
  const calculateProfileCompleteness = (siswa: Siswa): ProfileCompletenessItem[] => {
    return [
      {
        label: "Data Pribadi",
        completed: !!(siswa.nama_lengkap && siswa.nik && siswa.tempat_lahir && siswa.tanggal_lahir),
        required: true
      },
      {
        label: "Kontak & Alamat", 
        completed: !!(siswa.alamat_lengkap && siswa.nomor_hp),
        required: true
      },
      {
        label: "Data Fisik",
        completed: !!(siswa.tinggi_badan && siswa.berat_badan),
        required: true
      },
      {
        label: "Pendidikan",
        completed: !!(siswa.pendidikan_terakhir && siswa.nama_sekolah),
        required: true
      },
      {
        label: "Data Keluarga",
        completed: !!(siswa.nama_ayah && siswa.nama_ibu),
        required: true
      },
      {
        label: "Khusus Jepang",
        completed: !!(siswa.tujuan_ke_jepang && siswa.hobi),
        required: false
      },
      {
        label: "LPK & Ketersediaan",
        completed: !!(siswa.lpk_id && siswa.ketersediaan),
        required: true
      }
    ]
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Memuat data siswa...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!siswaData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Data siswa tidak ditemukan</h1>
            <p className="text-gray-600 mt-2">Siswa dengan ID tersebut tidak exists dalam database</p>
            <Link href="/siswa">
              <Button className="mt-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Kembali ke Daftar Siswa
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const profileCompleteness = calculateProfileCompleteness(siswaData)
  const completedItems = profileCompleteness.filter(item => item.completed).length
  const totalItems = profileCompleteness.length
  const completenessPercentage = Math.round((completedItems / totalItems) * 100)
  
  const currentStage = timelineData.find(stage => stage.progress?.status === "berlangsung")
  const completedStages = timelineData.filter(stage => stage.progress?.status === "selesai").length
  const totalStages = timelineData.length
  const progressPercentage = timelineStats?.progress_percentage || 0

  const isVerified = siswaData.status_verifikasi === 'terverifikasi'

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link href="/siswa" className="text-gray-500 hover:text-gray-700">
                  Data Siswa
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Detail Siswa</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center mb-4 lg:mb-0">
              <Link href="/siswa">
                <Button
                  variant="outline"
                  size="sm"
                  className="mr-4 border-gray-300 bg-transparent"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Kembali
                </Button>
              </Link>
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
                  <User className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="flex items-center gap-3">
                    <h1 className="text-3xl font-bold text-gray-900">{siswaData.nama_lengkap}</h1>
                    {isVerified ? (
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        <ShieldCheck className="h-3 w-3 mr-1" />
                        VERIFIED
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        UNVERIFIED
                      </Badge>
                    )}
                  </div>
                  <p className="text-gray-600 mt-1">Detail profil lengkap dan tracking progress seleksi</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Profile Completeness & Quick Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Profile Completeness */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                <CardTitle className="text-lg text-gray-800 flex items-center justify-between">
                  <span className="flex items-center">
                    <ClipboardCheck className="h-5 w-5 mr-2 text-blue-600" />
                    Kelengkapan Profil
                  </span>
                  {isVerified && (
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {completenessPercentage}%
                  </div>
                  <Progress value={completenessPercentage} className="h-3" />
                  <p className="text-sm text-gray-500 mt-2">
                    {completedItems} dari {totalItems} bagian selesai
                  </p>
                </div>

                <div className="space-y-3">
                  {profileCompleteness.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700 flex items-center">
                        {item.required && <Star className="h-3 w-3 text-orange-500 mr-1" />}
                        {item.label}
                      </span>
                      {item.completed ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Circle className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-gray-800">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-3">
                <Link href={`/siswa?edit=${siswaId}`}>
                  <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Data Pribadi
                  </Button>
                </Link>
                
                <Button 
                  size="sm" 
                  variant={isVerified ? "destructive" : "default"}
                  className="w-full"
                  onClick={() => {
                    toast({
                      title: "Info", 
                      description: "Fitur verifikasi akan segera hadir"
                    })
                  }}
                >
                  <UserCheck className="h-4 w-4 mr-2" />
                  {isVerified ? "Un-Verify" : "Verify Now"}
                </Button>

                <Button 
                  size="sm" 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setActiveTab("cv")}
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Generate CV
                </Button>

                <Button size="sm" variant="outline" className="w-full">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Dokumen
                </Button>

                <Button size="sm" variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Hapus Data
                </Button>
              </CardContent>
            </Card>

            {/* Progress Summary */}
            <Card>
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                <CardTitle className="text-lg text-gray-800">Progress Timeline</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {completedStages}/{totalStages}
                  </div>
                  <p className="text-sm text-gray-500 mb-4">Tahapan selesai</p>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-maroon-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">{progressPercentage}% selesai</p>
                </div>

                {currentStage && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Clock className="h-4 w-4 text-orange-600 mr-2" />
                      <span className="text-sm font-medium text-orange-800">Tahap Saat Ini</span>
                    </div>
                    <p className="font-semibold text-gray-900">{currentStage.nama_stage}</p>
                    <p className="text-sm text-gray-600 mt-1">{currentStage.deskripsi}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content with Tabs */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader className="p-0">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-9 bg-gray-50 rounded-t-lg">
                    <TabsTrigger value="biodata" className="text-xs">
                      <User className="h-4 w-4 mr-1" />
                      Biodata
                    </TabsTrigger>
                    <TabsTrigger value="pendidikan" className="text-xs">
                      <GraduationCap className="h-4 w-4 mr-1" />
                      Pendidikan
                    </TabsTrigger>
                    <TabsTrigger value="keluarga" className="text-xs">
                      <Users className="h-4 w-4 mr-1" />
                      Keluarga
                    </TabsTrigger>
                    <TabsTrigger value="jepang" className="text-xs">
                      <Heart className="h-4 w-4 mr-1" />
                      Jepang
                    </TabsTrigger>
                    <TabsTrigger value="social" className="text-xs">
                      <MessageSquare className="h-4 w-4 mr-1" />
                      Social
                    </TabsTrigger>
                    <TabsTrigger value="lokasi" className="text-xs">
                      <MapPin className="h-4 w-4 mr-1" />
                      Lokasi
                    </TabsTrigger>
                    <TabsTrigger value="timeline" className="text-xs">
                      <Clock className="h-4 w-4 mr-1" />
                      Timeline
                    </TabsTrigger>
                    <TabsTrigger value="cv" className="text-xs">
                      <FileText className="h-4 w-4 mr-1" />
                      CV
                    </TabsTrigger>
                    <TabsTrigger value="dokumen" className="text-xs">
                      <FileText className="h-4 w-4 mr-1" />
                      Dokumen
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="biodata" className="p-6">
                    <div className="space-y-6">
                      {/* Data Pribadi */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <User className="h-5 w-5 mr-2 text-orange-600" />
                          Data Pribadi
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                              <p className="text-lg font-semibold text-gray-900">{siswaData.nama_lengkap}</p>
                            </div>
                                                         <div>
                               <label className="text-sm font-medium text-gray-500">Nama Jepang</label>
                               <p className="font-medium text-gray-900">{siswaData.nama_lengkap_jepang || '-'}</p>
                             </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500 flex items-center">
                                <IdCard className="h-4 w-4 mr-1" />
                                NIK
                              </label>
                              <p className="font-mono text-gray-900">{siswaData.nik}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Jenis Kelamin</label>
                              <p className="font-medium text-gray-900">
                                {siswaData.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'}
                              </p>
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Tempat Lahir</label>
                              <p className="font-medium text-gray-900">{siswaData.tempat_lahir}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Tanggal Lahir</label>
                              <p className="font-medium text-gray-900">
                                {new Date(siswaData.tanggal_lahir).toLocaleDateString("id-ID", {
                                  day: "2-digit",
                                  month: "long",
                                  year: "numeric",
                                })}
                              </p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Agama</label>
                              <p className="font-medium text-gray-900">{siswaData.agama || '-'}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Status Pernikahan</label>
                              <p className="font-medium text-gray-900">{siswaData.status_pernikahan || '-'}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Kontak & Alamat */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Phone className="h-5 w-5 mr-2 text-orange-600" />
                          Kontak & Alamat
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500 flex items-center">
                                <Phone className="h-4 w-4 mr-1" />
                                No. Telepon
                              </label>
                                                             <p className="font-medium text-gray-900">{siswaData.nomor_hp}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500 flex items-center">
                                <Mail className="h-4 w-4 mr-1" />
                                Email
                              </label>
                              <p className="font-medium text-gray-900">{siswaData.email || '-'}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500 flex items-center">
                                <MapPin className="h-4 w-4 mr-1" />
                                Kota/Kabupaten
                              </label>
                              <p className="font-medium text-gray-900">{siswaData.kota_kabupaten}</p>
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500 flex items-center">
                                <Home className="h-4 w-4 mr-1" />
                                Alamat Lengkap
                              </label>
                              <p className="font-medium text-gray-900">{siswaData.alamat_lengkap}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Data Fisik */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <User className="h-5 w-5 mr-2 text-orange-600" />
                          Data Fisik
                        </h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Tinggi Badan</label>
                            <p className="font-medium text-gray-900">{siswaData.tinggi_badan ? `${siswaData.tinggi_badan} cm` : '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Berat Badan</label>
                            <p className="font-medium text-gray-900">{siswaData.berat_badan ? `${siswaData.berat_badan} kg` : '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Ukuran Baju</label>
                            <p className="font-medium text-gray-900">{siswaData.ukuran_baju || '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Ukuran Sepatu</label>
                            <p className="font-medium text-gray-900">{siswaData.ukuran_sepatu || '-'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="pendidikan" className="p-6">
                    <div className="space-y-6">
                      {/* Pendidikan Terakhir */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <GraduationCap className="h-5 w-5 mr-2 text-orange-600" />
                          Riwayat Pendidikan
                        </h3>
                        
                        {/* Display data from siswa_pendidikan table */}
                        {(siswaData as any).siswa_pendidikan && (siswaData as any).siswa_pendidikan.length > 0 ? (
                          <div className="space-y-4">
                            {(siswaData as any).siswa_pendidikan.map((pendidikan: any, index: number) => (
                              <div key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Jenjang Pendidikan</label>
                                    <p className="font-medium text-gray-900">{pendidikan.jenjang}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Nama Sekolah/Universitas</label>
                                    <p className="font-medium text-gray-900">{pendidikan.nama_sekolah}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Tahun Lulus</label>
                                    <p className="font-medium text-gray-900">{pendidikan.tahun_lulus || '-'}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Nilai Rata-rata</label>
                                    <p className="font-medium text-gray-900">{pendidikan.nilai_rata_rata || '-'}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          /* Fallback to main siswa table data if no related data */
                          <div className="border border-gray-200 rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium text-gray-500">Jenjang Pendidikan</label>
                                <p className="font-medium text-gray-900">{siswaData.pendidikan_terakhir}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-500">Nama Sekolah/Universitas</label>
                                <p className="font-medium text-gray-900">{siswaData.nama_sekolah}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-500">Jurusan</label>
                                <p className="font-medium text-gray-900">{siswaData.jurusan || '-'}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-500">Tahun Lulus</label>
                                <p className="font-medium text-gray-900">{siswaData.tahun_lulus || '-'}</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Pengalaman Kerja */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Building2 className="h-5 w-5 mr-2 text-orange-600" />
                          Pengalaman Kerja
                        </h3>
                        
                        {/* Display data from siswa_pengalaman_kerja table */}
                        {(siswaData as any).siswa_pengalaman_kerja && (siswaData as any).siswa_pengalaman_kerja.length > 0 ? (
                          <div className="space-y-4">
                            {(siswaData as any).siswa_pengalaman_kerja.map((pengalaman: any, index: number) => (
                              <div key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Nama Perusahaan</label>
                                    <p className="font-medium text-gray-900">{pengalaman.nama_perusahaan}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Posisi</label>
                                    <p className="font-medium text-gray-900">{pengalaman.posisi}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Tahun Mulai</label>
                                    <p className="font-medium text-gray-900">{pengalaman.tahun_mulai || '-'}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Tahun Selesai</label>
                                    <p className="font-medium text-gray-900">{pengalaman.tahun_selesai || '-'}</p>
                                  </div>
                                  {pengalaman.deskripsi_pekerjaan && (
                                    <div className="md:col-span-2">
                                      <label className="text-sm font-medium text-gray-500">Deskripsi</label>
                                      <p className="font-medium text-gray-900">{pengalaman.deskripsi_pekerjaan}</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="bg-gray-50 rounded-lg p-4">
                            <p className="text-gray-600 text-center">
                              Belum ada data pengalaman kerja
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Kemampuan Bahasa */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <BookOpen className="h-5 w-5 mr-2 text-orange-600" />
                          Kemampuan Bahasa
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Bahasa Jepang</label>
                            <p className="font-medium text-gray-900">{siswaData.level_bahasa_jepang || '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Bahasa Inggris</label>
                            <p className="font-medium text-gray-900">-</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="keluarga" className="p-6">
                    <div className="space-y-6">
                      {/* Data Keluarga dari Tabel Utama */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Users className="h-5 w-5 mr-2 text-orange-600" />
                          Data Keluarga Inti
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Nama Ayah</label>
                              <p className="font-medium text-gray-900">{siswaData.nama_ayah}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Pekerjaan Ayah</label>
                              <p className="font-medium text-gray-900">{siswaData.pekerjaan_ayah || '-'}</p>
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium text-gray-500">Nama Ibu</label>
                              <p className="font-medium text-gray-900">{siswaData.nama_ibu}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-500">Pekerjaan Ibu</label>
                              <p className="font-medium text-gray-900">{siswaData.pekerjaan_ibu || '-'}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Data Keluarga Extended dari Related Table */}
                      {(siswaData as any).siswa_keluarga && (siswaData as any).siswa_keluarga.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <Users className="h-5 w-5 mr-2 text-orange-600" />
                            Data Keluarga Extended
                          </h3>
                          <div className="space-y-4">
                            {(siswaData as any).siswa_keluarga.map((keluarga: any, index: number) => (
                              <div key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Nama</label>
                                    <p className="font-medium text-gray-900">{keluarga.nama}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Hubungan</label>
                                    <p className="font-medium text-gray-900">{keluarga.hubungan}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Umur</label>
                                    <p className="font-medium text-gray-900">{keluarga.umur ? `${keluarga.umur} tahun` : '-'}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Pekerjaan</label>
                                    <p className="font-medium text-gray-900">{keluarga.pekerjaan || '-'}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">No. Telepon</label>
                                    <p className="font-medium text-gray-900">{keluarga.nomor_hp || '-'}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">Tipe</label>
                                    <p className="font-medium text-gray-900">
                                      <Badge variant={keluarga.tipe === 'indonesia' ? 'default' : 'secondary'}>
                                        {keluarga.tipe || 'Indonesia'}
                                      </Badge>
                                    </p>
                                  </div>
                                  {keluarga.alamat && (
                                    <div className="md:col-span-3">
                                      <label className="text-sm font-medium text-gray-500">Alamat</label>
                                      <p className="font-medium text-gray-900">{keluarga.alamat}</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Kontak Darurat */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Phone className="h-5 w-5 mr-2 text-orange-600" />
                          Kontak Darurat
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Nama Kontak Darurat</label>
                            <p className="font-medium text-gray-900">-</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Nomor Telepon Darurat</label>
                            <p className="font-medium text-gray-900">-</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="jepang" className="p-6">
                    <div className="space-y-6">
                      {/* Motivasi & Tujuan */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Target className="h-5 w-5 mr-2 text-orange-600" />
                          Motivasi & Tujuan
                        </h3>
                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Motivasi ke Jepang</label>
                            <p className="font-medium text-gray-900">{siswaData.motivasi_ke_jepang || '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Tujuan Ke Jepang</label>
                            <p className="font-medium text-gray-900">{siswaData.tujuan_ke_jepang || '-'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Hobi & Minat */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Heart className="h-5 w-5 mr-2 text-orange-600" />
                          Hobi & Minat
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Hobi</label>
                            <p className="font-medium text-gray-900">{siswaData.hobi || '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Keahlian Khusus</label>
                            <p className="font-medium text-gray-900">{siswaData.keahlian_khusus || '-'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Preferensi Kerja */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferensi Kerja di Jepang</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500">Bidang Kerja Yang Diminati</label>
                            <p className="font-medium text-gray-900">{siswaData.bidang_kerja_diminati || '-'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Wilayah Penempatan</label>
                            <p className="font-medium text-gray-900">{siswaData.preferensi_wilayah_jepang || '-'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="timeline" className="p-6">
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">Timeline Progress Seleksi</h3>
                        <p className="text-sm text-gray-600">Tracking lengkap tahapan seleksi magang ke Jepang</p>
                      </div>
                      
                      <div className="relative">
                        {timelineData.map((stage, index) => {
                          const status = stage.progress?.status || 'belum_mulai'
                          const statusConfig = getStatusConfig(status)
                          const StageIcon = iconMap[stage.icon as keyof typeof iconMap] || ClipboardCheck
                          const StatusIcon = statusConfig.icon
                          const isLast = index === timelineData.length - 1

                          return (
                            <div key={stage.id} className="relative flex items-start pb-8">
                              {/* Timeline Line */}
                              {!isLast && (
                                <div
                                  className={cn(
                                    "absolute left-6 top-12 w-0.5 h-full",
                                    status === "selesai" ? "bg-maroon-300" : "bg-gray-300",
                                  )}
                                />
                              )}

                              {/* Timeline Node */}
                              <div className="relative flex-shrink-0">
                                <div
                                  className={cn(
                                    "w-12 h-12 rounded-full flex items-center justify-center border-4 border-white shadow-lg",
                                    statusConfig.color,
                                  )}
                                >
                                  <StageIcon className="h-5 w-5 text-white" />
                                </div>
                                {/* Status Indicator */}
                                <div className="absolute -bottom-1 -right-1">
                                  <div
                                    className={cn(
                                      "w-6 h-6 rounded-full flex items-center justify-center border-2 border-white",
                                      statusConfig.color,
                                    )}
                                  >
                                    <StatusIcon className="h-3 w-3 text-white" />
                                  </div>
                                </div>
                              </div>

                              {/* Timeline Content */}
                              <div className="ml-6 flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="text-lg font-semibold text-gray-900">{stage.nama_stage}</h3>
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className={statusConfig.badge}>
                                      {statusConfig.label}
                                    </Badge>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleEditStage(stage)}
                                      className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                                <p className="text-gray-600 mb-3">{stage.deskripsi}</p>

                                {/* Date and Notes */}
                                <div className={cn("rounded-lg p-4 border", statusConfig.bgColor, statusConfig.borderColor)}>
                                  {stage.progress?.tanggal_mulai && (
                                    <div className="flex items-center mb-2">
                                      <Calendar className={cn("h-4 w-4 mr-2", statusConfig.textColor)} />
                                      <span className="text-sm font-medium text-gray-700">
                                        Mulai: {new Date(stage.progress.tanggal_mulai).toLocaleDateString("id-ID", {
                                          day: "2-digit",
                                          month: "long",
                                          year: "numeric",
                                        })}
                                      </span>
                                    </div>
                                  )}
                                  {stage.progress?.tanggal_selesai && (
                                    <div className="flex items-center mb-2">
                                      <Calendar className={cn("h-4 w-4 mr-2", statusConfig.textColor)} />
                                      <span className="text-sm font-medium text-gray-700">
                                        Selesai: {new Date(stage.progress.tanggal_selesai).toLocaleDateString("id-ID", {
                                          day: "2-digit",
                                          month: "long",
                                          year: "numeric",
                                        })}
                                      </span>
                                    </div>
                                  )}
                                  {stage.progress?.catatan ? (
                                    <div className="flex items-start">
                                      <FileText className={cn("h-4 w-4 mr-2 mt-0.5 flex-shrink-0", statusConfig.textColor)} />
                                      <p className="text-sm text-gray-700">{stage.progress.catatan}</p>
                                    </div>
                                  ) : (
                                    <div className="flex items-center">
                                      <Clock className={cn("h-4 w-4 mr-2", statusConfig.textColor)} />
                                      <span className="text-sm text-gray-500 italic">
                                        {status === "berlangsung" ? "Sedang dalam proses" : "Menunggu tahap sebelumnya"}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="social" className="p-6">
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <MessageSquare className="h-5 w-5 mr-2 text-orange-600" />
                        Media Sosial
                      </h3>
                      
                      {(siswaData as any).siswa_social_media && (siswaData as any).siswa_social_media.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {(siswaData as any).siswa_social_media.map((social: any, index: number) => {
                            const platformInfo = getPlatformInfo(social.platform)
                            const displayUrl = social.url || generateUrl(social.platform, social.username)
                            
                            return (
                              <div 
                                key={index} 
                                className={`p-4 border rounded-lg ${
                                  !social.is_active ? 'bg-gray-50 opacity-60' : 'bg-white'
                                }`}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="text-2xl">{platformInfo.icon}</div>
                                  <div className="flex-1">
                                    <div className="font-medium">{platformInfo.label}</div>
                                    <div className="text-sm text-gray-600">@{social.username}</div>
                                    {displayUrl && (
                                      <a 
                                        href={displayUrl} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:underline"
                                      >
                                        {displayUrl}
                                      </a>
                                    )}
                                  </div>
                                  {!social.is_active && (
                                    <Badge variant="secondary">Tidak Aktif</Badge>
                                  )}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
                          <p>Belum ada akun media sosial yang ditambahkan</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="lokasi" className="p-6">
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <MapPin className="h-5 w-5 mr-2 text-orange-600" />
                        Lokasi Alamat
                      </h3>
                      
                      {siswaData.latitude && siswaData.longitude ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium">Latitude:</span> {siswaData.latitude.toFixed(6)}
                            </div>
                            <div>
                              <span className="font-medium">Longitude:</span> {siswaData.longitude.toFixed(6)}
                            </div>
                          </div>
                          {siswaData.alamat_koordinat && (
                            <div className="text-sm">
                              <span className="font-medium">Alamat:</span> {siswaData.alamat_koordinat}
                            </div>
                          )}
                          
                          {/* Map Display */}
                          <div className="w-full h-64 border rounded-md overflow-hidden">
                            <MapDisplay 
                              latitude={siswaData.latitude}
                              longitude={siswaData.longitude}
                              address={siswaData.alamat_koordinat}
                            />
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <MapPin className="h-12 w-12 mx-auto mb-2 opacity-50" />
                          <p>Lokasi alamat belum ditentukan</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="cv" className="p-6">
                    <CVGenerator siswa={siswaData} />
                  </TabsContent>

                  <TabsContent value="dokumen" className="p-6">
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                          <FileText className="h-5 w-5 mr-2 text-orange-600" />
                          Dokumen & Attachment
                        </h3>
                        <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                          <Plus className="h-4 w-4 mr-2" />
                          Upload Dokumen
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Placeholder untuk dokumen */}
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Dokumen akan ditampilkan di sini</p>
                        </div>
                      </div>

                      {/* Status LPK */}
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 mb-4">Informasi LPK & Status</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm font-medium text-gray-500 flex items-center">
                              <Building2 className="h-4 w-4 mr-1" />
                              LPK Mitra
                            </label>
                            <p className="font-medium text-gray-900">
                              {(siswaData as any).lpk_mitra?.nama_lpk || 'N/A'}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500">Status Pendaftaran</label>
                            <p className="font-medium text-gray-900 capitalize">{siswaData.status_pendaftaran}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardHeader>
            </Card>
          </div>
        </div>

        {/* Timeline Edit Modal */}
        <TimelineEditModal
          isOpen={isTimelineModalOpen}
          onClose={() => {
            setIsTimelineModalOpen(false)
            setEditingStage(null)
          }}
          stage={editingStage}
          onSave={handleSaveTimelineProgress}
        />
      </div>
    </div>
  )
}
