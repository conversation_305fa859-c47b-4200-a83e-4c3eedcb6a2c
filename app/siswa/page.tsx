"use client"

import { useState, use<PERSON>em<PERSON>, useEffect, use<PERSON>allback } from "react"
import {
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  Filter,
  Users,
  AlertTriangle,
  Loader2,
  UserCheck,
  UserX,
  Clock,
  FileSpreadsheet,
  Grid3X3,
  List,
  CheckSquare,
  Square,
  MoreVertical,
  Download,
  Upload,
  RefreshCw,
  Star,
  MapPin,
  Calendar,
  Phone,
  Mail,
  GraduationCap,
  Building2,
  CheckCircle,
  AlertCircle,
  XCircle,
  Percent,
} from "lucide-react"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DataTablePagination } from "@/components/ui/data-table-pagination"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { usePagination } from "@/hooks/use-pagination"
import { SiswaService, LpkMitraService } from "@/lib/services"
import { 
  SiswaWithRelations, 
  SiswaFilterParams,
  SiswaStats,
  VerificationStatus,
  AvailabilityStatus,
  RegistrationStatus,
  GenderType 
} from "@/lib/types/database"
import { SiswaFormModal } from "./components/siswa-form-modal"
import { ExcelImportModal } from "./components/excel-import-modal"

type ViewMode = 'table' | 'card'

export default function SiswaPage() {
  // State management
  const [siswaData, setSiswaData] = useState<SiswaWithRelations[]>([])
  const [stats, setStats] = useState<SiswaStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [selectedSiswa, setSelectedSiswa] = useState<Set<string>>(new Set())
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isExcelImportOpen, setIsExcelImportOpen] = useState(false)
  const [editingSiswa, setEditingSiswa] = useState<SiswaWithRelations | null>(null)
  const [deletingSiswa, setDeletingSiswa] = useState<SiswaWithRelations | null>(null)
  const [lpkOptions, setLpkOptions] = useState<Array<{ id: string; nama_lpk: string }>>([])

  // Filter state
  const [filters, setFilters] = useState<SiswaFilterParams>({
    search: '',
    page: 1,
    limit: 12,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  // Advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  // Computed filter state for display
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (filters.search) count++
    if (filters.lpk_id) count++
    if (filters.jenis_kelamin) count++
    if (filters.status_pendaftaran) count++
    if (filters.status_verifikasi) count++
    if (filters.ketersediaan) count++
    if (filters.pendidikan_min) count++
    if (filters.usia_min || filters.usia_max) count++
    if (filters.prefektur) count++
    if (filters.level_bahasa) count++
    return count
  }, [filters])

  // Pagination
  const pagination = usePagination({
    data: siswaData,
    itemsPerPage: filters.limit || 12,
  })

  // Data fetching
  const fetchData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      const [siswaResponse, statsResponse] = await Promise.all([
        SiswaService.getAll(filters),
        SiswaService.getComprehensiveStats()
      ])

      setSiswaData((siswaResponse as any)?.data || siswaResponse || [])
      setStats(statsResponse)
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat memuat data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [filters])

  const fetchLpkOptions = useCallback(async () => {
    try {
      const response = await LpkMitraService.getAll()
      setLpkOptions(((response as any)?.data || response || []).map((lpk: any) => ({
        id: lpk.id,
        nama_lpk: lpk.nama_lpk
      })))
    } catch (error) {
      console.error("Error fetching LPK options:", error)
    }
  }, [])

  useEffect(() => {
    fetchData(true)
  }, [fetchData])

  useEffect(() => {
    fetchLpkOptions()
  }, [fetchLpkOptions])

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSiswa(new Set(siswaData.map(s => s.id)))
    } else {
      setSelectedSiswa(new Set())
    }
  }

  const handleSelectSiswa = (siswaId: string, checked: boolean) => {
    const newSelected = new Set(selectedSiswa)
    if (checked) {
      newSelected.add(siswaId)
    } else {
      newSelected.delete(siswaId)
    }
    setSelectedSiswa(newSelected)
  }

  // Filter handlers
  const updateFilter = (key: keyof SiswaFilterParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }))
  }

  const resetFilters = () => {
    setFilters({
      search: '',
      page: 1,
      limit: 12,
      sort_by: 'created_at',
      sort_order: 'desc'
    })
    setSelectedSiswa(new Set())
  }

  // Bulk operations
  const handleBulkOperation = async (operation: string) => {
    if (selectedSiswa.size === 0) {
      toast({
        title: "Peringatan",
        description: "Pilih siswa terlebih dahulu",
        variant: "destructive",
      })
      return
    }

    const selectedIds = Array.from(selectedSiswa)
    
    try {
      switch (operation) {
        case 'approve':
          await SiswaService.bulkUpdateStatus(selectedIds, 'approved')
          toast({
            title: "Berhasil",
            description: `${selectedIds.length} siswa berhasil disetujui`,
          })
          break
        case 'verify':
          await SiswaService.bulkUpdateVerification(selectedIds, 'terverifikasi', 'system')
          toast({
            title: "Berhasil",
            description: `${selectedIds.length} siswa berhasil diverifikasi`,
          })
          break
        case 'reject':
          await SiswaService.bulkUpdateStatus(selectedIds, 'rejected')
          toast({
            title: "Berhasil",
            description: `${selectedIds.length} siswa berhasil ditolak`,
          })
          break
        default:
          break
      }
      
      setSelectedSiswa(new Set())
      fetchData()
    } catch (error) {
      console.error("Error performing bulk operation:", error)
      toast({
        title: "Error",
        description: "Gagal melakukan operasi bulk",
        variant: "destructive",
      })
    }
  }

  // Modal handlers
  const handleOpenModal = (siswa?: SiswaWithRelations) => {
    setEditingSiswa(siswa || null)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingSiswa(null)
  }

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingSiswa) {
        await SiswaService.update(editingSiswa.id, data)
        toast({
          title: "Berhasil",
          description: "Data siswa berhasil diperbarui",
        })
      } else {
        await SiswaService.create(data)
        toast({
          title: "Berhasil",
          description: "Data siswa berhasil ditambahkan",
        })
      }
      
      handleCloseModal()
      fetchData()
    } catch (error) {
      console.error("Error submitting siswa:", error)
      toast({
        title: "Error",
        description: "Gagal menyimpan data siswa",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async () => {
    if (!deletingSiswa) return

    try {
      await SiswaService.delete(deletingSiswa.id)
      toast({
        title: "Berhasil",
        description: "Data siswa berhasil dihapus",
      })
      setIsDeleteDialogOpen(false)
      setDeletingSiswa(null)
      fetchData()
    } catch (error) {
      console.error("Error deleting siswa:", error)
      toast({
        title: "Error",
        description: "Gagal menghapus data siswa",
        variant: "destructive",
      })
    }
  }

  // Badge components
  const getStatusBadge = (status: RegistrationStatus) => {
    const variants = {
      approved: { variant: "default" as const, className: "bg-green-500 hover:bg-green-600", icon: UserCheck },
      rejected: { variant: "destructive" as const, className: "", icon: UserX },
      review: { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200", icon: Clock },
      submitted: { variant: "outline" as const, className: "border-blue-200 text-blue-700", icon: Clock },
      draft: { variant: "outline" as const, className: "border-gray-300 text-gray-600", icon: null }
    }

    const config = variants[status] || variants.draft
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className={config.className || ""}>
        {Icon && <Icon className="h-3 w-3 mr-1" />}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getVerificationBadge = (status: VerificationStatus) => {
    const variants = {
      terverifikasi: { icon: CheckCircle, className: "text-green-600", label: "Terverifikasi" },
      sedang_diverifikasi: { icon: Clock, className: "text-yellow-600", label: "Diverifikasi" },
      ditolak: { icon: XCircle, className: "text-red-600", label: "Ditolak" },
      belum_diverifikasi: { icon: AlertCircle, className: "text-gray-600", label: "Belum" }
    }

    const config = variants[status] || variants.belum_diverifikasi
    const Icon = config.icon

    return (
      <div className={`flex items-center gap-1 text-xs ${config.className}`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </div>
    )
  }

  const getAvailabilityBadge = (status: AvailabilityStatus) => {
    const variants = {
      siap: { className: "bg-green-100 text-green-800", label: "Siap" },
      belum_siap: { className: "bg-red-100 text-red-800", label: "Belum Siap" },
      kondisional: { className: "bg-yellow-100 text-yellow-800", label: "Kondisional" }
    }

    const config = variants[status] || variants.belum_siap

    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    )
  }

  // Card component for card view
  const SiswaCard = ({ siswa }: { siswa: SiswaWithRelations }) => (
    <Card className="transition-all duration-200 hover:shadow-lg border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedSiswa.has(siswa.id)}
              onCheckedChange={(checked) => handleSelectSiswa(siswa.id, checked as boolean)}
            />
            <Avatar className="h-12 w-12">
              <AvatarImage src={`/placeholder-user.jpg`} />
              <AvatarFallback className="bg-blue-100 text-blue-600">
                {siswa.nama_lengkap.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg leading-none mb-1">
                {siswa.nama_lengkap}
                {siswa.nama_lengkap_jepang && (
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    ({siswa.nama_lengkap_jepang})
                  </span>
                )}
              </CardTitle>
              <CardDescription className="text-sm">
                NIK: {siswa.nik}
              </CardDescription>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/siswa/${siswa.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  Lihat Detail
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleOpenModal(siswa)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => {
                  setDeletingSiswa(siswa)
                  setIsDeleteDialogOpen(true)
                }}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Hapus
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Profile Completeness */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Kelengkapan Profil</span>
            <span className="font-medium">{siswa.profile_completeness}%</span>
          </div>
          <Progress value={siswa.profile_completeness} className="h-2" />
        </div>

        {/* Status Badges */}
        <div className="flex flex-wrap gap-2">
          {getStatusBadge(siswa.status_pendaftaran)}
          {getAvailabilityBadge(siswa.ketersediaan)}
          <Badge variant="outline" className="text-xs">
            <Star className="h-3 w-3 mr-1" />
            {siswa.profile_completeness}%
          </Badge>
        </div>

        {/* Key Information */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-gray-600">
              <Users className="h-3 w-3" />
              <span>{siswa.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'}</span>
            </div>
            <div className="flex items-center gap-1 text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{new Date().getFullYear() - new Date(siswa.tanggal_lahir).getFullYear()} tahun</span>
            </div>
            <div className="flex items-center gap-1 text-gray-600">
              <Phone className="h-3 w-3" />
              <span>{siswa.nomor_hp}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-gray-600">
              <GraduationCap className="h-3 w-3" />
              <span>{siswa.pendidikan_terakhir}</span>
            </div>
            <div className="flex items-center gap-1 text-gray-600">
              <Building2 className="h-3 w-3" />
              <span className="truncate">{siswa.lpk_mitra?.nama_lpk || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-1 text-gray-600">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{siswa.kota_kabupaten}</span>
            </div>
          </div>
        </div>

        {/* Verification Status */}
        <div className="pt-2 border-t">
          {getVerificationBadge(siswa.status_verifikasi)}
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat data siswa...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Manajemen Siswa</h1>
            <p className="text-muted-foreground">
              Kelola data siswa magang Jepang dengan fitur lengkap
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => fetchData()}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setIsExcelImportOpen(true)} 
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Import
            </Button>
            <Button onClick={() => handleOpenModal()} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Tambah Siswa
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Siswa</p>
                    <p className="text-2xl font-bold">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Terverifikasi</p>
                    <p className="text-2xl font-bold">{stats.by_status_verifikasi.terverifikasi || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Siap Bekerja</p>
                    <p className="text-2xl font-bold">{stats.by_ketersediaan.siap || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Percent className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Rata-rata Kelengkapan</p>
                    <p className="text-2xl font-bold">{stats.avg_profile_completeness}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter & Pencarian
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFiltersCount} filter aktif
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                Filter Lanjutan
              </Button>
              {activeFiltersCount > 0 && (
                <Button variant="outline" size="sm" onClick={resetFilters}>
                  Reset
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Cari Siswa</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Nama, NIK, Email, HP..."
                  value={filters.search || ''}
                  onChange={(e) => updateFilter('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status Pendaftaran</label>
              <Select 
                value={filters.status_pendaftaran || 'all'} 
                onValueChange={(value) => updateFilter('status_pendaftaran', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="review">Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status Verifikasi</label>
              <Select 
                value={filters.status_verifikasi || 'all'} 
                onValueChange={(value) => updateFilter('status_verifikasi', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Semua Verifikasi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Verifikasi</SelectItem>
                  <SelectItem value="belum_diverifikasi">Belum Diverifikasi</SelectItem>
                  <SelectItem value="sedang_diverifikasi">Sedang Diverifikasi</SelectItem>
                  <SelectItem value="terverifikasi">Terverifikasi</SelectItem>
                  <SelectItem value="ditolak">Ditolak</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Ketersediaan</label>
              <Select 
                value={filters.ketersediaan || 'all'} 
                onValueChange={(value) => updateFilter('ketersediaan', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Semua Ketersediaan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Ketersediaan</SelectItem>
                  <SelectItem value="siap">Siap</SelectItem>
                  <SelectItem value="belum_siap">Belum Siap</SelectItem>
                  <SelectItem value="kondisional">Kondisional</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <label className="text-sm font-medium">LPK Mitra</label>
                <Select 
                  value={filters.lpk_id || 'all'} 
                  onValueChange={(value) => updateFilter('lpk_id', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Semua LPK" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua LPK</SelectItem>
                    {lpkOptions.map((lpk) => (
                      <SelectItem key={lpk.id} value={lpk.id}>
                        {lpk.nama_lpk}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Jenis Kelamin</label>
                <Select 
                  value={filters.jenis_kelamin || 'all'} 
                  onValueChange={(value) => updateFilter('jenis_kelamin', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Gender</SelectItem>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Usia Minimum</label>
                <Input
                  type="number"
                  placeholder="Min usia"
                  value={filters.usia_min || ''}
                  onChange={(e) => updateFilter('usia_min', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Usia Maksimum</label>
                <Input
                  type="number"
                  placeholder="Max usia"
                  value={filters.usia_max || ''}
                  onChange={(e) => updateFilter('usia_max', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Menampilkan {siswaData.length} dari {stats?.total || 0} siswa
              {activeFiltersCount > 0 && " (difilter)"}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* View Controls and Bulk Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'card' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('card')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
          </div>

          {/* Selection Summary */}
          {selectedSiswa.size > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {selectedSiswa.size} siswa dipilih
              </span>
              <div className="flex gap-1">
                <Button size="sm" variant="outline" onClick={() => handleBulkOperation('approve')}>
                  <UserCheck className="h-3 w-3 mr-1" />
                  Setujui
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkOperation('verify')}>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Verifikasi
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkOperation('reject')}>
                  <UserX className="h-3 w-3 mr-1" />
                  Tolak
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Data Display */}
      {viewMode === 'table' ? (
        /* Table View */
        <Card>
          <CardContent className="p-0">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedSiswa.size === siswaData.length && siswaData.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Siswa & Profil</TableHead>
                    <TableHead>Kontak & Lokasi</TableHead>
                    <TableHead>Pendidikan & LPK</TableHead>
                    <TableHead>Status & Verifikasi</TableHead>
                    <TableHead>Kelengkapan</TableHead>
                    <TableHead className="w-20">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {siswaData.length > 0 ? (
                    siswaData.map((siswa) => (
                      <TableRow key={siswa.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedSiswa.has(siswa.id)}
                            onCheckedChange={(checked) => handleSelectSiswa(siswa.id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                                {siswa.nama_lengkap.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{siswa.nama_lengkap}</div>
                              {siswa.nama_lengkap_jepang && (
                                <div className="text-xs text-gray-500">{siswa.nama_lengkap_jepang}</div>
                              )}
                              <div className="text-xs text-gray-500">
                                {siswa.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'} • 
                                {new Date().getFullYear() - new Date(siswa.tanggal_lahir).getFullYear()} tahun
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm space-y-1">
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3 text-gray-400" />
                              {siswa.nomor_hp}
                            </div>
                            {siswa.email && (
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3 text-gray-400" />
                                {siswa.email}
                              </div>
                            )}
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              {siswa.kota_kabupaten}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm space-y-1">
                            <div className="flex items-center gap-1">
                              <GraduationCap className="h-3 w-3 text-gray-400" />
                              {siswa.pendidikan_terakhir}
                            </div>
                            <div className="flex items-center gap-1">
                              <Building2 className="h-3 w-3 text-gray-400" />
                              {siswa.lpk_mitra?.nama_lpk || 'N/A'}
                            </div>
                            {siswa.level_bahasa_jepang && (
                              <div className="text-xs text-gray-500">
                                Bahasa: {siswa.level_bahasa_jepang}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            {getStatusBadge(siswa.status_pendaftaran)}
                            {getVerificationBadge(siswa.status_verifikasi)}
                            {getAvailabilityBadge(siswa.ketersediaan)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-xs">
                              <span>{siswa.profile_completeness}%</span>
                            </div>
                            <Progress value={siswa.profile_completeness} className="h-2" />
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/siswa/${siswa.id}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Detail
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleOpenModal(siswa)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => {
                                  setDeletingSiswa(siswa)
                                  setIsDeleteDialogOpen(true)
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Hapus
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-32">
                        <div className="flex flex-col items-center justify-center text-center">
                          <Users className="h-12 w-12 text-gray-400" />
                          <h3 className="text-lg font-medium text-gray-900">Tidak ada data siswa</h3>
                          <p className="text-gray-500">
                            {activeFiltersCount > 0
                              ? "Tidak ditemukan siswa yang sesuai dengan filter"
                              : "Belum ada siswa yang terdaftar"}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Card View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {siswaData.length > 0 ? (
            siswaData.map((siswa) => (
              <SiswaCard key={siswa.id} siswa={siswa} />
            ))
          ) : (
            <div className="col-span-full">
              <Card className="p-12">
                <div className="flex flex-col items-center justify-center text-center">
                  <Users className="h-16 w-16 text-gray-400" />
                  <h3 className="text-xl font-medium text-gray-900 mt-4">Tidak ada data siswa</h3>
                  <p className="text-gray-500 mt-2">
                    {activeFiltersCount > 0
                      ? "Tidak ditemukan siswa yang sesuai dengan filter"
                      : "Belum ada siswa yang terdaftar"}
                  </p>
                </div>
              </Card>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {siswaData.length > 0 && (
        <DataTablePagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          totalItems={pagination.totalItems}
          itemsPerPage={pagination.itemsPerPage}
          startIndex={pagination.startIndex}
          endIndex={pagination.endIndex}
          onPageChange={pagination.goToPage}
          onItemsPerPageChange={pagination.setItemsPerPage}
          canGoNext={pagination.canGoNext}
          canGoPrevious={pagination.canGoPrevious}
        />
      )}

             {/* Modals */}
       <SiswaFormModal
         isOpen={isModalOpen}
         onClose={handleCloseModal}
         onSubmit={handleFormSubmit}
         editingSiswa={editingSiswa}
         lpkOptions={lpkOptions}
         formData={{} as any}
         setFormData={() => {}}
         formErrors={{}}
         isSubmitting={false}
       />

      <ExcelImportModal 
        open={isExcelImportOpen}
        onOpenChange={setIsExcelImportOpen}
        onImportComplete={() => {
          fetchData()
          toast({
            title: "Import Berhasil",
            description: "Data siswa berhasil diimport dari Excel",
          })
        }}
      />

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Hapus
            </AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus data siswa{" "}
              <span className="font-semibold">{deletingSiswa?.nama_lengkap}</span>?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 