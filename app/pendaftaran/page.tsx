"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  CalendarIcon,
  Check,
  ChevronsUpDown,
  User,
  MapPin,
  Building2,
  Calendar,
  BadgeIcon as Id<PERSON><PERSON>,
  <PERSON>,
} from "lucide-react"
import { format } from "date-fns"
import { id } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { cn } from "@/lib/utils"

// Form validation schema
const formSchema = z.object({
  namaLengkap: z
    .string()
    .min(2, { message: "Nama lengkap minimal 2 karakter" })
    .max(100, { message: "Nama lengkap maksimal 100 karakter" }),
  gender: z.string({ required_error: "Silakan pilih jenis kelamin" }),
  nik: z
    .string()
    .min(16, { message: "NIK harus 16 digit" })
    .max(16, { message: "NIK harus 16 digit" })
    .regex(/^\d+$/, { message: "NIK hanya boleh berisi angka" }),
  alamatLengkap: z
    .string()
    .min(10, { message: "Alamat lengkap minimal 10 karakter" })
    .max(500, { message: "Alamat lengkap maksimal 500 karakter" }),
  kotaKabupaten: z
    .string()
    .min(2, { message: "Kota/Kabupaten minimal 2 karakter" })
    .max(100, { message: "Kota/Kabupaten maksimal 100 karakter" }),
  tanggalLahir: z.date({ required_error: "Silakan pilih tanggal lahir" }),
  lpkMitra: z.string({ required_error: "Silakan pilih LPK Mitra" }),
})

// Sample LPK Mitra data
const lpkMitraList = [
  { value: "lpk-jakarta-1", label: "LPK Sukses Mandiri Jakarta" },
  { value: "lpk-bandung-1", label: "LPK Maju Bersama Bandung" },
  { value: "lpk-surabaya-1", label: "LPK Harapan Bangsa Surabaya" },
  { value: "lpk-medan-1", label: "LPK Karya Utama Medan" },
  { value: "lpk-makassar-1", label: "LPK Nusantara Makassar" },
  { value: "lpk-yogya-1", label: "LPK Bina Prestasi Yogyakarta" },
  { value: "lpk-semarang-1", label: "LPK Mitra Sejahtera Semarang" },
  { value: "lpk-palembang-1", label: "LPK Cahaya Masa Depan Palembang" },
]

type FormData = z.infer<typeof formSchema>

export default function PendaftaranSiswa() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [lpkOpen, setLpkOpen] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      namaLengkap: "",
      alamatLengkap: "",
      kotaKabupaten: "",
    },
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    console.log("Form data:", data)

    // Show success message or redirect
    alert("Pendaftaran siswa berhasil!")

    setIsSubmitting(false)
    form.reset()
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg mr-4">
              <User className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Formulir Pendaftaran Siswa</h1>
              <p className="text-gray-600 mt-1">Lengkapi data siswa untuk proses pendaftaran magang ke Jepang</p>
            </div>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span className="flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Langkah 1 dari 3: Data Pribadi
            </span>
          </div>
        </div>

        {/* Form Card */}
        <Card className="shadow-lg border-0">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-maroon-50 border-b">
            <CardTitle className="text-xl text-gray-800 flex items-center">
              <IdCard className="h-5 w-5 mr-2 text-orange-600" />
              Informasi Data Pribadi
            </CardTitle>
            <CardDescription>Pastikan semua data yang dimasukkan sesuai dengan dokumen resmi</CardDescription>
          </CardHeader>

          <CardContent className="p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* Row 1: Nama Lengkap & Gender */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="namaLengkap"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <User className="h-4 w-4 mr-2 text-orange-600" />
                          Nama Lengkap *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Masukkan nama lengkap sesuai KTP"
                            className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-gray-500">
                          Nama harus sesuai dengan yang tertera di KTP
                        </FormDescription>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <Users className="h-4 w-4 mr-2 text-orange-600" />
                          Jenis Kelamin *
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500">
                              <SelectValue placeholder="Pilih jenis kelamin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="laki-laki">Laki-laki</SelectItem>
                            <SelectItem value="perempuan">Perempuan</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 2: NIK & Tanggal Lahir */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="nik"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <IdCard className="h-4 w-4 mr-2 text-orange-600" />
                          NIK (Nomor Induk Kependudukan) *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Masukkan 16 digit NIK"
                            className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                            maxLength={16}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-gray-500">16 digit angka sesuai KTP</FormDescription>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tanggalLahir"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-orange-600" />
                          Tanggal Lahir *
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "h-11 pl-3 text-left font-normal border-gray-300 focus:border-orange-500 focus:ring-orange-500",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "dd MMMM yyyy", { locale: id })
                                ) : (
                                  <span>Pilih tanggal lahir</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormDescription className="text-xs text-gray-500">Tanggal lahir sesuai KTP</FormDescription>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Row 3: Alamat Lengkap */}
                <FormField
                  control={form.control}
                  name="alamatLengkap"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-orange-600" />
                        Alamat Lengkap *
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Masukkan alamat lengkap sesuai KTP"
                          className="min-h-[100px] border-gray-300 focus:border-orange-500 focus:ring-orange-500 resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-xs text-gray-500">
                        Alamat harus sesuai dengan yang tertera di KTP
                      </FormDescription>
                      <FormMessage className="text-red-600" />
                    </FormItem>
                  )}
                />

                {/* Row 4: Kota/Kabupaten & LPK Mitra */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="kotaKabupaten"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <Building2 className="h-4 w-4 mr-2 text-orange-600" />
                          Kota/Kabupaten *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Masukkan kota/kabupaten"
                            className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription className="text-xs text-gray-500">
                          Kota/kabupaten tempat tinggal
                        </FormDescription>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lpkMitra"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-gray-700 font-medium flex items-center">
                          <Building2 className="h-4 w-4 mr-2 text-orange-600" />
                          LPK Mitra *
                        </FormLabel>
                        <Popover open={lpkOpen} onOpenChange={setLpkOpen}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "h-11 justify-between border-gray-300 focus:border-orange-500 focus:ring-orange-500",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? lpkMitraList.find((lpk) => lpk.value === field.value)?.label
                                  : "Pilih LPK Mitra"}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput placeholder="Cari LPK Mitra..." />
                              <CommandList>
                                <CommandEmpty>LPK tidak ditemukan.</CommandEmpty>
                                <CommandGroup>
                                  {lpkMitraList.map((lpk) => (
                                    <CommandItem
                                      value={lpk.label}
                                      key={lpk.value}
                                      onSelect={() => {
                                        form.setValue("lpkMitra", lpk.value)
                                        setLpkOpen(false)
                                      }}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          lpk.value === field.value ? "opacity-100" : "opacity-0",
                                        )}
                                      />
                                      {lpk.label}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormDescription className="text-xs text-gray-500">
                          Pilih LPK Mitra yang mendaftarkan siswa
                        </FormDescription>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-12 border-gray-300 hover:bg-gray-50 bg-transparent"
                    onClick={() => form.reset()}
                  >
                    Reset Form
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 h-12 bg-gradient-to-r from-orange-500 to-maroon-600 hover:from-orange-600 hover:to-maroon-700 text-white font-medium shadow-lg"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Mendaftarkan...
                      </>
                    ) : (
                      <>
                        <User className="h-4 w-4 mr-2" />
                        Daftarkan Siswa
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-blue-800 mb-1">Informasi Penting</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Pastikan semua data sesuai dengan dokumen resmi (KTP, Ijazah, dll)</li>
                  <li>• Data yang sudah disimpan dapat diubah pada tahap verifikasi</li>
                  <li>• Proses selanjutnya akan dilakukan oleh tim verifikasi</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
