# 🤝 Contributing Guidelines

Terima kasih atas minat Anda untuk berkontribusi pada Dashboard Sistem Magang Jepang! Dokumen ini berisi panduan untuk membantu Anda berkontribusi secara efektif.

## 📋 Daftar Isi

- [Code of Conduct](#-code-of-conduct)
- [Getting Started](#-getting-started)
- [Development Workflow](#-development-workflow)
- [Coding Standards](#-coding-standards)
- [Commit Guidelines](#-commit-guidelines)
- [Pull Request Process](#-pull-request-process)
- [Testing Guidelines](#-testing-guidelines)
- [Documentation](#-documentation)

## 🤝 Code of Conduct

Proyek ini mengikuti kode etik yang ramah dan inklusif. Dengan berpartisipasi, Anda diharapkan untuk menjaga standar ini:

- Gunakan bahasa yang ramah dan inklusif
- Hormati sudut pandang dan pengalaman yang berbeda
- Terima kritik konstruktif dengan baik
- Fokus pada apa yang terbaik untuk komunitas
- Tunjukkan empati terhadap anggota komunitas lainnya

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- pnpm (recommended)
- Git
- MySQL/PostgreSQL
- Code editor (VS Code recommended)

### Setup Development Environment
```bash
# 1. Fork dan clone repository
git clone https://github.com/your-username/dashboard-magang-jepang.git
cd dashboard-magang-jepang

# 2. Install dependencies
pnpm install

# 3. Setup environment variables
cp .env.example .env.local
# Edit .env.local dengan konfigurasi Anda

# 4. Setup database
# Jalankan script SQL di folder scripts/

# 5. Start development server
pnpm dev
```

## 🔄 Development Workflow

### Branch Strategy
Kami menggunakan **Git Flow** dengan modifikasi:

- `main` - Production-ready code
- `develop` - Integration branch untuk development
- `feature/*` - Feature branches
- `bugfix/*` - Bug fix branches
- `hotfix/*` - Critical fixes untuk production

### Creating a Feature Branch
```bash
# Update develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/nama-fitur-anda

# Work on your feature...

# Push branch
git push origin feature/nama-fitur-anda
```

## 📝 Coding Standards

### TypeScript Guidelines
- Gunakan TypeScript untuk semua file baru
- Definisikan types/interfaces yang jelas
- Hindari penggunaan `any` type
- Gunakan strict mode TypeScript

```typescript
// ✅ Good
interface SiswaData {
  id: number;
  nama: string;
  email: string;
  status: 'aktif' | 'nonaktif';
}

// ❌ Bad
const siswa: any = {
  id: 1,
  nama: "John",
  // missing properties
};
```

### React Component Guidelines
- Gunakan functional components dengan hooks
- Implement proper error boundaries
- Use TypeScript untuk props
- Follow naming conventions

```tsx
// ✅ Good
interface SiswaCardProps {
  siswa: SiswaData;
  onEdit: (id: number) => void;
}

export function SiswaCard({ siswa, onEdit }: SiswaCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{siswa.nama}</CardTitle>
      </CardHeader>
    </Card>
  );
}

// ❌ Bad
export function siswaCard(props: any) {
  return <div>{props.siswa.nama}</div>;
}
```

### CSS/Styling Guidelines
- Gunakan Tailwind CSS classes
- Hindari inline styles kecuali untuk dynamic values
- Gunakan CSS variables untuk theming
- Follow responsive design principles

```tsx
// ✅ Good
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card className="border-l-4 border-l-blue-500">
    {/* content */}
  </Card>
</div>

// ❌ Bad
<div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)' }}>
  <div style={{ border: '1px solid blue' }}>
    {/* content */}
  </div>
</div>
```

### File Organization
```
src/
├── app/                 # Next.js app directory
│   ├── (dashboard)/     # Route groups
│   ├── api/            # API routes
│   └── globals.css     # Global styles
├── components/         # Reusable components
│   ├── ui/            # Base UI components
│   └── forms/         # Form components
├── lib/               # Utility functions
├── hooks/             # Custom React hooks
├── types/             # TypeScript type definitions
└── utils/             # Helper functions
```

## 📝 Commit Guidelines

Gunakan **Conventional Commits** format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types
- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes (formatting, etc.)
- `refactor` - Code refactoring
- `test` - Adding or updating tests
- `chore` - Maintenance tasks

### Examples
```bash
# Feature
git commit -m "feat(siswa): add bulk import functionality"

# Bug fix
git commit -m "fix(dashboard): resolve chart rendering issue"

# Documentation
git commit -m "docs: update API documentation"

# Breaking change
git commit -m "feat(auth)!: implement new authentication system

BREAKING CHANGE: old auth tokens are no longer valid"
```

## 🔍 Pull Request Process

### Before Creating PR
1. Ensure your branch is up to date with develop
2. Run tests and ensure they pass
3. Run linting and fix any issues
4. Update documentation if needed
5. Test your changes thoroughly

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process
1. At least one approval required
2. All CI checks must pass
3. No merge conflicts
4. Documentation updated if needed

## 🧪 Testing Guidelines

### Unit Testing
```typescript
// Example test file: __tests__/components/SiswaCard.test.tsx
import { render, screen } from '@testing-library/react';
import { SiswaCard } from '@/components/SiswaCard';

describe('SiswaCard', () => {
  const mockSiswa = {
    id: 1,
    nama: 'John Doe',
    email: '<EMAIL>',
    status: 'aktif' as const,
  };

  it('renders siswa name correctly', () => {
    render(<SiswaCard siswa={mockSiswa} onEdit={jest.fn()} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
```

### Integration Testing
- Test API endpoints
- Test database operations
- Test component interactions

### E2E Testing
- Test critical user workflows
- Test cross-browser compatibility
- Test responsive design

## 📚 Documentation

### Code Documentation
- Use JSDoc for functions and components
- Document complex business logic
- Include examples for utility functions

```typescript
/**
 * Calculates the age of a person based on birth date
 * @param birthDate - The birth date in ISO string format
 * @returns The age in years
 * @example
 * ```typescript
 * const age = calculateAge('1990-01-01');
 * console.log(age); // 34 (assuming current year is 2024)
 * ```
 */
export function calculateAge(birthDate: string): number {
  // implementation
}
```

### API Documentation
- Document all API endpoints
- Include request/response examples
- Document error responses
- Use OpenAPI/Swagger when possible

### README Updates
- Update README for new features
- Include setup instructions for new dependencies
- Update screenshots if UI changes significantly

## 🐛 Bug Reports

### Bug Report Template
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional context**
Any other context about the problem.
```

## 💡 Feature Requests

### Feature Request Template
```markdown
**Is your feature request related to a problem?**
A clear description of what the problem is.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
Alternative solutions or features you've considered.

**Additional context**
Any other context or screenshots about the feature request.
```

## 📞 Getting Help

- 💬 **Discussions**: Use GitHub Discussions untuk pertanyaan umum
- 🐛 **Issues**: Buat issue untuk bug reports atau feature requests
- 📧 **Email**: Kontak tim <NAME_EMAIL>

## 🙏 Recognition

Kontributor akan diakui dalam:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Terima kasih atas kontribusi Anda! 🎉
