'use client'

import React from 'react'
import { useAuth } from '@/lib/auth/auth-context'

interface PermissionGuardProps {
  children: React.ReactNode
  module: string
  action: string
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL specified permissions
}

interface MultiplePermissionGuardProps {
  children: React.ReactNode
  permissions: Array<{ module: string; action: string }>
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL specified permissions
}

interface RoleGuardProps {
  children: React.ReactNode
  roles: string | string[]
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL specified roles
}

// Single permission guard
export function PermissionGuard({ 
  children, 
  module, 
  action, 
  fallback = null 
}: PermissionGuardProps) {
  const { hasPermission } = useAuth()

  if (!hasPermission(module, action)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Multiple permissions guard
export function MultiplePermissionGuard({ 
  children, 
  permissions, 
  fallback = null,
  requireAll = false 
}: MultiplePermissionGuardProps) {
  const { hasPermission } = useAuth()

  const hasAccess = requireAll
    ? permissions.every(p => hasPermission(p.module, p.action))
    : permissions.some(p => hasPermission(p.module, p.action))

  if (!hasAccess) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Role-based guard
export function RoleGuard({ 
  children, 
  roles, 
  fallback = null,
  requireAll = false 
}: RoleGuardProps) {
  const { hasRole } = useAuth()

  const roleArray = Array.isArray(roles) ? roles : [roles]
  
  const hasAccess = requireAll
    ? roleArray.every(role => hasRole(role))
    : roleArray.some(role => hasRole(role))

  if (!hasAccess) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Combined permission and role guard
interface CombinedGuardProps {
  children: React.ReactNode
  permissions?: Array<{ module: string; action: string }>
  roles?: string[]
  fallback?: React.ReactNode
  requireAllPermissions?: boolean
  requireAllRoles?: boolean
  requireBoth?: boolean // If true, user must satisfy both permission AND role requirements
}

export function CombinedGuard({
  children,
  permissions = [],
  roles = [],
  fallback = null,
  requireAllPermissions = false,
  requireAllRoles = false,
  requireBoth = false
}: CombinedGuardProps) {
  const { hasPermission, hasRole } = useAuth()

  let hasPermissionAccess = true
  let hasRoleAccess = true

  // Check permissions
  if (permissions.length > 0) {
    hasPermissionAccess = requireAllPermissions
      ? permissions.every(p => hasPermission(p.module, p.action))
      : permissions.some(p => hasPermission(p.module, p.action))
  }

  // Check roles
  if (roles.length > 0) {
    hasRoleAccess = requireAllRoles
      ? roles.every(role => hasRole(role))
      : roles.some(role => hasRole(role))
  }

  // Determine final access
  const hasAccess = requireBoth
    ? hasPermissionAccess && hasRoleAccess
    : hasPermissionAccess || hasRoleAccess

  if (!hasAccess) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Admin-only guard (shortcut for administrator role)
export function AdminGuard({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  return (
    <RoleGuard roles="administrator" fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

// LPK Mitra guard (for LPK-specific content)
export function LPKGuard({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  return (
    <RoleGuard roles="lpk_mitra" fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

// Management roles guard (admin + pimpinan)
export function ManagementGuard({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  return (
    <RoleGuard roles={['administrator', 'pimpinan']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

// Conditional rendering based on user context
interface ConditionalRenderProps {
  children: React.ReactNode
  condition: (context: {
    hasPermission: (module: string, action: string) => boolean
    hasRole: (role: string) => boolean
    userProfile: any
    accessContext: any
  }) => boolean
  fallback?: React.ReactNode
}

export function ConditionalRender({ 
  children, 
  condition, 
  fallback = null 
}: ConditionalRenderProps) {
  const { hasPermission, hasRole, userProfile, accessContext } = useAuth()

  const shouldRender = condition({
    hasPermission,
    hasRole,
    userProfile,
    accessContext
  })

  if (!shouldRender) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Hook for conditional button/action rendering
export function useConditionalAction(
  module: string, 
  action: string,
  additionalCheck?: () => boolean
) {
  const { hasPermission } = useAuth()
  
  const canPerform = hasPermission(module, action) && (additionalCheck ? additionalCheck() : true)
  
  return {
    canPerform,
    disabled: !canPerform,
    className: canPerform ? '' : 'opacity-50 cursor-not-allowed'
  }
}
