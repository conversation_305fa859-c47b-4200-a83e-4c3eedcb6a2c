'use client'

import React from 'react'

interface AuthLoadingProps {
  message?: string
}

export function AuthLoading({ message = "Loading..." }: AuthLoadingProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg flex items-center justify-center mb-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Dashboard Magang Jepang
        </h2>
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  )
}

export function AuthRedirect({ message = "Redirecting to login..." }: AuthLoadingProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg flex items-center justify-center mb-4">
          <div className="animate-pulse">
            <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
            </svg>
          </div>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Dashboard Magang Jepang
        </h2>
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  )
}
