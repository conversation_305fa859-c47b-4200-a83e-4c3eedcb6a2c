'use client'

import React from 'react'
import dynamic from 'next/dynamic'

const MapboxMap = dynamic(() => import('./mapbox-wrapper'), {
  ssr: false,
  loading: () => (
    <div className="h-full w-full bg-gray-100 animate-pulse flex items-center justify-center">
      <div className="text-gray-500">Loading map...</div>
    </div>
  )
})



interface PlacementData {
  id: string
  siswa_id: string
  siswa?: {
    nama_lengkap: string
    jenis_kelamin: string
  }
  perusahaan_penerima?: {
    nama_perusahaan: string
    alamat_jepang: string
    kota_jepang: string
    prefektur: string
    bidang_usaha: string
  }
  kumiai?: {
    nama_kumiai: string
    kode_kumiai: string
  }
  posisi_kerja: string
  gaji_aktual: number
  status_penempatan: string
  tanggal_penempatan: string
}

interface JapanPlacementMapProps {
  data: PlacementData[]
  height?: string
  mapboxToken: string
}

export function JapanPlacementMap({
  data,
  height = '500px',
  mapboxToken
}: JapanPlacementMapProps) {
  return (
    <MapboxMap
      data={data}
      height={height}
      mapboxToken={mapboxToken}
    />
  )
}
