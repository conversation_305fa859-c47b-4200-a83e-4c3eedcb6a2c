'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { MapPin, Building2, Users, DollarSign } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface PlacementData {
  id: string
  siswa_id: string
  siswa?: {
    nama_lengkap: string
    jenis_kelamin: string
  }
  perusahaan_penerima?: {
    nama_perusahaan: string
    alamat_jepang: string
    kota_jepang: string
    prefektur: string
    bidang_usaha: string
  }
  kumiai?: {
    nama_kumiai: string
    kode_kumiai: string
  }
  posisi_kerja: string
  gaji_aktual: number
  status_penempatan: string
  tanggal_penempatan: string
}

interface MapboxWrapperProps {
  data: PlacementData[]
  height?: string
  mapboxToken: string
}

// Prefecture coordinates for Japan
const PREFECTURE_COORDINATES: Record<string, { lat: number; lng: number }> = {
  'Tokyo': { lat: 35.6762, lng: 139.6503 },
  'Osaka': { lat: 34.6937, lng: 135.5023 },
  'Aichi': { lat: 35.1802, lng: 136.9066 },
  'Kanagawa': { lat: 35.4478, lng: 139.6425 },
  'Saitama': { lat: 35.8617, lng: 139.6455 },
  'Chiba': { lat: 35.6074, lng: 140.1065 },
  'Hyogo': { lat: 34.6913, lng: 135.1830 },
  'Kyoto': { lat: 35.0116, lng: 135.7681 },
  'Fukuoka': { lat: 33.5904, lng: 130.4017 },
  'Hokkaido': { lat: 43.2642, lng: 142.7297 },
  'Miyagi': { lat: 38.2682, lng: 140.8694 },
  'Hiroshima': { lat: 34.3853, lng: 132.4553 },
  'Shizuoka': { lat: 34.9756, lng: 138.3828 },
  'Gunma': { lat: 36.3911, lng: 139.0608 },
  'Ibaraki': { lat: 36.3418, lng: 140.4468 },
  'Tochigi': { lat: 36.5658, lng: 139.8836 },
  'Nara': { lat: 34.6851, lng: 135.8048 },
  'Shiga': { lat: 35.0045, lng: 135.8686 },
  'Mie': { lat: 34.7303, lng: 136.5086 },
  'Wakayama': { lat: 34.2261, lng: 135.1675 }
}

export default function MapboxWrapper({ data, height = '500px', mapboxToken }: MapboxWrapperProps) {
  const [selectedMarker, setSelectedMarker] = useState<any>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<any>(null)
  const markers = useRef<any[]>([])

  // Check if we have a valid public token
  const isValidToken = mapboxToken && mapboxToken.startsWith('pk.')

  // Show token error if invalid
  if (!isValidToken) {
    return (
      <div className="h-full w-full bg-gradient-to-br from-red-50 to-orange-50 rounded-lg flex items-center justify-center border-2 border-dashed border-red-200">
        <div className="text-center max-w-md p-6">
          <MapPin className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">Invalid Mapbox Token</h3>
          <p className="text-red-500 text-sm mb-4">
            Token Mapbox harus berupa <strong>public token</strong> yang dimulai dengan <code>pk.</code>
          </p>
          <p className="text-xs text-gray-600 mb-4">
            Token saat ini: <code className="bg-gray-100 px-2 py-1 rounded text-xs">
              {mapboxToken ? `${mapboxToken.substring(0, 10)}...` : 'tidak ada'}
            </code>
          </p>
          <div className="text-xs text-gray-500 space-y-2">
            <p>1. Kunjungi <a href="https://account.mapbox.com/access-tokens/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Mapbox Account</a></p>
            <p>2. Buat atau copy <strong>Public Token</strong> (pk.)</p>
            <p>3. Update NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN di .env.local</p>
          </div>
        </div>
      </div>
    )
  }



  // Initialize Mapbox map
  useEffect(() => {
    if (!mapContainer.current || map.current) return

    const initializeMap = async () => {
      try {
        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl')

        // Import CSS
        await import('mapbox-gl/dist/mapbox-gl.css')

        // Set access token
        mapboxgl.default.accessToken = mapboxToken

        // Create map
        map.current = new mapboxgl.default.Map({
          container: mapContainer.current!,
          style: 'mapbox://styles/mapbox/light-v11',
          center: [138.2529, 36.2048], // Japan center
          zoom: 5.5,
          attributionControl: false
        })

        // Add navigation controls
        map.current.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
        map.current.addControl(new mapboxgl.default.FullscreenControl(), 'top-right')
        map.current.addControl(new mapboxgl.default.ScaleControl(), 'bottom-left')

        // Wait for map to load
        map.current.on('load', () => {
          setMapLoaded(true)
          addMarkersToMap(mapboxgl.default)
        })

        map.current.on('error', (e: any) => {
          console.error('Mapbox error:', e)
          setMapError('Failed to load map')
        })

      } catch (error) {
        console.error('Failed to initialize Mapbox:', error)
        setMapError('Mapbox library not available')
      }
    }

    initializeMap()

    // Cleanup
    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [mapboxToken])

  // Add markers to map
  const addMarkersToMap = (mapboxgl: any) => {
    // Clear existing markers
    markers.current.forEach(marker => marker.remove())
    markers.current = []

    // Add new markers
    prefectureData.forEach((prefData) => {
      // Create marker element
      const markerElement = document.createElement('div')
      markerElement.className = 'custom-marker'
      markerElement.style.cssText = `
        width: ${getMarkerSize(prefData.totalStudents)}px;
        height: ${getMarkerSize(prefData.totalStudents)}px;
        background-color: ${getMarkerColor(prefData.totalStudents)};
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        transition: transform 0.2s;
      `
      markerElement.textContent = prefData.totalStudents.toString()

      // Add hover effect
      markerElement.addEventListener('mouseenter', () => {
        markerElement.style.transform = 'scale(1.1)'
      })
      markerElement.addEventListener('mouseleave', () => {
        markerElement.style.transform = 'scale(1)'
      })

      // Create marker
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([prefData.coordinates.lng, prefData.coordinates.lat])
        .addTo(map.current)

      // Add click event
      markerElement.addEventListener('click', () => {
        setSelectedMarker(prefData)

        // Create popup
        const popup = new mapboxgl.Popup({ offset: 25 })
          .setLngLat([prefData.coordinates.lng, prefData.coordinates.lat])
          .setHTML(`
            <div class="p-3 max-w-xs">
              <h3 class="font-bold text-lg mb-2">${prefData.prefecture}</h3>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>Siswa:</span>
                  <span class="font-medium">${prefData.totalStudents}</span>
                </div>
                <div class="flex justify-between">
                  <span>Perusahaan:</span>
                  <span class="font-medium">${prefData.companiesCount}</span>
                </div>
                <div class="flex justify-between">
                  <span>Rata-rata Gaji:</span>
                  <span class="font-medium">${formatCurrency(prefData.averageSalary)}</span>
                </div>
              </div>
            </div>
          `)
          .addTo(map.current)
      })

      markers.current.push(marker)
    })
  }

  // Update markers when data changes
  useEffect(() => {
    if (mapLoaded && map.current) {
      const mapboxgl = require('mapbox-gl')
      addMarkersToMap(mapboxgl.default || mapboxgl)
    }
  }, [data, mapLoaded])

  // Group data by prefecture for statistics
  const prefectureData = React.useMemo(() => {
    const grouped = data.reduce((acc, placement) => {
      const prefecture = placement.perusahaan_penerima?.prefektur
      if (!prefecture || !PREFECTURE_COORDINATES[prefecture]) return acc

      if (!acc[prefecture]) {
        acc[prefecture] = {
          prefecture,
          coordinates: PREFECTURE_COORDINATES[prefecture],
          placements: [],
          totalStudents: 0,
          companies: new Set(),
          averageSalary: 0
        }
      }

      acc[prefecture].placements.push(placement)
      acc[prefecture].totalStudents += 1
      if (placement.perusahaan_penerima?.nama_perusahaan) {
        acc[prefecture].companies.add(placement.perusahaan_penerima.nama_perusahaan)
      }

      return acc
    }, {} as Record<string, any>)

    // Calculate average salary for each prefecture
    Object.values(grouped).forEach((prefData: any) => {
      const totalSalary = prefData.placements.reduce((sum: number, p: PlacementData) =>
        sum + (p.gaji_aktual || 0), 0)
      prefData.averageSalary = Math.round(totalSalary / prefData.totalStudents)
      prefData.companiesCount = prefData.companies.size
    })

    return Object.values(grouped).sort((a: any, b: any) => b.totalStudents - a.totalStudents)
  }, [data])

  // Get marker color based on student count
  const getMarkerColor = (studentCount: number) => {
    if (studentCount >= 20) return '#800000' // Maroon for high concentration
    if (studentCount >= 10) return '#FFA500' // Orange for medium concentration
    if (studentCount >= 5) return '#FFD700'  // Gold for low-medium concentration
    return '#87CEEB' // Sky blue for low concentration
  }

  // Get marker size based on student count
  const getMarkerSize = (studentCount: number) => {
    if (studentCount >= 20) return 40
    if (studentCount >= 10) return 32
    if (studentCount >= 5) return 24
    return 16
  }

  const handleMarkerClick = useCallback((prefData: any) => {
    setSelectedMarker(prefData)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'aktif':
        return 'default'
      case 'berangkat':
        return 'secondary'
      case 'selesai':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  // Show map error if any
  if (mapError) {
    return (
      <div className="h-full w-full bg-gradient-to-br from-red-50 to-orange-50 rounded-lg flex items-center justify-center border-2 border-dashed border-red-200">
        <div className="text-center max-w-md p-6">
          <MapPin className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-600 mb-2">Map Error</h3>
          <p className="text-red-500 text-sm mb-4">{mapError}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
          >
            Refresh Page
          </button>
        </div>
      </div>
    )
  }

  // Render the actual Mapbox map
  return (
    <div className="w-full relative" style={{ height }}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className="w-full h-full rounded-lg overflow-hidden"
        style={{ minHeight: '400px' }}
      />

      {/* Loading Overlay */}
      {!mapLoaded && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-3"></div>
            <p className="text-gray-600 text-sm">Loading Japan Map...</p>
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border z-10">
        <h4 className="font-medium text-sm text-gray-900 mb-2">Jumlah Siswa</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 rounded-full bg-sky-400"></div>
            <span>1-4 siswa</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 rounded-full" style={{ backgroundColor: '#FFD700' }}></div>
            <span>5-9 siswa</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-orange-500"></div>
            <span>10-19 siswa</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-7 h-7 rounded-full bg-red-900"></div>
            <span>20+ siswa</span>
          </div>
        </div>
      </div>

      {/* Map Info */}
      <div className="absolute top-4 left-4 bg-white p-3 rounded-lg shadow-lg border z-10">
        <h4 className="font-medium text-sm text-gray-900 mb-1">Peta Penempatan Siswa</h4>
        <p className="text-xs text-gray-600">Klik marker untuk detail prefecture</p>
      </div>

      {/* Prefecture Statistics Panel */}
      {selectedMarker && (
        <div className="absolute top-4 right-4 bg-white p-4 rounded-lg shadow-lg border z-10 max-w-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-bold text-lg text-gray-900">{selectedMarker.prefecture}</h3>
            <button
              onClick={() => setSelectedMarker(null)}
              className="text-gray-500 hover:text-gray-700 text-lg"
            >
              ×
            </button>
          </div>

          <div className="space-y-3 text-sm">
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-2 bg-blue-50 rounded">
                <Users className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                <div className="font-bold text-blue-800">{selectedMarker.totalStudents}</div>
                <div className="text-xs text-blue-600">Siswa</div>
              </div>
              <div className="text-center p-2 bg-green-50 rounded">
                <Building2 className="h-5 w-5 text-green-600 mx-auto mb-1" />
                <div className="font-bold text-green-800">{selectedMarker.companiesCount}</div>
                <div className="text-xs text-green-600">Perusahaan</div>
              </div>
            </div>

            <div className="text-center p-2 bg-yellow-50 rounded">
              <DollarSign className="h-5 w-5 text-yellow-600 mx-auto mb-1" />
              <div className="font-bold text-yellow-800">{formatCurrency(selectedMarker.averageSalary)}</div>
              <div className="text-xs text-yellow-600">Rata-rata Gaji</div>
            </div>

            <div className="border-t pt-3">
              <h4 className="font-medium text-gray-900 mb-2">Penempatan Terbaru</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {selectedMarker.placements.slice(0, 3).map((placement: PlacementData) => (
                  <div key={placement.id} className="text-xs bg-gray-50 p-2 rounded">
                    <div className="font-medium text-gray-900">{placement.siswa?.nama_lengkap}</div>
                    <div className="text-gray-600">{placement.perusahaan_penerima?.nama_perusahaan}</div>
                  </div>
                ))}
                {selectedMarker.placements.length > 3 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{selectedMarker.placements.length - 3} lainnya
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )

}
