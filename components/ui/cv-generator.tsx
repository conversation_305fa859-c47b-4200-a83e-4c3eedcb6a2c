"use client"

import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Printer, 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  GraduationCap, 
  Briefcase, 
  Users, 
  Globe,
  MessageSquare,
  Calendar,
  Award,
  Languages
} from 'lucide-react'
import { SiswaWithRelations } from '@/lib/types/database'
import { toast } from '@/hooks/use-toast'

interface CVGeneratorProps {
  siswa: SiswaWithRelations
}

export function CVGenerator({ siswa }: CVGeneratorProps) {
  const cvRef = useRef<HTMLDivElement>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  const downloadPDF = async () => {
    if (!cvRef.current) return

    setIsGenerating(true)
    try {
      // Import html2canvas and jsPDF dynamically
      const html2canvas = (await import('html2canvas')).default
      const jsPDF = (await import('jspdf')).default

      const canvas = await html2canvas(cvRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgWidth = 210
      const pageHeight = 295
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      pdf.save(`CV_${siswa.nama_lengkap.replace(/\s+/g, '_')}.pdf`)
      toast({
        title: "CV berhasil diunduh",
        description: "File PDF telah disimpan ke perangkat Anda"
      })
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast({
        title: "Gagal mengunduh CV",
        description: "Terjadi kesalahan saat menghasilkan PDF",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const printCV = () => {
    if (!cvRef.current) return

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>CV ${siswa.nama_lengkap}</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px;
                background: white;
              }
              .cv-container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
              }
              .header { 
                text-align: center; 
                border-bottom: 3px solid #2563eb;
                padding-bottom: 20px;
                margin-bottom: 30px;
              }
              .name { 
                font-size: 32px; 
                font-weight: bold; 
                color: #1e40af;
                margin-bottom: 10px;
              }
              .title { 
                font-size: 18px; 
                color: #64748b;
                margin-bottom: 15px;
              }
              .contact-info {
                display: flex;
                justify-content: center;
                gap: 20px;
                flex-wrap: wrap;
                font-size: 14px;
                color: #64748b;
              }
              .section {
                margin-bottom: 30px;
              }
              .section-title {
                font-size: 20px;
                font-weight: bold;
                color: #1e40af;
                border-bottom: 2px solid #e2e8f0;
                padding-bottom: 8px;
                margin-bottom: 15px;
              }
              .info-grid {
                display: grid;
                grid-template-columns: 1fr 2fr;
                gap: 10px;
                margin-bottom: 15px;
              }
              .info-label {
                font-weight: bold;
                color: #374151;
              }
              .info-value {
                color: #4b5563;
              }
              .education-item, .experience-item {
                margin-bottom: 20px;
                padding: 15px;
                border-left: 4px solid #2563eb;
                background: #f8fafc;
              }
              .education-title, .experience-title {
                font-weight: bold;
                color: #1e40af;
                margin-bottom: 5px;
              }
              .education-details, .experience-details {
                color: #64748b;
                font-size: 14px;
              }
              .skills {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
              }
              .skill-badge {
                background: #2563eb;
                color: white;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
              }
              .social-media {
                display: flex;
                gap: 15px;
                flex-wrap: wrap;
              }
              .social-item {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 14px;
                color: #64748b;
              }
              @media print {
                body { margin: 0; }
                .cv-container { box-shadow: none; }
              }
            </style>
          </head>
          <body>
            ${cvRef.current.innerHTML}
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Generate CV</h2>
          <p className="text-gray-600">Buat dan unduh CV profesional untuk {siswa.nama_lengkap}</p>
        </div>
        <div className="flex gap-3">
          <Button 
            onClick={downloadPDF} 
            disabled={isGenerating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Download className="h-4 w-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Download PDF'}
          </Button>
          <Button 
            onClick={printCV}
            variant="outline"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print CV
          </Button>
        </div>
      </div>

      {/* CV Preview */}
      <Card className="overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Preview CV
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div 
            ref={cvRef}
            className="bg-white p-8 max-w-4xl mx-auto"
            style={{ minHeight: '297mm', width: '210mm' }}
          >
            {/* Header */}
            <div className="text-center border-b-4 border-blue-600 pb-6 mb-8">
              <h1 className="text-3xl font-bold text-blue-800 mb-2">
                {siswa.nama_lengkap}
              </h1>
              {siswa.nama_lengkap_jepang && (
                <p className="text-lg text-gray-600 mb-3">
                  {siswa.nama_lengkap_jepang}
                </p>
              )}
              <p className="text-lg text-gray-700 mb-4">
                {siswa.pendidikan_terakhir} • {siswa.jurusan || 'Jurusan'}
              </p>
              <div className="flex justify-center gap-6 text-sm text-gray-600 flex-wrap">
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  {siswa.email || 'Email tidak tersedia'}
                </div>
                <div className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  {siswa.nomor_hp}
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {siswa.alamat_lengkap}
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <User className="h-5 w-5 inline mr-2" />
                Informasi Pribadi
              </h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-semibold text-gray-700">NIK:</span>
                  <span className="ml-2 text-gray-600">{siswa.nik}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Tempat, Tanggal Lahir:</span>
                  <span className="ml-2 text-gray-600">
                    {siswa.tempat_lahir}, {formatDate(siswa.tanggal_lahir)} ({calculateAge(siswa.tanggal_lahir)} tahun)
                  </span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Jenis Kelamin:</span>
                  <span className="ml-2 text-gray-600">
                    {siswa.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'}
                  </span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Agama:</span>
                  <span className="ml-2 text-gray-600">{siswa.agama || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Status Pernikahan:</span>
                  <span className="ml-2 text-gray-600">{siswa.status_pernikahan || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Golongan Darah:</span>
                  <span className="ml-2 text-gray-600">{siswa.golongan_darah || '-'}</span>
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <GraduationCap className="h-5 w-5 inline mr-2" />
                Pendidikan
              </h2>
              
              {/* Main Education */}
              <div className="mb-4 p-4 border-l-4 border-blue-600 bg-blue-50">
                <h3 className="font-bold text-blue-800 mb-1">
                  {siswa.pendidikan_terakhir} - {siswa.nama_sekolah}
                </h3>
                <p className="text-gray-600 text-sm">
                  {siswa.jurusan && `${siswa.jurusan} • `}
                  Lulus: {siswa.tahun_lulus || '-'}
                  {siswa.ipk && ` • IPK: ${siswa.ipk}`}
                </p>
              </div>

              {/* Additional Education */}
              {(siswa as any).siswa_pendidikan && (siswa as any).siswa_pendidikan.length > 0 && (
                <div className="space-y-3">
                  {(siswa as any).siswa_pendidikan.map((edu: any, index: number) => (
                    <div key={index} className="p-3 border-l-4 border-gray-300 bg-gray-50">
                      <h4 className="font-semibold text-gray-800 mb-1">
                        {edu.jenjang} - {edu.nama_institusi}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {edu.jurusan && `${edu.jurusan} • `}
                        {edu.tahun_masuk} - {edu.tahun_lulus}
                        {edu.ipk && ` • IPK: ${edu.ipk}`}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Work Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <Briefcase className="h-5 w-5 inline mr-2" />
                Pengalaman Kerja
              </h2>
              
              {(siswa as any).siswa_pengalaman_kerja && (siswa as any).siswa_pengalaman_kerja.length > 0 ? (
                <div className="space-y-4">
                  {(siswa as any).siswa_pengalaman_kerja.map((exp: any, index: number) => (
                    <div key={index} className="p-4 border-l-4 border-green-600 bg-green-50">
                      <h3 className="font-bold text-green-800 mb-1">
                        {exp.posisi} - {exp.nama_perusahaan}
                      </h3>
                      <p className="text-gray-600 text-sm mb-2">
                        {exp.tahun_mulai} - {exp.tahun_selesai || 'Sekarang'}
                      </p>
                      {exp.deskripsi_pekerjaan && (
                        <p className="text-gray-700 text-sm">
                          {exp.deskripsi_pekerjaan}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">Belum ada pengalaman kerja</p>
              )}
            </div>

            {/* Skills & Languages */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <Award className="h-5 w-5 inline mr-2" />
                Keahlian & Bahasa
              </h2>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-700 mb-2">Keahlian Khusus:</h3>
                  <div className="flex flex-wrap gap-2">
                    {siswa.bakat_khusus && (
                      <Badge variant="secondary">{siswa.bakat_khusus}</Badge>
                    )}
                    {siswa.sertifikat_keahlian && (
                      <Badge variant="secondary">{siswa.sertifikat_keahlian}</Badge>
                    )}
                    {siswa.sertifikat_bahasa && (
                      <Badge variant="secondary">{siswa.sertifikat_bahasa}</Badge>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-700 mb-2">Bahasa Jepang:</h3>
                  <Badge variant="outline" className="text-blue-600 border-blue-600">
                    {siswa.level_bahasa_jepang || 'Belum ditentukan'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Family */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <Users className="h-5 w-5 inline mr-2" />
                Informasi Keluarga
              </h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-semibold text-gray-700">Nama Ayah:</span>
                  <span className="ml-2 text-gray-600">{siswa.nama_ayah || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Pekerjaan Ayah:</span>
                  <span className="ml-2 text-gray-600">{siswa.pekerjaan_ayah || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Nama Ibu:</span>
                  <span className="ml-2 text-gray-600">{siswa.nama_ibu || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Pekerjaan Ibu:</span>
                  <span className="ml-2 text-gray-600">{siswa.pekerjaan_ibu || '-'}</span>
                </div>
              </div>
            </div>

            {/* Social Media */}
            {(siswa as any).siswa_social_media && (siswa as any).siswa_social_media.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                  <MessageSquare className="h-5 w-5 inline mr-2" />
                  Media Sosial
                </h2>
                <div className="flex flex-wrap gap-4">
                  {(siswa as any).siswa_social_media.map((social: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        {social.platform}: @{social.username}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Additional Information */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                <Calendar className="h-5 w-5 inline mr-2" />
                Informasi Tambahan
              </h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-semibold text-gray-700">Hobi:</span>
                  <span className="ml-2 text-gray-600">{siswa.hobi || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Minat Kerja:</span>
                  <span className="ml-2 text-gray-600">{siswa.minat_kerja || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Tujuan ke Jepang:</span>
                  <span className="ml-2 text-gray-600">{siswa.tujuan_ke_jepang || '-'}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">Target Kerja Jepang:</span>
                  <span className="ml-2 text-gray-600">{siswa.target_kerja_jepang || '-'}</span>
                </div>
              </div>
            </div>

            {/* LPK Information */}
            {siswa.lpk_mitra && (
              <div className="mb-8">
                <h2 className="text-xl font-bold text-blue-800 border-b-2 border-gray-200 pb-2 mb-4">
                  <GraduationCap className="h-5 w-5 inline mr-2" />
                  Informasi LPK
                </h2>
                <div className="p-4 border-l-4 border-orange-600 bg-orange-50">
                  <h3 className="font-bold text-orange-800 mb-1">
                    {siswa.lpk_mitra.nama_lpk}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Masuk: {siswa.tanggal_masuk_lpk ? formatDate(siswa.tanggal_masuk_lpk) : '-'} • 
                    Lama Belajar: {siswa.lama_belajar_bulan || '-'} bulan
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 