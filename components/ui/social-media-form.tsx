'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2, Edit, Share2 } from 'lucide-react'

interface SocialMediaAccount {
  id?: string
  platform: string
  username: string
  url?: string
  is_active: boolean
}

interface SocialMediaFormProps {
  socialMedia: SocialMediaAccount[]
  onChange: (socialMedia: SocialMediaAccount[]) => void
  className?: string
}

const SOCIAL_MEDIA_PLATFORMS = [
  { value: 'instagram', label: 'Instagram', icon: '📷' },
  { value: 'facebook', label: 'Facebook', icon: '📘' },
  { value: 'twitter', label: 'Twitter/X', icon: '🐦' },
  { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
  { value: 'tiktok', label: 'TikTok', icon: '🎵' },
  { value: 'youtube', label: 'YouTube', icon: '📺' },
  { value: 'whatsapp', label: 'WhatsApp', icon: '💬' },
  { value: 'telegram', label: 'Telegram', icon: '📱' },
  { value: 'line', label: 'Line', icon: '💚' },
  { value: 'wechat', label: 'WeChat', icon: '💬' },
  { value: 'other', label: 'Lainnya', icon: '🔗' }
]

export function SocialMediaForm({ socialMedia, onChange, className }: SocialMediaFormProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [newAccount, setNewAccount] = useState<SocialMediaAccount>({
    platform: '',
    username: '',
    url: '',
    is_active: true
  })

  const handleAddAccount = () => {
    if (!newAccount.platform || !newAccount.username) return

    const updatedSocialMedia = [...socialMedia, { ...newAccount }]
    onChange(updatedSocialMedia)
    
    // Reset form
    setNewAccount({
      platform: '',
      username: '',
      url: '',
      is_active: true
    })
  }

  const handleEditAccount = (index: number) => {
    setEditingIndex(index)
    setNewAccount({ ...socialMedia[index] })
  }

  const handleUpdateAccount = () => {
    if (editingIndex === null || !newAccount.platform || !newAccount.username) return

    const updatedSocialMedia = [...socialMedia]
    updatedSocialMedia[editingIndex] = { ...newAccount }
    onChange(updatedSocialMedia)
    
    // Reset form
    setEditingIndex(null)
    setNewAccount({
      platform: '',
      username: '',
      url: '',
      is_active: true
    })
  }

  const handleDeleteAccount = (index: number) => {
    const updatedSocialMedia = socialMedia.filter((_, i) => i !== index)
    onChange(updatedSocialMedia)
  }

  const handleCancelEdit = () => {
    setEditingIndex(null)
    setNewAccount({
      platform: '',
      username: '',
      url: '',
      is_active: true
    })
  }

  const getPlatformInfo = (platform: string) => {
    return SOCIAL_MEDIA_PLATFORMS.find(p => p.value === platform) || 
           { value: platform, label: platform, icon: '🔗' }
  }

  const generateUrl = (platform: string, username: string) => {
    const cleanUsername = username.replace('@', '')
    
    switch (platform) {
      case 'instagram':
        return `https://instagram.com/${cleanUsername}`
      case 'facebook':
        return `https://facebook.com/${cleanUsername}`
      case 'twitter':
        return `https://twitter.com/${cleanUsername}`
      case 'linkedin':
        return `https://linkedin.com/in/${cleanUsername}`
      case 'tiktok':
        return `https://tiktok.com/@${cleanUsername}`
      case 'youtube':
        return `https://youtube.com/@${cleanUsername}`
      case 'whatsapp':
        return `https://wa.me/${cleanUsername.replace(/\D/g, '')}`
      case 'telegram':
        return `https://t.me/${cleanUsername}`
      case 'line':
        return `https://line.me/ti/p/${cleanUsername}`
      default:
        return username.startsWith('http') ? username : ''
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5" />
          Media Sosial
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add/Edit Form */}
        <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <h4 className="font-medium">
              {editingIndex !== null ? 'Edit Akun' : 'Tambah Akun Baru'}
            </h4>
            {editingIndex !== null && (
              <Button variant="ghost" size="sm" onClick={handleCancelEdit}>
                Batal
              </Button>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="platform">Platform</Label>
              <Select 
                value={newAccount.platform} 
                onValueChange={(value) => setNewAccount({ ...newAccount, platform: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih platform" />
                </SelectTrigger>
                <SelectContent>
                  {SOCIAL_MEDIA_PLATFORMS.map((platform) => (
                    <SelectItem key={platform.value} value={platform.value}>
                      <span className="flex items-center gap-2">
                        <span>{platform.icon}</span>
                        <span>{platform.label}</span>
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                placeholder="Masukkan username"
                value={newAccount.username}
                onChange={(e) => setNewAccount({ ...newAccount, username: e.target.value })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">URL (Opsional)</Label>
            <Input
              id="url"
              placeholder="URL profil lengkap"
              value={newAccount.url}
              onChange={(e) => setNewAccount({ ...newAccount, url: e.target.value })}
            />
            {newAccount.platform && newAccount.username && !newAccount.url && (
              <div className="text-sm text-gray-600">
                URL yang disarankan: {generateUrl(newAccount.platform, newAccount.username)}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={newAccount.is_active}
              onCheckedChange={(checked) => setNewAccount({ ...newAccount, is_active: checked })}
            />
            <Label htmlFor="is_active">Akun aktif</Label>
          </div>

          <Button 
            onClick={editingIndex !== null ? handleUpdateAccount : handleAddAccount}
            disabled={!newAccount.platform || !newAccount.username}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            {editingIndex !== null ? 'Update Akun' : 'Tambah Akun'}
          </Button>
        </div>

        {/* Existing Accounts */}
        {socialMedia.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Akun Media Sosial</h4>
            <div className="space-y-2">
              {socialMedia.map((account, index) => {
                const platformInfo = getPlatformInfo(account.platform)
                const displayUrl = account.url || generateUrl(account.platform, account.username)
                
                return (
                  <div 
                    key={index} 
                    className={`flex items-center justify-between p-3 border rounded-lg ${
                      !account.is_active ? 'bg-gray-50 opacity-60' : 'bg-white'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{platformInfo.icon}</div>
                      <div>
                        <div className="font-medium">{platformInfo.label}</div>
                        <div className="text-sm text-gray-600">@{account.username}</div>
                        {displayUrl && (
                          <a 
                            href={displayUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-xs text-blue-600 hover:underline"
                          >
                            {displayUrl}
                          </a>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {!account.is_active && (
                        <Badge variant="secondary">Tidak Aktif</Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditAccount(index)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteAccount(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {socialMedia.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Share2 className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>Belum ada akun media sosial yang ditambahkan</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 