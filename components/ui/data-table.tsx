import React from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DataTablePagination } from "@/components/ui/data-table-pagination"
import { usePagination } from "@/hooks/use-pagination"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

interface Column<T> {
  key: string
  header: string
  cell: (item: T, index: number) => React.ReactNode
  className?: string
}

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  title?: string
  description?: string
  isLoading?: boolean
  itemsPerPage?: number
  enableKeyboardNavigation?: boolean
  showPagination?: boolean
  emptyMessage?: string
  headerActions?: React.ReactNode
}

export function DataTable<T extends { id: string }>({
  data,
  columns,
  title,
  description,
  isLoading = false,
  itemsPerPage = 10,
  enableKeyboardNavigation = true,
  showPagination = true,
  emptyMessage = "Tidak ada data yang tersedia",
  headerActions
}: DataTableProps<T>) {
  const pagination = usePagination({
    data,
    itemsPerPage,
    enableKeyboardNavigation
  })

  const LoadingRow = () => (
    <TableRow>
      <TableCell colSpan={columns.length + 1} className="h-24 text-center">
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Memuat data...</span>
        </div>
      </TableCell>
    </TableRow>
  )

  const EmptyRow = () => (
    <TableRow>
      <TableCell colSpan={columns.length + 1} className="h-24 text-center text-gray-500">
        {emptyMessage}
      </TableCell>
    </TableRow>
  )

  return (
    <Card className="w-full">
      {(title || description || headerActions) && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="space-y-1">
            {title && <CardTitle className="text-2xl font-bold">{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {headerActions && <div className="flex items-center space-x-2">{headerActions}</div>}
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-center w-16">No</TableHead>
                {columns.map((column) => (
                  <TableHead key={column.key} className={`font-semibold ${column.className || ''}`}>
                    {column.header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <LoadingRow />
              ) : pagination.paginatedData.length > 0 ? (
                pagination.paginatedData.map((item, index) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="text-center font-medium">
                      {(pagination.currentPage - 1) * pagination.itemsPerPage + index + 1}
                    </TableCell>
                    {columns.map((column) => (
                      <TableCell key={column.key} className={column.className}>
                        {column.cell(item, index)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <EmptyRow />
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {showPagination && pagination.totalItems > 0 && (
          <DataTablePagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            itemsPerPage={pagination.itemsPerPage}
            startIndex={pagination.startIndex}
            endIndex={pagination.endIndex}
            onPageChange={pagination.goToPage}
            onItemsPerPageChange={pagination.setItemsPerPage}
            canGoNext={pagination.canGoNext}
            canGoPrevious={pagination.canGoPrevious}
            isLoading={isLoading}
          />
        )}
      </CardContent>
    </Card>
  )
}
