'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, Search, X, Loader2 } from 'lucide-react'

interface MapPickerProps {
  latitude?: number
  longitude?: number
  alamat_koordinat?: string
  onLocationChange: (lat: number, lng: number, address: string) => void
  className?: string
}

export function MapPicker({ 
  latitude, 
  longitude, 
  alamat_koordinat, 
  onLocationChange, 
  className 
}: MapPickerProps) {
  const [searchAddress, setSearchAddress] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [mapLoading, setMapLoading] = useState(true)
  const [error, setError] = useState('')
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const markerRef = useRef<any>(null)
  const popupRef = useRef<any>(null)

  // Get Mapbox token from environment
  const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN

  useEffect(() => {
    // Initialize map when component mounts
    if (mapRef.current && !mapInstanceRef.current && mapboxToken) {
      initializeMap()
    }
  }, [mapboxToken])

  useEffect(() => {
    // Update marker when coordinates change
    if (latitude && longitude && mapInstanceRef.current) {
      updateMarker(latitude, longitude).catch(console.error)
    }
  }, [latitude, longitude])

  const initializeMap = async () => {
    if (!mapRef.current || !mapboxToken) return

    try {
      // Dynamically import mapbox-gl
      const mapboxgl = await import('mapbox-gl')
      
      // Import CSS dynamically
      if (typeof window !== 'undefined') {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
        document.head.appendChild(link)
      }

      // Set access token
      mapboxgl.default.accessToken = mapboxToken

      // Create map centered on Semarang, Indonesia
      mapInstanceRef.current = new mapboxgl.default.Map({
        container: mapRef.current,
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [110.4203, -7.0051], // Semarang coordinates
        zoom: 12,
        attributionControl: false
      })

      // Add navigation controls
      mapInstanceRef.current.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
      mapInstanceRef.current.addControl(new mapboxgl.default.FullscreenControl(), 'top-right')

              // Wait for map to load
        mapInstanceRef.current.on('load', () => {
          setMapLoading(false)
          
          // If we have initial coordinates, add marker
          if (latitude && longitude) {
            updateMarker(latitude, longitude).catch(console.error)
          }
        })

              // Add click event to map
        mapInstanceRef.current.on('click', (e: any) => {
          const { lng, lat } = e.lngLat
          handleMapClick(lat, lng).catch(console.error)
        })

      mapInstanceRef.current.on('error', (e: any) => {
        console.error('Mapbox error:', e)
        setError('Gagal memuat peta')
      })

    } catch (error) {
      console.error('Failed to initialize Mapbox:', error)
      setError('Mapbox tidak tersedia')
      setMapLoading(false)
    }
  }

  const updateMarker = async (lat: number, lng: number) => {
    if (!mapInstanceRef.current) return

    // Remove existing marker
    if (markerRef.current) {
      markerRef.current.remove()
    }
    if (popupRef.current) {
      popupRef.current.remove()
    }

    try {
      // Import mapbox-gl dynamically
      const mapboxgl = await import('mapbox-gl')
      
      const markerElement = document.createElement('div')
      markerElement.className = 'custom-marker'
      markerElement.style.cssText = `
        width: 30px;
        height: 30px;
        background-color: #3b82f6;
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      `
      markerElement.innerHTML = '📍'

      markerRef.current = new mapboxgl.default.Marker(markerElement)
        .setLngLat([lng, lat])
        .addTo(mapInstanceRef.current)

      // Create popup
      popupRef.current = new mapboxgl.default.Popup({ offset: 25 })
        .setLngLat([lng, lat])
        .setHTML(`
          <div class="p-2">
            <div class="font-medium">Lokasi Dipilih</div>
            <div class="text-sm text-gray-600">${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
          </div>
        `)
        .addTo(mapInstanceRef.current)

      // Fly to location
      mapInstanceRef.current.flyTo({
        center: [lng, lat],
        zoom: 15,
        duration: 2000
      })
    } catch (error) {
      console.error('Error creating marker:', error)
    }
  }

  const handleSearch = async () => {
    if (!searchAddress.trim() || !mapboxToken) return

    setIsLoading(true)
    setError('')

    try {
      // Use Mapbox Geocoding API
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchAddress)}.json?access_token=${mapboxToken}&country=ID&limit=1`
      )
      
      const data = await response.json()
      
      if (data.features && data.features.length > 0) {
        const [lng, lat] = data.features[0].center
        const address = data.features[0].place_name
        
        onLocationChange(lat, lng, address)
        setSearchAddress('')
      } else {
        setError('Alamat tidak ditemukan')
      }
    } catch (err) {
      console.error('Geocoding error:', err)
      setError('Gagal mencari alamat. Silakan coba lagi.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleMapClick = async (lat: number, lng: number) => {
    // Reverse geocoding to get address
    if (!mapboxToken) {
      onLocationChange(lat, lng, 'Lokasi yang dipilih')
      return
    }

    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxToken}&country=ID&limit=1`
      )
      const data = await response.json()
      
      if (data.features && data.features.length > 0) {
        const address = data.features[0].place_name
        onLocationChange(lat, lng, address)
      } else {
        onLocationChange(lat, lng, 'Lokasi yang dipilih')
      }
    } catch (error) {
      console.error('Reverse geocoding error:', error)
      onLocationChange(lat, lng, 'Lokasi yang dipilih')
    }
  }

  const clearLocation = () => {
    try {
      if (markerRef.current) {
        markerRef.current.remove()
        markerRef.current = null
      }
      if (popupRef.current) {
        popupRef.current.remove()
        popupRef.current = null
      }
      onLocationChange(0, 0, '')
    } catch (error) {
      console.error('Error clearing location:', error)
    }
  }

  // Check if Mapbox token is valid
  if (!mapboxToken || !mapboxToken.startsWith('pk.')) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Pilih Lokasi Alamat
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8 text-gray-500">
            <MapPin className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Mapbox token tidak valid atau tidak ditemukan</p>
            <p className="text-xs text-gray-400 mt-1">
              Pastikan NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN sudah diset di .env.local
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Pilih Lokasi Alamat
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Address */}
        <div className="space-y-2">
          <Label htmlFor="search-address">Cari Alamat</Label>
          <div className="flex gap-2">
            <Input
              id="search-address"
              placeholder="Masukkan alamat lengkap..."
              value={searchAddress}
              onChange={(e) => setSearchAddress(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button 
              onClick={handleSearch} 
              disabled={isLoading}
              size="sm"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Map Display */}
        <div className="space-y-2">
          <Label>Peta Lokasi</Label>
          <div className="relative">
            <div 
              ref={mapRef}
              className="w-full h-64 border rounded-md overflow-hidden"
            />
            
            {/* Loading Overlay */}
            {mapLoading && (
              <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
                  <p className="text-gray-600 text-sm">Memuat peta...</p>
                </div>
              </div>
            )}

            {/* Instructions */}
            {!mapLoading && !latitude && !longitude && (
              <div className="absolute top-2 left-2 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-md text-sm text-gray-600">
                Klik pada peta untuk memilih lokasi
              </div>
            )}
          </div>
        </div>

        {/* Current Location Display */}
        {latitude && longitude && (
          <div className="space-y-2">
            <Label>Koordinat Saat Ini</Label>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium">Latitude:</span> {latitude.toFixed(6)}
              </div>
              <div>
                <span className="font-medium">Longitude:</span> {longitude.toFixed(6)}
              </div>
            </div>
            {alamat_koordinat && (
              <div className="text-sm">
                <span className="font-medium">Alamat:</span> {alamat_koordinat}
              </div>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearLocation}
              className="mt-2"
            >
              <X className="h-4 w-4 mr-2" />
              Hapus Lokasi
            </Button>
          </div>
        )}

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}
      </CardContent>
    </Card>
  )
} 