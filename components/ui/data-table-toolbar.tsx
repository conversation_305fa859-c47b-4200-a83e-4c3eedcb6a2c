import React from 'react'
import { Search, Filter, X } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

interface FilterOption {
  value: string
  label: string
}

interface FilterConfig {
  key: string
  label: string
  options: FilterOption[]
  value: string
  onChange: (value: string) => void
}

interface DataTableToolbarProps {
  searchValue: string
  onSearchChange: (value: string) => void
  searchPlaceholder?: string
  filters?: FilterConfig[]
  actions?: React.ReactNode
  showClearFilters?: boolean
  onClearFilters?: () => void
}

export function DataTableToolbar({
  searchValue,
  onSearchChange,
  searchPlaceholder = "Cari data...",
  filters = [],
  actions,
  showClearFilters = false,
  onClearFilters
}: DataTableToolbarProps) {
  const hasActiveFilters = searchValue || filters.some(filter => filter.value && filter.value !== 'all')

  return (
    <div className="flex flex-col sm:flex-row gap-4 p-4 bg-white border-b">
      {/* Search Input */}
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder={searchPlaceholder}
          value={searchValue}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Filters */}
      {filters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.map((filter) => (
            <Select
              key={filter.key}
              value={filter.value}
              onValueChange={filter.onChange}
            >
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <SelectValue placeholder={filter.label} />
                </div>
              </SelectTrigger>
              <SelectContent>
                {filter.options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ))}
        </div>
      )}

      {/* Actions */}
      {actions && (
        <div className="flex items-center gap-2">
          {actions}
        </div>
      )}

      {/* Clear Filters */}
      {showClearFilters && hasActiveFilters && onClearFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          Clear Filters
        </Button>
      )}
    </div>
  )
}

// Active Filters Display Component
interface ActiveFiltersProps {
  searchValue: string
  filters: FilterConfig[]
  onRemoveSearch: () => void
  onRemoveFilter: (filterKey: string) => void
}

export function ActiveFilters({
  searchValue,
  filters,
  onRemoveSearch,
  onRemoveFilter
}: ActiveFiltersProps) {
  const activeFilters = filters.filter(filter => filter.value && filter.value !== 'all')
  
  if (!searchValue && activeFilters.length === 0) {
    return null
  }

  return (
    <div className="flex flex-wrap gap-2 p-4 bg-gray-50 border-b">
      <span className="text-sm font-medium text-gray-700">Active filters:</span>
      
      {searchValue && (
        <Badge variant="secondary" className="flex items-center gap-1">
          Search: "{searchValue}"
          <X 
            className="h-3 w-3 cursor-pointer hover:text-red-500" 
            onClick={onRemoveSearch}
          />
        </Badge>
      )}
      
      {activeFilters.map((filter) => {
        const selectedOption = filter.options.find(opt => opt.value === filter.value)
        return (
          <Badge key={filter.key} variant="secondary" className="flex items-center gap-1">
            {filter.label}: {selectedOption?.label}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => onRemoveFilter(filter.key)}
            />
          </Badge>
        )
      })}
    </div>
  )
}
