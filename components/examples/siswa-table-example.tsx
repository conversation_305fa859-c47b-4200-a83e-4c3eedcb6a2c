'use client'

import { useState, useMemo, useEffect } from 'react'
import { Plus, Download, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { DataTableToolbar, ActiveFilters } from '@/components/ui/data-table-toolbar'
import { useExport } from '@/hooks/use-export'
import { SiswaService, type Siswa } from '@/lib/services'
import { toast } from '@/hooks/use-toast'

export function SiswaTableExample() {
  const [siswaData, setSiswaData] = useState<Siswa[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [genderFilter, setGenderFilter] = useState('all')
  const [educationFilter, setEducationFilter] = useState('all')

  const { isExporting, exportToCSV, exportToJSON } = useExport()

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const data = await SiswaService.getAll()
        setSiswaData(data)
      } catch (error) {
        console.error('Error fetching data:', error)
        toast({
          title: "Error",
          description: "Gagal memuat data siswa",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Filter data
  const filteredData = useMemo(() => {
    let filtered = siswaData

    if (searchTerm) {
      filtered = filtered.filter(siswa =>
        siswa.nama_lengkap.toLowerCase().includes(searchTerm.toLowerCase()) ||
        siswa.nik.includes(searchTerm) ||
        siswa.email?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(siswa => siswa.status_pendaftaran === statusFilter)
    }

    if (genderFilter !== 'all') {
      filtered = filtered.filter(siswa => siswa.jenis_kelamin === genderFilter)
    }

    if (educationFilter !== 'all') {
      filtered = filtered.filter(siswa => siswa.pendidikan_terakhir === educationFilter)
    }

    return filtered
  }, [siswaData, searchTerm, statusFilter, genderFilter, educationFilter])

  // Table columns configuration
  const columns = [
    {
      key: 'nama_lengkap',
      header: 'Informasi Siswa',
      cell: (siswa: Siswa) => (
        <div>
          <div className="font-medium text-gray-900">{siswa.nama_lengkap}</div>
          <div className="text-sm text-gray-500">NIK: {siswa.nik}</div>
          <div className="text-xs text-gray-400 mt-1">
            <Badge variant="outline" className="text-xs">
              {siswa.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan'}
            </Badge>
            <span className="ml-2 text-gray-500">
              {new Date().getFullYear() - new Date(siswa.tanggal_lahir).getFullYear()} tahun
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'kontak',
      header: 'Kontak',
      cell: (siswa: Siswa) => (
        <div className="text-sm">
          <div>{siswa.nomor_telepon}</div>
          {siswa.email && <div className="text-gray-500">{siswa.email}</div>}
        </div>
      )
    },
    {
      key: 'pendidikan',
      header: 'Pendidikan',
      cell: (siswa: Siswa) => (
        <div className="text-sm">
          <div className="font-medium">{siswa.pendidikan_terakhir}</div>
          {siswa.jurusan && <div className="text-gray-500">{siswa.jurusan}</div>}
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      cell: (siswa: Siswa) => {
        const statusConfig = {
          draft: { label: 'Draft', variant: 'secondary' as const },
          submitted: { label: 'Submitted', variant: 'default' as const },
          review: { label: 'Review', variant: 'default' as const },
          approved: { label: 'Approved', variant: 'default' as const },
          rejected: { label: 'Rejected', variant: 'destructive' as const }
        }
        
        const config = statusConfig[siswa.status_pendaftaran as keyof typeof statusConfig] || 
                      { label: siswa.status_pendaftaran, variant: 'secondary' as const }
        
        return <Badge variant={config.variant}>{config.label}</Badge>
      }
    }
  ]

  // Filter configurations
  const filterConfigs = [
    {
      key: 'status',
      label: 'Status',
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { value: 'all', label: 'Semua Status' },
        { value: 'draft', label: 'Draft' },
        { value: 'submitted', label: 'Submitted' },
        { value: 'review', label: 'Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'rejected', label: 'Rejected' }
      ]
    },
    {
      key: 'gender',
      label: 'Jenis Kelamin',
      value: genderFilter,
      onChange: setGenderFilter,
      options: [
        { value: 'all', label: 'Semua' },
        { value: 'L', label: 'Laki-laki' },
        { value: 'P', label: 'Perempuan' }
      ]
    },
    {
      key: 'education',
      label: 'Pendidikan',
      value: educationFilter,
      onChange: setEducationFilter,
      options: [
        { value: 'all', label: 'Semua Pendidikan' },
        { value: 'SMA', label: 'SMA' },
        { value: 'SMK', label: 'SMK' },
        { value: 'D3', label: 'D3' },
        { value: 'S1', label: 'S1' }
      ]
    }
  ]

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('all')
    setGenderFilter('all')
    setEducationFilter('all')
  }

  // Export handlers
  const handleExportCSV = async () => {
    try {
      await exportToCSV(filteredData, {
        filename: `siswa_export_${new Date().toISOString().split('T')[0]}.csv`
      })
      toast({
        title: "Export Berhasil",
        description: "Data siswa berhasil diekspor ke CSV"
      })
    } catch (error) {
      toast({
        title: "Export Gagal",
        description: "Terjadi kesalahan saat mengekspor data",
        variant: "destructive"
      })
    }
  }

  const handleExportJSON = async () => {
    try {
      await exportToJSON(filteredData, {
        filename: `siswa_export_${new Date().toISOString().split('T')[0]}.json`
      })
      toast({
        title: "Export Berhasil",
        description: "Data siswa berhasil diekspor ke JSON"
      })
    } catch (error) {
      toast({
        title: "Export Gagal",
        description: "Terjadi kesalahan saat mengekspor data",
        variant: "destructive"
      })
    }
  }

  // Header actions
  const headerActions = (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleExportCSV}
        disabled={isExporting || filteredData.length === 0}
      >
        <Download className="h-4 w-4 mr-2" />
        Export CSV
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={handleExportJSON}
        disabled={isExporting || filteredData.length === 0}
      >
        <FileText className="h-4 w-4 mr-2" />
        Export JSON
      </Button>
      <Button size="sm">
        <Plus className="h-4 w-4 mr-2" />
        Tambah Siswa
      </Button>
    </>
  )

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <DataTableToolbar
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Cari nama, NIK, atau email..."
        filters={filterConfigs}
        actions={headerActions}
        showClearFilters={true}
        onClearFilters={clearFilters}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchValue={searchTerm}
        filters={filterConfigs}
        onRemoveSearch={() => setSearchTerm('')}
        onRemoveFilter={(key) => {
          switch (key) {
            case 'status':
              setStatusFilter('all')
              break
            case 'gender':
              setGenderFilter('all')
              break
            case 'education':
              setEducationFilter('all')
              break
          }
        }}
      />

      {/* Data Table */}
      <DataTable
        data={filteredData}
        columns={columns}
        title="Data Siswa"
        description={`Total ${filteredData.length} siswa dari ${siswaData.length} siswa`}
        isLoading={loading}
        itemsPerPage={10}
        enableKeyboardNavigation={true}
        emptyMessage="Tidak ada data siswa yang ditemukan"
      />
    </div>
  )
}
