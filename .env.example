# ==============================================
# Environment Variables Template
# Dashboard Sistem Magang Jepang
# ==============================================

# Application Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Dashboard Sistem Magang Jepang"

# ==============================================
# Supabase Configuration
# ==============================================

# Supabase Project Settings
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Database Connection (PostgreSQL via Supabase)
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"

# Supabase Auth Settings
SUPABASE_JWT_SECRET="your-jwt-secret"

# Database Pool Settings (optional)
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# ==============================================
# Authentication & Security
# ==============================================

# NextAuth.js Configuration
NEXTAUTH_SECRET="your-super-secret-key-minimum-32-characters-long"
NEXTAUTH_URL="http://localhost:3000"

# JWT Settings
JWT_SECRET="your-jwt-secret-key"
JWT_EXPIRES_IN="7d"

# Session Settings
SESSION_MAX_AGE=604800  # 7 days in seconds

# ==============================================
# Email Configuration
# ==============================================

# SMTP Settings
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Email Templates
EMAIL_TEMPLATE_DIR="./templates/email"

# ==============================================
# File Upload Configuration
# ==============================================

# Upload Limits
UPLOAD_MAX_SIZE=5242880  # 5MB in bytes
UPLOAD_ALLOWED_TYPES="pdf,jpg,jpeg,png,doc,docx,xls,xlsx"

# Storage Configuration
# Options: supabase, s3, cloudinary, local
STORAGE_PROVIDER="supabase"

# Supabase Storage (Recommended)
SUPABASE_STORAGE_BUCKET="documents"
SUPABASE_STORAGE_URL="https://your-project-ref.supabase.co/storage/v1"

# AWS S3 (alternative)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="ap-southeast-1"
AWS_S3_BUCKET="magang-jepang-files"

# Cloudinary (alternative)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Local Storage (development only)
STORAGE_PATH="./uploads"

# ==============================================
# External APIs
# ==============================================

# Google Maps API (for location features)
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Google Analytics (optional)
NEXT_PUBLIC_GA_TRACKING_ID="GA-XXXXXXXXX"

# ==============================================
# Rate Limiting
# ==============================================

# API Rate Limits (requests per hour)
RATE_LIMIT_AUTHENTICATED=1000
RATE_LIMIT_UNAUTHENTICATED=100
RATE_LIMIT_UPLOAD=50

# ==============================================
# Logging & Monitoring
# ==============================================

# Log Level (error, warn, info, debug)
LOG_LEVEL="info"

# Sentry Error Tracking (optional)
SENTRY_DSN="your-sentry-dsn"
SENTRY_ENVIRONMENT="development"

# ==============================================
# Cache Configuration
# ==============================================

# Redis Cache (optional)
REDIS_URL="redis://localhost:6379"
CACHE_TTL=3600  # 1 hour in seconds

# ==============================================
# Backup Configuration
# ==============================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Backup Storage
BACKUP_STORAGE_PATH="./backups"

# ==============================================
# Development Settings
# ==============================================

# Debug Mode
DEBUG=false
NEXT_PUBLIC_DEBUG=false

# API Base URL (for external integrations)
API_BASE_URL="http://localhost:3000/api"

# ==============================================
# Production Settings
# ==============================================

# SSL/HTTPS
FORCE_HTTPS=false

# CORS Settings
CORS_ORIGIN="http://localhost:3000"

# Security Headers
SECURITY_HEADERS_ENABLED=true

# ==============================================
# Feature Flags
# ==============================================

# Enable/Disable Features
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_FILE_UPLOAD=true
FEATURE_EXPORT_EXCEL=true
FEATURE_IMPORT_EXCEL=true
FEATURE_ADVANCED_SEARCH=true
FEATURE_REAL_TIME_UPDATES=false

# ==============================================
# Notification Settings
# ==============================================

# Push Notifications (optional)
VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"
VAPID_SUBJECT="mailto:<EMAIL>"

# SMS Notifications (optional)
SMS_PROVIDER="twilio"  # twilio, nexmo, etc.
SMS_API_KEY="********-api-key"
SMS_API_SECRET="********-api-secret"
SMS_FROM_NUMBER="+**********"

# ==============================================
# Integration Settings
# ==============================================

# Government API Integration (optional)
DUKCAPIL_API_URL="https://api.dukcapil.go.id"
DUKCAPIL_API_KEY="your-dukcapil-api-key"

# Bank API Integration (optional)
BANK_API_URL="https://api.bank.co.id"
BANK_API_KEY="your-bank-api-key"

# ==============================================
# Localization
# ==============================================

# Default Language
DEFAULT_LOCALE="id"
SUPPORTED_LOCALES="id,en,ja"

# Timezone
DEFAULT_TIMEZONE="Asia/Jakarta"

# ==============================================
# Performance Settings
# ==============================================

# Next.js Performance
NEXT_PUBLIC_BUNDLE_ANALYZER=false

# Database Query Timeout
DATABASE_QUERY_TIMEOUT=30000  # 30 seconds

# API Response Timeout
API_TIMEOUT=10000  # 10 seconds

# ==============================================
# Testing Configuration
# ==============================================

# Test Database
TEST_DATABASE_URL="mysql://test_user:test_password@localhost:3306/test_magang_jepang"

# Test Environment
TEST_ENV=true

# ==============================================
# Custom Application Settings
# ==============================================

# Application Specific
MAX_STUDENTS_PER_LPK=1000
MAX_JOB_ORDERS_PER_COMPANY=50
DEFAULT_TRAINING_DURATION_MONTHS=3

# Business Rules
MIN_AGE_REQUIREMENT=18
MAX_AGE_REQUIREMENT=35
REQUIRED_DOCUMENTS_COUNT=8

# System Maintenance
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="Sistem sedang dalam pemeliharaan. Silakan coba lagi nanti."

# ==============================================
# Notes:
# ==============================================
# 1. Copy this file to .env.local for development
# 2. Never commit actual credentials to version control
# 3. Use strong, unique passwords and secrets
# 4. Update NEXTAUTH_SECRET and JWT_SECRET with random strings
# 5. Configure database connection string properly
# 6. Set appropriate file upload limits based on your server capacity
# 7. Enable features based on your requirements
# ==============================================
