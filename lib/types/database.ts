// Database Types for Dashboard Magang Jepang
// Generated from Supabase schema

export type UserRole = 'admin' | 'operator' | 'lpk_admin' | 'viewer'
export type GenderType = 'L' | 'P'
export type EducationLevel = 'SD' | 'SMP' | 'SMA' | 'SMK' | 'D3' | 'S1'
export type StatusType = 'aktif' | 'nonaktif' | 'suspended'
export type RegistrationStatus = 'draft' | 'submitted' | 'review' | 'approved' | 'rejected'
export type PlacementStatus = 'ditempatkan' | 'berangkat' | 'aktif' | 'selesai' | 'dibatalkan'
export type JobStatus = 'draft' | 'published' | 'closed' | 'cancelled'
export type DocumentStatus = 'pending' | 'approved' | 'rejected'

// New types for enhanced siswa data
export type MaritalStatus = 'lajang' | 'menikah' | 'bercerai' | 'duda_janda'
export type BloodType = 'A' | 'B' | 'AB' | 'O'
export type AvailabilityStatus = 'siap' | 'belum_siap' | 'kondisional'
export type VerificationStatus = 'belum_diverifikasi' | 'sedang_diverifikasi' | 'terverifikasi' | 'ditolak'
export type RelationshipType = 'ayah' | 'ibu' | 'saudara' | 'pasangan' | 'anak' | 'lainnya'
export type AttachmentType = 'foto_profil' | 'ktp' | 'kartu_keluarga' | 'ijazah' | 'sertifikat' | 'cv' | 'dokumen_lain'

// LPK Mitra
export interface LpkMitra {
  id: string
  nama_lpk: string
  alamat_lengkap: string
  kota: string
  provinsi: string
  nama_pimpinan: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  tanggal_kerjasama?: string
  catatan?: string
  created_at: string
  updated_at: string
}

// Kumiai
export interface Kumiai {
  id: string
  nama_kumiai: string
  kode_kumiai: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  keterangan?: string
  created_at: string
  updated_at: string
}

// Perusahaan Penerima
export interface PerusahaanPenerima {
  id: string
  kumiai_id: string
  nama_perusahaan: string
  alamat_jepang: string
  kota_jepang: string
  prefektur: string
  bidang_usaha: string
  kontak_person: string
  nomor_telepon: string
  email: string
  website?: string
  status: StatusType
  keterangan?: string
  created_at: string
  updated_at: string
}

// Job Order
export interface JobOrder {
  id: string
  perusahaan_id: string
  kumiai_id: string
  judul_pekerjaan: string
  deskripsi_pekerjaan: string
  posisi: string
  bidang_kerja: string
  jenis_kelamin: string
  usia_min: number
  usia_max: number
  pendidikan_min: EducationLevel
  pengalaman_kerja?: string
  keahlian_khusus?: string
  gaji_pokok: number
  tunjangan: number
  jam_kerja_per_hari: number
  hari_kerja_per_minggu: number
  overtime_available: boolean
  akomodasi?: string
  transportasi?: string
  asuransi?: string
  fasilitas_lain?: string
  jumlah_kuota: number
  kuota_terisi: number
  status: JobStatus
  tanggal_buka: string
  tanggal_tutup: string
  created_at: string
  updated_at: string
}

// Enhanced Siswa Interface with all new fields
export interface Siswa {
  // Basic Info
  id: string
  lpk_id: string
  nama_lengkap: string
  nama_lengkap_jepang?: string // 氏名
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: GenderType
  agama: string
  status_pernikahan: MaritalStatus
  
  // Address Info
  alamat_lengkap: string
  kelurahan: string
  kecamatan: string
  kota_kabupaten: string
  provinsi: string
  kode_pos?: string
  
  // Location Coordinates
  latitude?: number
  longitude?: number
  alamat_koordinat?: string
  
  // Contact Info
  nomor_hp: string
  email?: string
  nomor_wa?: string
  
  // Physical Info
  tinggi_badan?: number // cm
  berat_badan?: number // kg
  ukuran_sepatu?: number
  ukuran_baju?: string
  ukuran_celana?: number
  lingkar_kepala?: number // cm
  lingkar_pinggang?: number // cm
  golongan_darah?: BloodType
  riwayat_penyakit?: string
  alergi?: string
  
  // Education Info
  pendidikan_terakhir: EducationLevel
  nama_sekolah?: string
  tahun_lulus?: number
  jurusan?: string
  ipk?: number
  sertifikat_keahlian?: string
  
  // Family Info
  nama_ayah?: string
  nama_ibu?: string
  alamat_keluarga?: string
  nomor_hp_keluarga?: string
  pekerjaan_ayah?: string
  pekerjaan_ibu?: string
  
  // Japanese-specific Info
  hobi?: string
  bakat_khusus?: string
  minat_kerja?: string
  pengalaman_organisasi?: string
  tujuan_ke_jepang?: string
  target_kerja_jepang?: string
  rencana_setelah_jepang?: string
  
  // LPK Info
  tanggal_masuk_lpk?: string
  lama_belajar_bulan?: number
  level_bahasa_jepang?: string
  sertifikat_bahasa?: string
  nilai_ujian_masuk?: number
  
  // Availability & Status
  ketersediaan: AvailabilityStatus
  tanggal_ketersediaan?: string
  keterangan_ketersediaan?: string
  
  // Verification & Profile
  status_verifikasi: VerificationStatus
  verified_by?: string
  verified_at?: string
  catatan_verifikasi?: string
  profile_completeness: number // 0-100%
  
  // Registration Info
  status_pendaftaran: RegistrationStatus
  tanggal_daftar: string
  catatan?: string
  
  // System fields
  created_at: string
  updated_at: string
}

// Siswa Education History
export interface SiswaPendidikan {
  id: string
  siswa_id: string
  jenjang: EducationLevel
  nama_sekolah: string
  jurusan?: string
  tahun_masuk: number
  tahun_lulus: number
  ipk?: number
  keterangan?: string
  created_at: string
  updated_at: string
}

// Siswa Work Experience
export interface SiswaPengalamanKerja {
  id: string
  siswa_id: string
  nama_perusahaan: string
  posisi: string
  tahun_mulai: number
  tahun_selesai?: number
  deskripsi_pekerjaan?: string
  gaji?: number
  alasan_berhenti?: string
  created_at: string
  updated_at: string
}

// Siswa Family Members
export interface SiswaKeluarga {
  id: string
  siswa_id: string
  nama_lengkap: string
  hubungan: RelationshipType
  tanggal_lahir?: string
  pekerjaan?: string
  nomor_hp?: string
  alamat?: string
  created_at: string
  updated_at: string
}

// Siswa Attachments/Documents
export interface SiswaAttachment {
  id: string
  siswa_id: string
  nama_file: string
  jenis_dokumen: AttachmentType
  file_path: string
  file_size?: number
  mime_type?: string
  uploaded_by: string
  keterangan?: string
  created_at: string
  updated_at: string
}

// Social Media
export interface SiswaSocialMedia {
  id: string
  siswa_id: string
  platform: string
  username: string
  url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Penempatan Siswa
export interface PenempatanSiswa {
  id: string
  siswa_id: string
  job_order_id: string
  perusahaan_id: string
  kumiai_id: string
  tanggal_penempatan: string
  tanggal_keberangkatan?: string
  tanggal_kepulangan?: string
  status_penempatan: PlacementStatus
  posisi_kerja: string
  gaji_aktual: number
  alamat_kerja: string
  evaluasi_bulanan?: string
  catatan_khusus?: string
  created_at: string
  updated_at: string
}

// Enhanced Database Tables Type
export interface Database {
  public: {
    Tables: {
      lpk_mitra: {
        Row: LpkMitra
        Insert: Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>>
      }
      kumiai: {
        Row: Kumiai
        Insert: Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>>
      }
      perusahaan_penerima: {
        Row: PerusahaanPenerima
        Insert: Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>>
      }
      job_order: {
        Row: JobOrder
        Insert: Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa: {
        Row: Siswa
        Insert: Omit<Siswa, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Siswa, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa_pendidikan: {
        Row: SiswaPendidikan
        Insert: Omit<SiswaPendidikan, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SiswaPendidikan, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa_pengalaman_kerja: {
        Row: SiswaPengalamanKerja
        Insert: Omit<SiswaPengalamanKerja, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SiswaPengalamanKerja, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa_keluarga: {
        Row: SiswaKeluarga
        Insert: Omit<SiswaKeluarga, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SiswaKeluarga, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa_attachments: {
        Row: SiswaAttachment
        Insert: Omit<SiswaAttachment, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SiswaAttachment, 'id' | 'created_at' | 'updated_at'>>
      }
      siswa_social_media: {
        Row: SiswaSocialMedia
        Insert: Omit<SiswaSocialMedia, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SiswaSocialMedia, 'id' | 'created_at' | 'updated_at'>>
      }
      penempatan_siswa: {
        Row: PenempatanSiswa
        Insert: Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}

// Complex data structures for UI components
export interface SiswaWithRelations extends Siswa {
  lpk_mitra?: LpkMitra
  pendidikan?: SiswaPendidikan[]
  pengalaman_kerja?: SiswaPengalamanKerja[]
  keluarga?: SiswaKeluarga[]
  attachments?: SiswaAttachment[]
  social_media?: SiswaSocialMedia[]
  penempatan?: PenempatanSiswa[]
}

// Form data structures
export interface SiswaFormData {
  // Basic Info
  nama_lengkap: string
  nama_lengkap_jepang?: string
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: GenderType
  agama: string
  status_pernikahan: MaritalStatus
  
  // Contact & Address
  alamat_lengkap: string
  kelurahan: string
  kecamatan: string
  kota_kabupaten: string
  provinsi: string
  kode_pos?: string
  nomor_hp: string
  email?: string
  nomor_wa?: string
  
  // Location Coordinates
  latitude?: number
  longitude?: number
  alamat_koordinat?: string
  
  // Physical Info
  tinggi_badan?: number
  berat_badan?: number
  ukuran_sepatu?: number
  ukuran_baju?: string
  ukuran_celana?: number
  lingkar_kepala?: number
  lingkar_pinggang?: number
  golongan_darah?: BloodType
  riwayat_penyakit?: string
  alergi?: string
  
  // Education
  pendidikan_terakhir: EducationLevel
  nama_sekolah?: string
  tahun_lulus?: number
  jurusan?: string
  ipk?: number
  sertifikat_keahlian?: string
  
  // Family
  nama_ayah?: string
  nama_ibu?: string
  alamat_keluarga?: string
  nomor_hp_keluarga?: string
  pekerjaan_ayah?: string
  pekerjaan_ibu?: string
  
  // Japanese-specific
  hobi?: string
  bakat_khusus?: string
  minat_kerja?: string
  pengalaman_organisasi?: string
  tujuan_ke_jepang?: string
  target_kerja_jepang?: string
  rencana_setelah_jepang?: string
  
  // LPK
  lpk_id: string
  tanggal_masuk_lpk?: string
  lama_belajar_bulan?: number
  level_bahasa_jepang?: string
  sertifikat_bahasa?: string
  nilai_ujian_masuk?: number
  
  // Availability
  ketersediaan: AvailabilityStatus
  tanggal_ketersediaan?: string
  keterangan_ketersediaan?: string
  
  // Related data
  pendidikan?: Omit<SiswaPendidikan, 'id' | 'siswa_id' | 'created_at' | 'updated_at'>[]
  pengalaman_kerja?: Omit<SiswaPengalamanKerja, 'id' | 'siswa_id' | 'created_at' | 'updated_at'>[]
  keluarga?: Omit<SiswaKeluarga, 'id' | 'siswa_id' | 'created_at' | 'updated_at'>[]
  social_media?: Omit<SiswaSocialMedia, 'id' | 'siswa_id' | 'created_at' | 'updated_at'>[]
}

// Filter and search interfaces
export interface SiswaFilterParams {
  search?: string
  lpk_id?: string
  jenis_kelamin?: GenderType
  status_pendaftaran?: RegistrationStatus
  status_verifikasi?: VerificationStatus
  ketersediaan?: AvailabilityStatus
  pendidikan_min?: EducationLevel
  usia_min?: number
  usia_max?: number
  prefektur?: string
  level_bahasa?: string
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// Statistics interfaces
export interface SiswaStats {
  total: number
  by_status_pendaftaran: Record<RegistrationStatus, number>
  by_status_verifikasi: Record<VerificationStatus, number>
  by_ketersediaan: Record<AvailabilityStatus, number>
  by_jenis_kelamin: Record<GenderType, number>
  by_lpk: Array<{ lpk_id: string; nama_lpk: string; count: number }>
  avg_profile_completeness: number
  recent_registrations: number // last 30 days
}
