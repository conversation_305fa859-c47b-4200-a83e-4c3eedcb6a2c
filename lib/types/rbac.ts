// RBAC Types for Dashboard Magang Jepang
// Role-Based Access Control System Types

export type UserRole = 
  | 'administrator'
  | 'lpk_mitra' 
  | 'divisi_education'
  | 'divisi_recruitment' 
  | 'pemberangkatan'
  | 'pimpinan'

export type PermissionAction = 'create' | 'read' | 'update' | 'delete'

export type PermissionModule = 
  | 'dashboard'
  | 'siswa'
  | 'lpk_mitra'
  | 'job_order'
  | 'kumiai'
  | 'pendidikan'
  | 'penempatan'
  | 'dokumen'
  | 'pendaftaran'
  | 'users'
  | 'roles'
  | 'reports'

// Database Types
export interface Role {
  id: string
  name: UserRole
  display_name: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Permission {
  id: string
  name: string
  display_name: string
  description?: string
  module: PermissionModule
  action: PermissionAction
  created_at: string
}

export interface RolePermission {
  id: string
  role_id: string
  permission_id: string
  created_at: string
  role?: Role
  permission?: Permission
}

export interface UserRoleAssignment {
  id: string
  user_id: string
  role_id: string
  assigned_by?: string
  lpk_mitra_id?: string
  is_active: boolean
  assigned_at: string
  expires_at?: string
  role?: Role
  lpk_mitra?: {
    id: string
    nama_lpk: string
  }
}

export interface UserProfile {
  id: string
  username?: string
  full_name: string
  email?: string
  phone?: string
  position?: string
  department?: string
  lpk_mitra_id?: string
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
  role_assignments?: UserRoleAssignment[]
  lpk_mitra?: {
    id: string
    nama_lpk: string
  }
}

// API Request/Response Types
export interface CreateUserRequest {
  email: string
  password: string
  full_name: string
  username?: string
  phone?: string
  position?: string
  department?: string
  roles: string[] // role names
  lpk_mitra_id?: string
}

export interface UpdateUserRequest {
  full_name?: string
  username?: string
  phone?: string
  position?: string
  department?: string
  is_active?: boolean
  roles?: string[] // role names
  lpk_mitra_id?: string
}

export interface CreateRoleRequest {
  name: string
  display_name: string
  description?: string
  permissions: string[] // permission names
}

export interface UpdateRoleRequest {
  display_name?: string
  description?: string
  is_active?: boolean
  permissions?: string[] // permission names
}

export interface AssignRoleRequest {
  user_id: string
  role_name: string
  lpk_mitra_id?: string
  expires_at?: string
}

// View Types (from database views)
export interface UserRoleView {
  id: string
  username?: string
  full_name: string
  email?: string
  role_name?: string
  role_display_name?: string
  role_active?: boolean
  assigned_at?: string
  expires_at?: string
  lpk_name?: string
}

export interface RolePermissionView {
  role_name: string
  role_display_name: string
  permission_name: string
  permission_display_name: string
  module: PermissionModule
  action: PermissionAction
}

export interface UserPermissionView {
  user_id: string
  username?: string
  full_name: string
  permission_name: string
  permission_display_name: string
  module: PermissionModule
  action: PermissionAction
  role_name: string
}

// Utility Types
export interface UserPermissions {
  [module: string]: {
    [action: string]: boolean
  }
}

export interface RolePermissions {
  [permission_name: string]: boolean
}

// Filter and Pagination Types
export interface UserFilterParams {
  search?: string
  role?: string
  is_active?: boolean
  lpk_mitra_id?: string
  page?: number
  limit?: number
}

export interface RoleFilterParams {
  search?: string
  is_active?: boolean
  page?: number
  limit?: number
}

// Response Types
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

// Permission Check Types
export interface PermissionCheck {
  module: PermissionModule
  action: PermissionAction
}

export interface AccessControlContext {
  user_id: string
  roles: string[]
  permissions: string[]
  lpk_mitra_id?: string
}

// Constants
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  administrator: 'Akses penuh ke semua fitur dan data sistem',
  lpk_mitra: 'Akses terbatas hanya ke data yang diinputkan oleh LPK sendiri',
  divisi_education: 'Akses ke menu dan data pendidikan',
  divisi_recruitment: 'Akses ke menu dan data recruitment',
  pemberangkatan: 'Akses ke menu pemberkasan dan pemberangkatan',
  pimpinan: 'Akses read-only ke semua laporan dan data untuk monitoring'
}

export const MODULE_NAMES: Record<PermissionModule, string> = {
  dashboard: 'Dashboard',
  siswa: 'Data Siswa',
  lpk_mitra: 'LPK Mitra',
  job_order: 'Job Order',
  kumiai: 'Kumiai',
  pendidikan: 'Pendidikan',
  penempatan: 'Penempatan',
  dokumen: 'Dokumen',
  pendaftaran: 'Pendaftaran',
  users: 'User Management',
  roles: 'Role Management',
  reports: 'Laporan'
}

export const ACTION_NAMES: Record<PermissionAction, string> = {
  create: 'Tambah',
  read: 'Lihat',
  update: 'Edit',
  delete: 'Hapus'
}
