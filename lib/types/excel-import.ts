// Excel Import Types and Interfaces

export interface ExcelRowData {
  // Personal Data
  nama_lengkap: string
  nik: string
  jenis_kelamin: 'L' | 'P'
  tempat_lahir: string
  tanggal_lahir: string | Date
  agama: string
  status_pernikahan?: string
  
  // Address
  alamat_lengkap: string
  kelurahan: string
  kecamatan: string
  kota_kabupaten: string
  provinsi: string
  kode_pos?: string
  
  // Contact
  nomor_hp: string
  email?: string
  
  // Education
  pendidikan_terakhir: 'SD' | 'SMP' | 'SMA' | 'SMK' | 'D3' | 'S1'
  nama_sekolah: string
  tahun_lulus: number
  jurusan?: string
  
  // Family
  nama_ayah?: string
  nama_ibu?: string
  alamat_keluarga?: string
  nomor_hp_keluarga?: string
  pekerjaan_ayah?: string
  pekerjaan_ibu?: string
  penghasilan_orangtua?: string
  
  // LPK Information (bisa auto-create jika belum ada)
  nama_lpk: string
  alamat_lpk?: string
  kota_lpk?: string
  provinsi_lpk?: string
  kontak_person_lpk?: string
  nomor_telepon_lpk?: string
  email_lpk?: string
  
  // Kumiai Information (bisa auto-create jika belum ada)
  nama_kumiai?: string
  kode_kumiai?: string
  alamat_jepang?: string
  kota_jepang?: string
  prefektur?: string
  kontak_person_kumiai?: string
  nomor_telepon_kumiai?: string
  email_kumiai?: string
  
  // Perusahaan Information (bisa auto-create jika belum ada)
  nama_perusahaan?: string
  alamat_perusahaan?: string
  kota_perusahaan?: string
  prefektur_perusahaan?: string
  bidang_usaha?: string
  kontak_person_perusahaan?: string
  nomor_telepon_perusahaan?: string
  email_perusahaan?: string
  
  // Placement Information (optional)
  tanggal_penempatan?: string | Date
  tanggal_keberangkatan?: string | Date
  tanggal_kepulangan?: string | Date
  status_penempatan?: 'ditempatkan' | 'berangkat' | 'aktif' | 'selesai' | 'dibatalkan'
  posisi_kerja?: string
  gaji_aktual?: number
  
  // Status
  status_pendaftaran?: 'draft' | 'submitted' | 'review' | 'approved' | 'rejected'
  tanggal_daftar?: string | Date
  catatan?: string
}

export interface ExcelColumnMapping {
  [excelColumn: string]: keyof ExcelRowData
}

export interface ImportValidationError {
  row: number
  column: string
  field: keyof ExcelRowData
  value: any
  error: string
  severity: 'error' | 'warning'
}

export interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: ImportValidationError[]
  warnings: ImportValidationError[]
  createdEntities: {
    lpk: number
    kumiai: number
    perusahaan: number
    siswa: number
    penempatan: number
  }
  message: string
}

export interface EntityAutoCreateResult {
  id: string
  name: string
  isNew: boolean
}

export interface ImportPreviewData {
  headers: string[]
  data: ExcelRowData[]
  mapping: ExcelColumnMapping
  errors: ImportValidationError[]
  statistics: {
    totalRows: number
    validRows: number
    errorRows: number
    warningRows: number
  }
}

export interface ImportProcessingStatus {
  status: 'idle' | 'processing' | 'completed' | 'error'
  progress: number
  currentStep: string
  processedRows: number
  totalRows: number
  errors: ImportValidationError[]
}

// Default column mappings untuk berbagai format Excel yang umum
export const DEFAULT_COLUMN_MAPPINGS: { [key: string]: ExcelColumnMapping } = {
  // Format 1: Standar Indonesia
  standard: {
    'Nama Lengkap': 'nama_lengkap',
    'NIK': 'nik',
    'Jenis Kelamin': 'jenis_kelamin',
    'Tempat Lahir': 'tempat_lahir',
    'Tanggal Lahir': 'tanggal_lahir',
    'Agama': 'agama',
    'Status Pernikahan': 'status_pernikahan',
    'Alamat Lengkap': 'alamat_lengkap',
    'Kelurahan': 'kelurahan',
    'Kecamatan': 'kecamatan',
    'Kota/Kabupaten': 'kota_kabupaten',
    'Provinsi': 'provinsi',
    'Kode Pos': 'kode_pos',
    'Nomor HP': 'nomor_hp',
    'Email': 'email',
    'Pendidikan Terakhir': 'pendidikan_terakhir',
    'Nama Sekolah': 'nama_sekolah',
    'Tahun Lulus': 'tahun_lulus',
    'Jurusan': 'jurusan',
    'Nama Ayah': 'nama_ayah',
    'Nama Ibu': 'nama_ibu',
    'Alamat Keluarga': 'alamat_keluarga',
    'Nomor HP Keluarga': 'nomor_hp_keluarga',
    // Template headers (yang digunakan dalam template Excel)
    'nama_lengkap': 'nama_lengkap',
    'nik': 'nik',
    'tanggal_lahir': 'tanggal_lahir',
    'tempat_lahir': 'tempat_lahir',
    'jenis_kelamin': 'jenis_kelamin',
    'agama': 'agama',
    'alamat': 'alamat_lengkap',
    'provinsi': 'provinsi',
    'kota_kabupaten': 'kota_kabupaten',
    'kecamatan': 'kecamatan',
    'kelurahan': 'kelurahan',
    'kode_pos': 'kode_pos',
    'nomor_telepon': 'nomor_hp',
    'email': 'email',
    'pendidikan_terakhir': 'pendidikan_terakhir',
    'nama_sekolah': 'nama_sekolah',
    'jurusan': 'jurusan',
    'tahun_lulus': 'tahun_lulus',
    'nama_ayah': 'nama_ayah',
    'nama_ibu': 'nama_ibu',
    'nomor_telepon_orangtua': 'nomor_hp_keluarga',
    'alamat_orangtua': 'alamat_keluarga',
    'pekerjaan_ayah': 'pekerjaan_ayah',
    'pekerjaan_ibu': 'pekerjaan_ibu',
    'penghasilan_orangtua': 'penghasilan_orangtua',
    'nama_lpk': 'nama_lpk',
    'alamat_lpk': 'alamat_lpk',
    'nomor_telepon_lpk': 'nomor_telepon_lpk',
    'nama_kumiai': 'nama_kumiai',
    'alamat_kumiai': 'alamat_jepang',
    'nomor_telepon_kumiai': 'nomor_telepon_kumiai',
    'nama_perusahaan': 'nama_perusahaan',
    'alamat_perusahaan': 'alamat_perusahaan',
    'nomor_telepon_perusahaan': 'nomor_telepon_perusahaan',
    'prefecture': 'prefektur',
    'kota_penempatan': 'kota_jepang',
    'tanggal_mulai_penempatan': 'tanggal_penempatan',
    'status_penempatan': 'status_penempatan',
    
    // Alternative headers (Indonesian)
    'Nama LPK': 'nama_lpk',
    'Alamat LPK': 'alamat_lpk',
    'Kota LPK': 'kota_lpk',
    'Provinsi LPK': 'provinsi_lpk',
    'Kontak Person LPK': 'kontak_person_lpk',
    'Nomor Telepon LPK': 'nomor_telepon_lpk',
    'Email LPK': 'email_lpk',
    'Nama Kumiai': 'nama_kumiai',
    'Kode Kumiai': 'kode_kumiai',
    'Alamat Jepang': 'alamat_jepang',
    'Kota Jepang': 'kota_jepang',
    'Prefektur': 'prefektur',
    'Kontak Person Kumiai': 'kontak_person_kumiai',
    'Nomor Telepon Kumiai': 'nomor_telepon_kumiai',
    'Email Kumiai': 'email_kumiai',
    'Nama Perusahaan': 'nama_perusahaan',
    'Alamat Perusahaan': 'alamat_perusahaan',
    'Kota Perusahaan': 'kota_perusahaan',
    'Prefektur Perusahaan': 'prefektur_perusahaan',
    'Bidang Usaha': 'bidang_usaha',
    'Kontak Person Perusahaan': 'kontak_person_perusahaan',
    'Nomor Telepon Perusahaan': 'nomor_telepon_perusahaan',
    'Email Perusahaan': 'email_perusahaan',
    'Tanggal Penempatan': 'tanggal_penempatan',
    'Tanggal Keberangkatan': 'tanggal_keberangkatan',
    'Tanggal Kepulangan': 'tanggal_kepulangan',
    'Status Penempatan': 'status_penempatan',
    'Posisi Kerja': 'posisi_kerja',
    'Gaji Aktual': 'gaji_aktual',
    'Status Pendaftaran': 'status_pendaftaran',
    'Tanggal Daftar': 'tanggal_daftar',
    'Catatan': 'catatan'
  },
  
  // Format 2: English version
  english: {
    'Full Name': 'nama_lengkap',
    'National ID': 'nik',
    'Gender': 'jenis_kelamin',
    'Place of Birth': 'tempat_lahir',
    'Date of Birth': 'tanggal_lahir',
    'Religion': 'agama',
    'Marital Status': 'status_pernikahan',
    'Full Address': 'alamat_lengkap',
    'Village': 'kelurahan',
    'District': 'kecamatan',
    'City': 'kota_kabupaten',
    'Province': 'provinsi',
    'Postal Code': 'kode_pos',
    'Phone Number': 'nomor_hp',
    'Email': 'email',
    'Last Education': 'pendidikan_terakhir',
    'School Name': 'nama_sekolah',
    'Graduation Year': 'tahun_lulus',
    'Major': 'jurusan',
    'Father Name': 'nama_ayah',
    'Mother Name': 'nama_ibu',
    'Family Address': 'alamat_keluarga',
    'Family Phone': 'nomor_hp_keluarga',
    'LPK Name': 'nama_lpk',
    'LPK Address': 'alamat_lpk',
    'LPK City': 'kota_lpk',
    'LPK Province': 'provinsi_lpk',
    'LPK Contact Person': 'kontak_person_lpk',
    'LPK Phone': 'nomor_telepon_lpk',
    'LPK Email': 'email_lpk',
    'Kumiai Name': 'nama_kumiai',
    'Kumiai Code': 'kode_kumiai',
    'Japan Address': 'alamat_jepang',
    'Japan City': 'kota_jepang',
    'Prefecture': 'prefektur',
    'Kumiai Contact': 'kontak_person_kumiai',
    'Kumiai Phone': 'nomor_telepon_kumiai',
    'Kumiai Email': 'email_kumiai',
    'Company Name': 'nama_perusahaan',
    'Company Address': 'alamat_perusahaan',
    'Company City': 'kota_perusahaan',
    'Company Prefecture': 'prefektur_perusahaan',
    'Business Field': 'bidang_usaha',
    'Company Contact': 'kontak_person_perusahaan',
    'Company Phone': 'nomor_telepon_perusahaan',
    'Company Email': 'email_perusahaan',
    'Placement Date': 'tanggal_penempatan',
    'Departure Date': 'tanggal_keberangkatan',
    'Return Date': 'tanggal_kepulangan',
    'Placement Status': 'status_penempatan',
    'Work Position': 'posisi_kerja',
    'Actual Salary': 'gaji_aktual',
    'Registration Status': 'status_pendaftaran',
    'Registration Date': 'tanggal_daftar',
    'Notes': 'catatan'
  }
}

// Required fields yang harus ada untuk import sukses
export const REQUIRED_FIELDS: (keyof ExcelRowData)[] = [
  'nama_lengkap',
  'nik',
  'jenis_kelamin',
  'tempat_lahir',
  'tanggal_lahir',
  'agama',
  'alamat_lengkap',
  'kelurahan',
  'kecamatan',
  'kota_kabupaten',
  'provinsi',
  'nomor_hp',
  'pendidikan_terakhir',
  'nama_sekolah',
  'tahun_lulus',
  'nama_lpk'
] 