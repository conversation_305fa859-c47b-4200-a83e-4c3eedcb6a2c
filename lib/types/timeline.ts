// Timeline Progress Types
export type TimelineStageCategory = 'preparation' | 'selection' | 'documentation' | 'training' | 'deployment'
export type TimelineStatus = 'belum_mulai' | 'berlangsung' | 'selesai' | 'dibatalkan'

export interface TimelineStage {
  id: string
  nama_stage: string
  deskripsi: string
  urutan: number
  icon: string
  kategori: TimelineStageCategory
  is_aktif: boolean
  created_at: string
  updated_at: string
}

export interface SiswaTimelineProgress {
  id: string
  siswa_id: string
  timeline_stage_id: string
  status: TimelineStatus
  tanggal_mulai?: string
  tanggal_selesai?: string
  catatan?: string
  dokumen_pendukung?: any
  created_by?: string
  created_at: string
  updated_at: string
}

export interface TimelineProgressWithStage extends SiswaTimelineProgress {
  timeline_stage: TimelineStage
}

export interface StudentTimelineView {
  id: string
  siswa_id: string
  nama_lengkap: string
  nama_stage: string
  deskripsi: string
  urutan: number
  icon: string
  kategori: TimelineStageCategory
  status: TimelineStatus
  tanggal_mulai?: string
  tanggal_selesai?: string
  catatan?: string
  dokumen_pendukung?: any
  created_at: string
  updated_at: string
}

export interface TimelineStageWithProgress extends TimelineStage {
  progress?: SiswaTimelineProgress
}

export interface TimelineStats {
  total_stages: number
  completed_stages: number
  current_stage?: string
  progress_percentage: number
} 