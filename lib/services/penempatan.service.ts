import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { PenempatanSiswa } from '@/lib/types/database'

export class PenempatanService {
  // Get all Penempatan with optional filtering
  static async getAll(filters?: {
    status_penempatan?: string
    siswa_id?: string
    job_order_id?: string
    perusahaan_id?: string
    kumiai_id?: string
    tahun?: number
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('penempatan_siswa')
      .select(`
        *,
        siswa(
          nama_lengkap,
          nik,
          jenis_kelamin,
          lpk_mitra(nama_lpk)
        ),
        job_order(
          judul_pekerjaan,
          posisi,
          bidang_kerja
        ),
        perusahaan_penerima(
          nama_perusahaan,
          prefektur
        ),
        kumiai(
          nama_kumiai,
          kode_kumiai
        )
      `)

    // Apply filters
    if (filters?.status_penempatan) {
      query = query.eq('status_penempatan', filters.status_penempatan)
    }

    if (filters?.siswa_id) {
      query = query.eq('siswa_id', filters.siswa_id)
    }

    if (filters?.job_order_id) {
      query = query.eq('job_order_id', filters.job_order_id)
    }

    if (filters?.perusahaan_id) {
      query = query.eq('perusahaan_id', filters.perusahaan_id)
    }

    if (filters?.kumiai_id) {
      query = query.eq('kumiai_id', filters.kumiai_id)
    }

    if (filters?.tahun) {
      const startDate = `${filters.tahun}-01-01`
      const endDate = `${filters.tahun}-12-31`
      query = query.gte('tanggal_penempatan', startDate).lte('tanggal_penempatan', endDate)
    }

    if (filters?.search) {
      // Search in related tables through joins
      query = query.or(`posisi_kerja.ilike.%${filters.search}%,alamat_kerja.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Penempatan by ID with full details
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        *,
        siswa(*),
        job_order(*),
        perusahaan_penerima(*),
        kumiai(*)
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Penempatan
  static async create(penempatanData: Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .insert(penempatanData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Penempatan
  static async update(id: string, penempatanData: Partial<Omit<PenempatanSiswa, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .update({
        ...penempatanData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Penempatan
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Penempatan statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('penempatan_siswa')
      .select('id', { count: 'exact' })

    const { data: activeData, error: activeError } = await supabase
      .from('penempatan_siswa')
      .select('id', { count: 'exact' })
      .eq('status_penempatan', 'aktif')

    const { data: berangkatData, error: berangkatError } = await supabase
      .from('penempatan_siswa')
      .select('id', { count: 'exact' })
      .eq('status_penempatan', 'berangkat')

    const { data: selesaiData, error: selesaiError } = await supabase
      .from('penempatan_siswa')
      .select('id', { count: 'exact' })
      .eq('status_penempatan', 'selesai')

    if (totalError || activeError || berangkatError || selesaiError) {
      throw new Error('Failed to fetch Penempatan statistics')
    }

    return {
      total: totalData?.length || 0,
      active: activeData?.length || 0,
      berangkat: berangkatData?.length || 0,
      selesai: selesaiData?.length || 0,
      ditempatkan: (totalData?.length || 0) - (activeData?.length || 0) - (berangkatData?.length || 0) - (selesaiData?.length || 0)
    }
  }

  // Get Penempatan by year for charts
  static async getByYear(year?: number) {
    const currentYear = year || new Date().getFullYear()
    const startDate = `${currentYear}-01-01`
    const endDate = `${currentYear}-12-31`

    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        tanggal_penempatan,
        status_penempatan,
        siswa(jenis_kelamin),
        perusahaan_penerima(prefektur)
      `)
      .gte('tanggal_penempatan', startDate)
      .lte('tanggal_penempatan', endDate)
      .order('tanggal_penempatan')

    return formatSupabaseResponse(data, error)
  }

  // Get Penempatan by Kumiai
  static async getByKumiai(kumiaiId: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        *,
        siswa(nama_lengkap, lpk_mitra(nama_lpk)),
        job_order(judul_pekerjaan),
        perusahaan_penerima(nama_perusahaan)
      `)
      .eq('kumiai_id', kumiaiId)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get Penempatan by Perusahaan
  static async getByPerusahaan(perusahaanId: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        *,
        siswa(nama_lengkap, lpk_mitra(nama_lpk)),
        job_order(judul_pekerjaan),
        kumiai(nama_kumiai)
      `)
      .eq('perusahaan_id', perusahaanId)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get monthly placement statistics for dashboard
  static async getMonthlyStats(year?: number) {
    const currentYear = year || new Date().getFullYear()
    const startDate = `${currentYear}-01-01`
    const endDate = `${currentYear}-12-31`

    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select('tanggal_penempatan')
      .gte('tanggal_penempatan', startDate)
      .lte('tanggal_penempatan', endDate)

    if (error) {
      throw new Error('Failed to fetch monthly stats')
    }

    // Group by month
    const monthlyStats = Array.from({ length: 12 }, (_, i) => ({
      month: i + 1,
      count: 0
    }))

    data?.forEach(item => {
      const month = new Date(item.tanggal_penempatan).getMonth()
      monthlyStats[month].count++
    })

    return monthlyStats
  }

  // Check if student is already placed
  static async checkStudentPlacement(siswaId: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select('id, status_penempatan')
      .eq('siswa_id', siswaId)
      .in('status_penempatan', ['ditempatkan', 'berangkat', 'aktif'])

    if (error) {
      throw new Error('Failed to check student placement')
    }

    return (data?.length || 0) > 0
  }
}
