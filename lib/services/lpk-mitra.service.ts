import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { LpkMitra } from '@/lib/types/database'

export class LpkMitraService {
  // Get all LPK Mitra with optional filtering
  static async getAll(filters?: {
    status?: string
    kota?: string
    provinsi?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('lpk_mitra')
      .select('*')

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.kota) {
      query = query.eq('kota', filters.kota)
    }

    if (filters?.provinsi) {
      query = query.eq('provinsi', filters.provinsi)
    }

    if (filters?.search) {
      query = query.or(`nama_lpk.ilike.%${filters.search}%,nama_pimpinan.ilike.%${filters.search}%,email.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get LPK Mitra by ID
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .select('*')
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new LPK Mitra
  static async create(lpkData: Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .insert(lpkData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update LPK Mitra
  static async update(id: string, lpkData: Partial<Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .update({
        ...lpkData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete LPK Mitra
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get LPK Mitra statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('lpk_mitra')
      .select('id', { count: 'exact' })

    const { data: activeData, error: activeError } = await supabase
      .from('lpk_mitra')
      .select('id', { count: 'exact' })
      .eq('status', 'aktif')

    if (totalError || activeError) {
      throw new Error('Failed to fetch LPK statistics')
    }

    return {
      total: totalData?.length || 0,
      active: activeData?.length || 0,
      inactive: (totalData?.length || 0) - (activeData?.length || 0)
    }
  }

  // Get LPK Mitra with student count
  static async getWithStudentCount() {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .select(`
        *,
        siswa(count)
      `)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique cities for filter options
  static async getUniqueCities() {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .select('kota')
      .order('kota')

    if (error) {
      throw new Error('Failed to fetch cities')
    }

    const uniqueCities = [...new Set(data?.map(item => item.kota) || [])]
    return uniqueCities
  }

  // Get unique provinces for filter options
  static async getUniqueProvinces() {
    const { data, error } = await supabase
      .from('lpk_mitra')
      .select('provinsi')
      .order('provinsi')

    if (error) {
      throw new Error('Failed to fetch provinces')
    }

    const uniqueProvinces = [...new Set(data?.map(item => item.provinsi) || [])]
    return uniqueProvinces
  }
}
