import { supabase } from '@/lib/supabase'
import type {
  Role,
  Permission,
  UserProfile,
  UserRoleAssignment,
  CreateUserRequest,
  UpdateUserRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  AssignRoleRequest,
  UserFilterParams,
  RoleFilterParams,
  PaginatedResponse,
  UserPermissions,
  RolePermissions,
  PermissionCheck,
  AccessControlContext
} from '@/lib/types/rbac'

export class RBACService {
  // =====================================================
  // User Management
  // =====================================================

  static async getUsers(params: UserFilterParams = {}): Promise<PaginatedResponse<UserProfile>> {
    const { search, role, is_active, lpk_mitra_id, page = 1, limit = 10 } = params
    const offset = (page - 1) * limit

    let query = supabase
      .from('user_profiles')
      .select(`
        *,
        lpk_mitra:lpk_mitra_id(id, nama_lpk),
        role_assignments:user_role_assignments(
          id,
          role_id,
          is_active,
          assigned_at,
          expires_at,
          lpk_mitra_id,
          role:roles(name, display_name)
        )
      `, { count: 'exact' })

    if (search) {
      query = query.or(`full_name.ilike.%${search}%,username.ilike.%${search}%,email.ilike.%${search}%`)
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active)
    }

    if (lpk_mitra_id) {
      query = query.eq('lpk_mitra_id', lpk_mitra_id)
    }

    if (role) {
      query = query.eq('role_assignments.role.name', role)
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1)
      .order('full_name')

    if (error) throw error

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit)
    }
  }

  static async getUserById(id: string): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        lpk_mitra:lpk_mitra_id(id, nama_lpk),
        role_assignments:user_role_assignments(
          id,
          role_id,
          is_active,
          assigned_at,
          expires_at,
          lpk_mitra_id,
          role:roles(name, display_name),
          lpk_mitra:lpk_mitra_id(id, nama_lpk)
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  static async createUser(userData: CreateUserRequest): Promise<UserProfile> {
    // For now, we'll create a placeholder since we need admin access to create users
    // In production, this would use Supabase Admin API
    throw new Error('User creation requires admin setup - please use Supabase dashboard for now')
  }

  static async updateUser(id: string, userData: UpdateUserRequest): Promise<UserProfile> {
    const { roles, ...profileData } = userData

    // Update user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update(profileData)
      .eq('id', id)

    if (profileError) throw profileError

    // Update roles if provided
    if (roles) {
      // Deactivate all current roles
      await supabase
        .from('user_role_assignments')
        .update({ is_active: false })
        .eq('user_id', id)

      // Assign new roles
      for (const roleName of roles) {
        await this.assignUserRole({
          user_id: id,
          role_name: roleName,
          lpk_mitra_id: userData.lpk_mitra_id
        })
      }
    }

    return await this.getUserById(id) as UserProfile
  }

  static async deleteUser(id: string): Promise<void> {
    // Deactivate user instead of deleting
    const { error } = await supabase
      .from('user_profiles')
      .update({ is_active: false })
      .eq('id', id)

    if (error) throw error
  }

  // =====================================================
  // Role Management
  // =====================================================

  static async getRoles(params: RoleFilterParams = {}): Promise<PaginatedResponse<Role>> {
    const { search, is_active, page = 1, limit = 10 } = params
    const offset = (page - 1) * limit

    let query = supabase
      .from('roles')
      .select('*', { count: 'exact' })

    if (search) {
      query = query.or(`name.ilike.%${search}%,display_name.ilike.%${search}%`)
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active)
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1)
      .order('display_name')

    if (error) throw error

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      total_pages: Math.ceil((count || 0) / limit)
    }
  }

  static async getRoleById(id: string): Promise<Role | null> {
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  static async createRole(roleData: CreateRoleRequest): Promise<Role> {
    const { permissions, ...roleInfo } = roleData

    // Create role
    const { data, error } = await supabase
      .from('roles')
      .insert(roleInfo)
      .select()
      .single()

    if (error) throw error

    // Assign permissions
    if (permissions && permissions.length > 0) {
      await this.updateRolePermissions(data.id, permissions)
    }

    return data
  }

  static async updateRole(id: string, roleData: UpdateRoleRequest): Promise<Role> {
    const { permissions, ...roleInfo } = roleData

    // Update role
    const { error } = await supabase
      .from('roles')
      .update(roleInfo)
      .eq('id', id)

    if (error) throw error

    // Update permissions if provided
    if (permissions) {
      await this.updateRolePermissions(id, permissions)
    }

    return await this.getRoleById(id) as Role
  }

  static async deleteRole(id: string): Promise<void> {
    // Deactivate role instead of deleting
    const { error } = await supabase
      .from('roles')
      .update({ is_active: false })
      .eq('id', id)

    if (error) throw error
  }

  // =====================================================
  // Permission Management
  // =====================================================

  static async getPermissions(): Promise<Permission[]> {
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .order('module', { ascending: true })
      .order('action', { ascending: true })

    if (error) throw error
    return data || []
  }

  static async getRolePermissions(roleId: string): Promise<RolePermissions> {
    const { data, error } = await supabase
      .from('role_permissions')
      .select(`
        permission:permissions(name)
      `)
      .eq('role_id', roleId)

    if (error) throw error

    const permissions: RolePermissions = {}
    data?.forEach(item => {
      if (item.permission) {
        permissions[item.permission.name] = true
      }
    })

    return permissions
  }

  static async updateRolePermissions(roleId: string, permissionNames: string[]): Promise<void> {
    // Remove existing permissions
    await supabase
      .from('role_permissions')
      .delete()
      .eq('role_id', roleId)

    if (permissionNames.length === 0) return

    // Get permission IDs
    const { data: permissions, error: permError } = await supabase
      .from('permissions')
      .select('id, name')
      .in('name', permissionNames)

    if (permError) throw permError

    // Insert new permissions
    const rolePermissions = permissions?.map(p => ({
      role_id: roleId,
      permission_id: p.id
    })) || []

    if (rolePermissions.length > 0) {
      const { error } = await supabase
        .from('role_permissions')
        .insert(rolePermissions)

      if (error) throw error
    }
  }

  // =====================================================
  // User Role Assignment
  // =====================================================

  static async assignUserRole(assignment: AssignRoleRequest): Promise<void> {
    const { data, error } = await supabase.rpc('assign_user_role', {
      target_user_id: assignment.user_id,
      role_name: assignment.role_name,
      lpk_id: assignment.lpk_mitra_id || null,
      expires_at: assignment.expires_at || null
    })

    if (error) throw error
  }

  static async revokeUserRole(userId: string, roleName: string): Promise<void> {
    const { error } = await supabase.rpc('revoke_user_role', {
      target_user_id: userId,
      role_name: roleName
    })

    if (error) throw error
  }

  // =====================================================
  // Permission Checking
  // =====================================================

  static async getUserPermissions(userId: string): Promise<UserPermissions> {
    const { data, error } = await supabase.rpc('get_user_permissions', {
      user_uuid: userId
    })

    if (error) throw error

    const permissions: UserPermissions = {}
    data?.forEach((perm: any) => {
      if (!permissions[perm.module]) {
        permissions[perm.module] = {}
      }
      permissions[perm.module][perm.action] = true
    })

    return permissions
  }

  static async checkUserPermission(userId: string, permission: PermissionCheck): Promise<boolean> {
    const permissionName = `${permission.module}.${permission.action}`
    
    const { data, error } = await supabase.rpc('user_has_permission', {
      user_uuid: userId,
      permission_name: permissionName
    })

    if (error) throw error
    return data || false
  }

  static async getAccessControlContext(userId: string): Promise<AccessControlContext> {
    const user = await this.getUserById(userId)
    if (!user) throw new Error('User not found')

    const permissions = await this.getUserPermissions(userId)
    const roles = user.role_assignments?.filter(ra => ra.is_active).map(ra => ra.role?.name).filter(Boolean) || []
    const permissionNames = Object.keys(permissions).flatMap(module => 
      Object.keys(permissions[module]).map(action => `${module}.${action}`)
    )

    return {
      user_id: userId,
      roles: roles as string[],
      permissions: permissionNames,
      lpk_mitra_id: user.lpk_mitra_id
    }
  }
}
