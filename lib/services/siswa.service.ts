import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { 
  Siswa, 
  SiswaWithRelations, 
  SiswaFormData, 
  SiswaFilterParams, 
  SiswaStats,
  SiswaPendidikan,
  SiswaPengalamanKerja,
  SiswaKeluarga,
  SiswaAttachment,
  SiswaSocialMedia,
  VerificationStatus,
  AvailabilityStatus 
} from '@/lib/types/database'

export class SiswaService {
  // Enhanced getAll with comprehensive filtering
  static async getAll(filters?: SiswaFilterParams) {
    let query = supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(id, nama_lpk),
        siswa_pendidikan(*),
        siswa_pengalaman_kerja(*),
        siswa_keluarga(*),
        siswa_attachments(*),
        siswa_social_media(*),
        penempatan_siswa(
          id,
          status_penempatan,
          tanggal_penempatan,
          job_order(id, judul_pekerjaan),
          perusahaan_penerima(id, nama_perusahaan, prefektur),
          kumiai(id, nama_kumiai)
        )
      `)

    // Apply comprehensive filters
    if (filters?.lpk_id) {
      query = query.eq('lpk_id', filters.lpk_id)
    }

    if (filters?.jenis_kelamin) {
      query = query.eq('jenis_kelamin', filters.jenis_kelamin)
    }

    if (filters?.status_pendaftaran) {
      query = query.eq('status_pendaftaran', filters.status_pendaftaran)
    }

    if (filters?.status_verifikasi) {
      query = query.eq('status_verifikasi', filters.status_verifikasi)
    }

    if (filters?.ketersediaan) {
      query = query.eq('ketersediaan', filters.ketersediaan)
    }

    if (filters?.pendidikan_min) {
      query = query.gte('pendidikan_terakhir', filters.pendidikan_min)
    }

    if (filters?.usia_min && filters?.usia_max) {
      const currentYear = new Date().getFullYear()
      const birthYearMax = currentYear - filters.usia_min
      const birthYearMin = currentYear - filters.usia_max
      query = query.gte('tanggal_lahir', `${birthYearMin}-01-01`)
      query = query.lte('tanggal_lahir', `${birthYearMax}-12-31`)
    }

    if (filters?.prefektur) {
      query = query.eq('penempatan_siswa.perusahaan_penerima.prefektur', filters.prefektur)
    }

    if (filters?.level_bahasa) {
      query = query.eq('level_bahasa_jepang', filters.level_bahasa)
    }

    // Enhanced search functionality
    if (filters?.search) {
      query = query.or(`
        nama_lengkap.ilike.%${filters.search}%,
        nama_lengkap_jepang.ilike.%${filters.search}%,
        nik.ilike.%${filters.search}%,
        email.ilike.%${filters.search}%,
        nomor_hp.ilike.%${filters.search}%,
        alamat_lengkap.ilike.%${filters.search}%,
        nama_sekolah.ilike.%${filters.search}%,
        jurusan.ilike.%${filters.search}%
      `)
    }

    // Apply sorting
    const sortBy = filters?.sort_by || 'created_at'
    const sortOrder = filters?.sort_order === 'asc' ? { ascending: true } : { ascending: false }
    query = query.order(sortBy, sortOrder)

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Siswa by ID with all related data
  static async getById(id: string): Promise<{ data: SiswaWithRelations | null; error: any }> {
    const { data, error } = await supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(*),
        siswa_pendidikan(*),
        siswa_pengalaman_kerja(*),
        siswa_keluarga(*),
        siswa_attachments(*),
        siswa_social_media(*),
        penempatan_siswa(
          *,
          job_order(*),
          perusahaan_penerima(*),
          kumiai(*)
        )
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Siswa with comprehensive data
  static async create(siswaData: SiswaFormData) {
    try {
      // Start transaction - now with full enhanced schema support
      const { data: siswa, error: siswaError } = await supabase
        .from('siswa')
        .insert({
          // Basic fields
          lpk_id: siswaData.lpk_id,
          nama_lengkap: siswaData.nama_lengkap,
          nama_lengkap_jepang: siswaData.nama_lengkap_jepang,
          nik: siswaData.nik,
          tempat_lahir: siswaData.tempat_lahir,
          tanggal_lahir: siswaData.tanggal_lahir,
          jenis_kelamin: siswaData.jenis_kelamin,
          agama: siswaData.agama,
          status_pernikahan: siswaData.status_pernikahan,
          
          // Contact & Address
          alamat_lengkap: siswaData.alamat_lengkap,
          kelurahan: siswaData.kelurahan,
          kecamatan: siswaData.kecamatan,
          kota_kabupaten: siswaData.kota_kabupaten,
          provinsi: siswaData.provinsi,
          kode_pos: siswaData.kode_pos,
          nomor_hp: siswaData.nomor_hp,
          email: siswaData.email,
          nomor_wa: siswaData.nomor_wa,
          
          // Location Coordinates
          latitude: siswaData.latitude,
          longitude: siswaData.longitude,
          alamat_koordinat: siswaData.alamat_koordinat,
          
          // Physical Info - ✅ Now fully supported
          tinggi_badan: siswaData.tinggi_badan,
          berat_badan: siswaData.berat_badan,
          ukuran_sepatu: siswaData.ukuran_sepatu,
          ukuran_baju: siswaData.ukuran_baju,
          ukuran_celana: siswaData.ukuran_celana,
          lingkar_kepala: siswaData.lingkar_kepala,
          lingkar_pinggang: siswaData.lingkar_pinggang,
          golongan_darah: siswaData.golongan_darah,
          riwayat_penyakit: siswaData.riwayat_penyakit,
          alergi: siswaData.alergi,
          
          // Education - Enhanced
          pendidikan_terakhir: siswaData.pendidikan_terakhir,
          nama_sekolah: siswaData.nama_sekolah,
          tahun_lulus: siswaData.tahun_lulus,
          jurusan: siswaData.jurusan,
          ipk: siswaData.ipk,
          sertifikat_keahlian: siswaData.sertifikat_keahlian,
          
          // Family - Enhanced
          nama_ayah: siswaData.nama_ayah,
          nama_ibu: siswaData.nama_ibu,
          alamat_keluarga: siswaData.alamat_keluarga,
          nomor_hp_keluarga: siswaData.nomor_hp_keluarga,
          pekerjaan_ayah: siswaData.pekerjaan_ayah,
          pekerjaan_ibu: siswaData.pekerjaan_ibu,
          
          // Japanese-specific - ✅ Now fully supported
          hobi: siswaData.hobi,
          bakat_khusus: siswaData.bakat_khusus,
          minat_kerja: siswaData.minat_kerja,
          pengalaman_organisasi: siswaData.pengalaman_organisasi,
          tujuan_ke_jepang: siswaData.tujuan_ke_jepang,
          target_kerja_jepang: siswaData.target_kerja_jepang,
          rencana_setelah_jepang: siswaData.rencana_setelah_jepang,
          
          // LPK - ✅ Now fully supported
          tanggal_masuk_lpk: siswaData.tanggal_masuk_lpk,
          lama_belajar_bulan: siswaData.lama_belajar_bulan,
          level_bahasa_jepang: siswaData.level_bahasa_jepang,
          sertifikat_bahasa: siswaData.sertifikat_bahasa,
          nilai_ujian_masuk: siswaData.nilai_ujian_masuk,
          
          // Availability - ✅ Now fully supported
          ketersediaan: siswaData.ketersediaan,
          tanggal_ketersediaan: siswaData.tanggal_ketersediaan,
          keterangan_ketersediaan: siswaData.keterangan_ketersediaan,
          
          // System fields
          status_pendaftaran: 'draft',
          status_verifikasi: 'belum_diverifikasi',
          tanggal_daftar: new Date().toISOString()
        })
        .select()
        .single()

      if (siswaError) throw siswaError

      // Insert related data
      if (siswaData.pendidikan && siswaData.pendidikan.length > 0) {
        for (const edu of siswaData.pendidikan) {
          await supabase
            .from('siswa_pendidikan')
            .insert({
              siswa_id: siswa.id,
              ...edu
            })
        }
      }

      if (siswaData.pengalaman_kerja && siswaData.pengalaman_kerja.length > 0) {
        for (const exp of siswaData.pengalaman_kerja) {
          await supabase
            .from('siswa_pengalaman_kerja')
            .insert({
              siswa_id: siswa.id,
              ...exp
            })
        }
      }

      if (siswaData.keluarga && siswaData.keluarga.length > 0) {
        for (const family of siswaData.keluarga) {
          await supabase
            .from('siswa_keluarga')
            .insert({
              siswa_id: siswa.id,
              ...family
            })
        }
      }

      if (siswaData.social_media && siswaData.social_media.length > 0) {
        for (const social of siswaData.social_media) {
          await supabase
            .from('siswa_social_media')
            .insert({
              siswa_id: siswa.id,
              ...social
            })
        }
      }

      // ✅ Profile completeness will be auto-calculated by database trigger
      console.log('🎉 Siswa created with full enhanced data support!')

      return siswa
    } catch (error) {
      console.error('Error creating siswa:', error)
      throw new Error(`Failed to create siswa: ${(error as any)?.message || 'Unknown error'}`)
    }
  }

  // Helper function to filter pendidikan data for basic schema
  private static filterPendidikanFields(pendidikan: any) {
    // Check for both possible field names from form
    const namaSekolah = pendidikan.nama_sekolah
    const jenjang = pendidikan.jenjang
    
    // Validate required fields - jenjang and nama_sekolah are required
    if (!jenjang || !namaSekolah) {
      console.warn('Skipping pendidikan record - missing required fields:', pendidikan)
      return null
    }

    return {
      jenjang: jenjang,
      nama_sekolah: namaSekolah, // Map to correct database field name
      tahun_lulus: pendidikan.tahun_lulus || null,
      nilai_rata_rata: pendidikan.nilai_rata_rata || pendidikan.ipk || null
    }
  }

  // Helper function to filter pengalaman kerja data for basic schema  
  private static filterPengalamanKerjaFields(pengalaman: any) {
    // Validate required fields - nama_perusahaan and posisi are required
    if (!pengalaman.nama_perusahaan || !pengalaman.posisi) {
      console.warn('Skipping pengalaman kerja record - missing required fields:', pengalaman)
      return null
    }

    return {
      nama_perusahaan: pengalaman.nama_perusahaan,
      posisi: pengalaman.posisi,
      tahun_mulai: pengalaman.tahun_mulai || null,
      tahun_selesai: pengalaman.tahun_selesai || null,
      deskripsi: pengalaman.deskripsi || null
    }
  }

  // Helper function to filter keluarga data for basic schema
  private static filterKeluargaFields(keluarga: any) {
    // Check for both possible field names from form
    const nama = keluarga.nama || keluarga.nama_lengkap
    const hubungan = keluarga.hubungan
    
    // Validate required fields - nama and hubungan are required
    if (!nama || !hubungan) {
      console.warn('Skipping keluarga record - missing required fields:', keluarga)
      return null
    }

    return {
      nama: nama, // Map to correct database field name
      hubungan: hubungan,
      umur: keluarga.umur || null,
      pekerjaan: keluarga.pekerjaan || null,
      alamat: keluarga.alamat || null,
      no_telp: keluarga.no_telp || keluarga.nomor_hp || null, // Handle both field names
      tipe: keluarga.tipe || 'indonesia' // default value
    }
  }

  // Helper function to filter social media data
  private static filterSocialMediaFields(social: any) {
    // Validate required fields - platform and username are required
    if (!social.platform || !social.username) {
      console.warn('Skipping social media record - missing required fields:', social)
      return null
    }

    return {
      platform: social.platform,
      username: social.username,
      url: social.url || null,
      is_active: social.is_active !== undefined ? social.is_active : true
    }
  }

  // Helper function to filter data - now includes ALL enhanced fields from migration
  private static filterBasicSchemaFields(data: Partial<SiswaFormData>) {
    // Include ALL fields since migration added them to database schema
    const allFields = {
      // Basic info - CORE FIELDS
      nama_lengkap: data.nama_lengkap,
      nama_lengkap_jepang: data.nama_lengkap_jepang,
      nik: data.nik,
      tempat_lahir: data.tempat_lahir,
      tanggal_lahir: data.tanggal_lahir,
      jenis_kelamin: data.jenis_kelamin,
      agama: data.agama,
      status_pernikahan: data.status_pernikahan,
      
      // Contact & Address - CORE FIELDS
      alamat_lengkap: data.alamat_lengkap,
      kelurahan: data.kelurahan,
      kecamatan: data.kecamatan,
      kota_kabupaten: data.kota_kabupaten,
      provinsi: data.provinsi,
      kode_pos: data.kode_pos,
      nomor_hp: data.nomor_hp,
      email: data.email,
      nomor_wa: data.nomor_wa, // ✅ Added in migration
      
      // Location Coordinates - ✅ Added in new migration
      latitude: data.latitude,
      longitude: data.longitude,
      alamat_koordinat: data.alamat_koordinat,
      
      // Physical data - ✅ ALL ADDED IN MIGRATION
      tinggi_badan: data.tinggi_badan,
      berat_badan: data.berat_badan,
      ukuran_sepatu: data.ukuran_sepatu,
      ukuran_baju: data.ukuran_baju, // ✅ Added in migration
      ukuran_celana: data.ukuran_celana, // ✅ Added in migration
      lingkar_kepala: data.lingkar_kepala, // ✅ Added in supplementary migration
      lingkar_pinggang: data.lingkar_pinggang, // ✅ Added in supplementary migration
      golongan_darah: data.golongan_darah, // ✅ Added in migration
      riwayat_penyakit: data.riwayat_penyakit, // ✅ Added in migration
      alergi: data.alergi, // ✅ Added in migration
      
      // Education - ENHANCED
      pendidikan_terakhir: data.pendidikan_terakhir,
      nama_sekolah: data.nama_sekolah,
      tahun_lulus: data.tahun_lulus,
      jurusan: data.jurusan,
      ipk: data.ipk, // ✅ Added in migration
      sertifikat_keahlian: data.sertifikat_keahlian, // ✅ Added in migration
      
      // Family - ENHANCED
      nama_ayah: data.nama_ayah,
      nama_ibu: data.nama_ibu,
      alamat_keluarga: data.alamat_keluarga,
      nomor_hp_keluarga: data.nomor_hp_keluarga,
      pekerjaan_ayah: data.pekerjaan_ayah, // ✅ Added in migration
      pekerjaan_ibu: data.pekerjaan_ibu, // ✅ Added in migration
      
      // Japanese-specific - ✅ ALL ADDED IN MIGRATION
      hobi: data.hobi,
      bakat_khusus: data.bakat_khusus,
      minat_kerja: data.minat_kerja,
      pengalaman_organisasi: data.pengalaman_organisasi, // ✅ Added in migration
      tujuan_ke_jepang: data.tujuan_ke_jepang,
      target_kerja_jepang: data.target_kerja_jepang, // ✅ Added in migration
      rencana_setelah_jepang: data.rencana_setelah_jepang, // ✅ Added in migration
      
      // LPK - ✅ ALL ADDED IN MIGRATION
      lpk_id: data.lpk_id,
      tanggal_masuk_lpk: data.tanggal_masuk_lpk, // ✅ Added in supplementary migration
      lama_belajar_bulan: data.lama_belajar_bulan, // ✅ Added in migration
      level_bahasa_jepang: data.level_bahasa_jepang, // ✅ Added in migration
      sertifikat_bahasa: data.sertifikat_bahasa, // ✅ Added in migration
      nilai_ujian_masuk: data.nilai_ujian_masuk, // ✅ Added in migration
      
      // Availability - ✅ ALL ADDED IN MIGRATION
      ketersediaan: data.ketersediaan, // ✅ Added in migration
      tanggal_ketersediaan: data.tanggal_ketersediaan, // ✅ Added in migration
      keterangan_ketersediaan: data.keterangan_ketersediaan, // ✅ Added in migration
      
      // System fields
      updated_at: new Date().toISOString()
    }

    // Remove undefined fields
    const filtered = Object.fromEntries(
      Object.entries(allFields).filter(([_, value]) => value !== undefined)
    )

    console.log('Enhanced data for full schema (post-migration):', filtered)
    return filtered
  }

  // Update Siswa with comprehensive data
  static async update(id: string, siswaData: Partial<SiswaFormData>) {
    try {
      console.log('Starting siswa update for ID:', id)
      console.log('Original update data:', siswaData)

      // Filter data to only include basic schema fields
      const basicUpdateData = this.filterBasicSchemaFields(siswaData)
      console.log('Filtered update data for basic schema:', basicUpdateData)

      // ✅ Enhanced fields are now available after migration 20250114000001
      console.log('🎉 All enhanced fields are now supported in database schema!')
      console.log('✅ Physical data, Japanese-specific data, LPK data, and availability data will be saved')

      // Update main siswa record with ALL enhanced fields (post-migration)
      const { data: siswa, error: siswaError } = await supabase
        .from('siswa')
        .update(basicUpdateData)
        .eq('id', id)
        .select()
        .single()

      if (siswaError) {
        console.error('Error updating main siswa record:', siswaError)
        throw new Error(`Failed to update main siswa: ${siswaError.message}`)
      }

      console.log('Main siswa record updated successfully:', siswa)

      // Update related data if provided (these tables exist)
      if (siswaData.pendidikan) {
        console.log('Updating pendidikan data...')
        // Delete existing and insert new
        const { error: deleteEduError } = await supabase
          .from('siswa_pendidikan')
          .delete()
          .eq('siswa_id', id)
        
        if (deleteEduError) {
          console.error('Error deleting existing pendidikan:', deleteEduError)
          throw new Error(`Failed to delete existing pendidikan: ${deleteEduError.message}`)
        }

        for (const edu of siswaData.pendidikan) {
          const filteredEdu = this.filterPendidikanFields(edu)
          console.log('Filtered pendidikan data:', filteredEdu)
          
          // Skip if validation failed
          if (!filteredEdu) {
            console.log('Skipping invalid pendidikan record')
            continue
          }
          
          const { error: insertEduError } = await supabase
            .from('siswa_pendidikan')
            .insert({
              siswa_id: id,
              ...filteredEdu
            })
          
          if (insertEduError) {
            console.error('Error inserting pendidikan:', insertEduError)
            throw new Error(`Failed to insert pendidikan: ${insertEduError.message}`)
          }
        }
        console.log('Pendidikan data updated successfully')
      }

      if (siswaData.pengalaman_kerja) {
        console.log('Updating pengalaman kerja data...')
        const { error: deleteExpError } = await supabase
          .from('siswa_pengalaman_kerja')
          .delete()
          .eq('siswa_id', id)
        
        if (deleteExpError) {
          console.error('Error deleting existing pengalaman_kerja:', deleteExpError)
          throw new Error(`Failed to delete existing pengalaman_kerja: ${deleteExpError.message}`)
        }

        for (const exp of siswaData.pengalaman_kerja) {
          const filteredExp = this.filterPengalamanKerjaFields(exp)
          console.log('Filtered pengalaman kerja data:', filteredExp)
          
          // Skip if validation failed
          if (!filteredExp) {
            console.log('Skipping invalid pengalaman kerja record')
            continue
          }
          
          const { error: insertExpError } = await supabase
            .from('siswa_pengalaman_kerja')
            .insert({
              siswa_id: id,
              ...filteredExp
            })
          
          if (insertExpError) {
            console.error('Error inserting pengalaman_kerja:', insertExpError)
            throw new Error(`Failed to insert pengalaman_kerja: ${insertExpError.message}`)
          }
        }
        console.log('Pengalaman kerja data updated successfully')
      }

      if (siswaData.keluarga) {
        console.log('Updating keluarga data...')
        const { error: deleteFamilyError } = await supabase
          .from('siswa_keluarga')
          .delete()
          .eq('siswa_id', id)
        
        if (deleteFamilyError) {
          console.error('Error deleting existing keluarga:', deleteFamilyError)
          throw new Error(`Failed to delete existing keluarga: ${deleteFamilyError.message}`)
        }

        for (const family of siswaData.keluarga) {
          const filteredFamily = this.filterKeluargaFields(family)
          console.log('Filtered keluarga data:', filteredFamily)
          
          // Skip if validation failed
          if (!filteredFamily) {
            console.log('Skipping invalid keluarga record')
            continue
          }
          
          const { error: insertFamilyError } = await supabase
            .from('siswa_keluarga')
            .insert({
              siswa_id: id,
              ...filteredFamily
            })
          
          if (insertFamilyError) {
            console.error('Error inserting keluarga:', insertFamilyError)
            throw new Error(`Failed to insert keluarga: ${insertFamilyError.message}`)
          }
        }
        console.log('Keluarga data updated successfully')
      }

      if (siswaData.social_media) {
        console.log('Updating social media data...')
        const { error: deleteSocialError } = await supabase
          .from('siswa_social_media')
          .delete()
          .eq('siswa_id', id)
        
        if (deleteSocialError) {
          console.error('Error deleting existing social_media:', deleteSocialError)
          throw new Error(`Failed to delete existing social_media: ${deleteSocialError.message}`)
        }

        for (const social of siswaData.social_media) {
          const filteredSocial = this.filterSocialMediaFields(social)
          console.log('Filtered social media data:', filteredSocial)
          
          // Skip if validation failed
          if (!filteredSocial) {
            console.log('Skipping invalid social media record')
            continue
          }
          
          const { error: insertSocialError } = await supabase
            .from('siswa_social_media')
            .insert({
              siswa_id: id,
              ...filteredSocial
            })
          
          if (insertSocialError) {
            console.error('Error inserting social_media:', insertSocialError)
            throw new Error(`Failed to insert social_media: ${insertSocialError.message}`)
          }
        }
        console.log('Social media data updated successfully')
      }

      // ✅ Profile completeness calculation now available (enhanced schema fields included)
      console.log('🔢 Profile completeness will be auto-calculated by database trigger')
      console.log('Update operation completed successfully')

      return siswa
    } catch (error) {
      console.error('Error updating siswa:', error)
      console.error('Error details:', JSON.stringify(error, null, 2))
      throw new Error(`Failed to update siswa: ${(error as any)?.message || 'Unknown error'}`)
    }
  }

  // Delete Siswa and all related data
  static async delete(id: string) {
    try {
      // Delete related data first (cascade should handle this but being explicit)
      await supabase.from('siswa_attachments').delete().eq('siswa_id', id)
      await supabase.from('siswa_social_media').delete().eq('siswa_id', id)
      await supabase.from('siswa_keluarga').delete().eq('siswa_id', id)
      await supabase.from('siswa_pengalaman_kerja').delete().eq('siswa_id', id)
      await supabase.from('siswa_pendidikan').delete().eq('siswa_id', id)
      
      // Delete main record
      const { data, error } = await supabase
        .from('siswa')
        .delete()
        .eq('id', id)
        .select()
        .single()

      return formatSupabaseSingleResponse(data, error)
    } catch (error) {
      return formatSupabaseSingleResponse(null, error)
    }
  }

  // Calculate profile completeness percentage
  static async calculateProfileCompleteness(siswaId: string): Promise<number> {
    const { data: siswa } = await supabase
      .from('siswa')
      .select('*')
      .eq('id', siswaId)
      .single()

    if (!siswa) return 0

    const requiredFields = [
      'nama_lengkap', 'nik', 'tempat_lahir', 'tanggal_lahir', 'jenis_kelamin',
      'alamat_lengkap', 'kelurahan', 'kecamatan', 'kota_kabupaten', 'provinsi',
      'nomor_hp', 'pendidikan_terakhir'
    ]

    const optionalHighValueFields = [
      'nama_lengkap_jepang', 'email', 'tinggi_badan', 'berat_badan',
      'nama_sekolah', 'tahun_lulus', 'level_bahasa_jepang', 'hobi',
              'minat_kerja', 'tujuan_ke_jepang'
    ]

    let score = 0
    let maxScore = 100

    // Required fields (60 points)
    const completedRequired = requiredFields.filter(field => (siswa as any)[field]).length
    score += (completedRequired / requiredFields.length) * 60

    // Optional high-value fields (30 points)
    const completedOptional = optionalHighValueFields.filter(field => (siswa as any)[field]).length
    score += (completedOptional / optionalHighValueFields.length) * 30

    // Bonus for related data (10 points)
    const { data: pendidikan } = await supabase
      .from('siswa_pendidikan')
      .select('id')
      .eq('siswa_id', siswaId)

    const { data: attachments } = await supabase
      .from('siswa_attachments')
      .select('id')
      .eq('siswa_id', siswaId)

    if (pendidikan && pendidikan.length > 0) score += 5
    if (attachments && attachments.length > 0) score += 5

    return Math.round(Math.min(score, maxScore))
  }

  // Verification workflow methods
  static async updateVerificationStatus(
    siswaId: string, 
    status: VerificationStatus, 
    verifiedBy?: string, 
    catatan?: string
  ) {
    const updateData: any = {
      status_verifikasi: status,
      updated_at: new Date().toISOString()
    }

    if (status === 'terverifikasi' || status === 'ditolak') {
      updateData.verified_by = verifiedBy
      updateData.verified_at = new Date().toISOString()
      updateData.catatan_verifikasi = catatan
    }

    const { data, error } = await supabase
      .from('siswa')
      .update(updateData)
      .eq('id', siswaId)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Bulk operations
  static async bulkUpdateStatus(siswaIds: string[], status: any) {
    const { data, error } = await supabase
      .from('siswa')
      .update({ 
        status_pendaftaran: status,
        updated_at: new Date().toISOString()
      })
      .in('id', siswaIds)
      .select()

    return formatSupabaseResponse(data, error)
  }

  static async bulkUpdateVerification(siswaIds: string[], status: VerificationStatus, verifiedBy: string) {
    const { data, error } = await supabase
      .from('siswa')
      .update({ 
        status_verifikasi: status,
        verified_by: verifiedBy,
        verified_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', siswaIds)
      .select()

    return formatSupabaseResponse(data, error)
  }

  // Enhanced statistics
  static async getComprehensiveStats(): Promise<SiswaStats> {
    // Total count
    const { count: total } = await supabase
      .from('siswa')
      .select('*', { count: 'exact' })

    // By status pendaftaran
    const { data: statusData } = await supabase
      .from('siswa')
      .select('status_pendaftaran')

    // By status verifikasi
    const { data: verifikasiData } = await supabase
      .from('siswa')
      .select('status_verifikasi')

    // By ketersediaan
    const { data: ketersediaanData } = await supabase
      .from('siswa')
      .select('ketersediaan')

    // By gender
    const { data: genderData } = await supabase
      .from('siswa')
      .select('jenis_kelamin')

    // By LPK
    const { data: lpkData } = await supabase
      .from('siswa')
      .select(`
        lpk_id,
        lpk_mitra(nama_lpk)
      `)

    // Average profile completeness
    const { data: completenessData } = await supabase
      .from('siswa')
      .select('profile_completeness')

    // Recent registrations (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { count: recentCount } = await supabase
      .from('siswa')
      .select('*', { count: 'exact' })
      .gte('created_at', thirtyDaysAgo.toISOString())

    // Process statistics
    const by_status_pendaftaran = statusData?.reduce((acc: any, curr) => {
      acc[curr.status_pendaftaran] = (acc[curr.status_pendaftaran] || 0) + 1
      return acc
    }, {}) || {}

    const by_status_verifikasi = verifikasiData?.reduce((acc: any, curr) => {
      acc[curr.status_verifikasi] = (acc[curr.status_verifikasi] || 0) + 1
      return acc
    }, {}) || {}

    const by_ketersediaan = ketersediaanData?.reduce((acc: any, curr) => {
      acc[curr.ketersediaan] = (acc[curr.ketersediaan] || 0) + 1
      return acc
    }, {}) || {}

    const by_jenis_kelamin = genderData?.reduce((acc: any, curr) => {
      acc[curr.jenis_kelamin] = (acc[curr.jenis_kelamin] || 0) + 1
      return acc
    }, {}) || {}

    const lpkCounts: { [key: string]: { nama_lpk: string; count: number } } = {}
    lpkData?.forEach((item: any) => {
      if (item.lpk_id) {
        if (!lpkCounts[item.lpk_id]) {
          lpkCounts[item.lpk_id] = {
            nama_lpk: item.lpk_mitra?.nama_lpk || 'Unknown',
            count: 0
          }
        }
        lpkCounts[item.lpk_id].count++
      }
    })

    const by_lpk = Object.entries(lpkCounts).map(([lpk_id, data]) => ({
      lpk_id,
      nama_lpk: data.nama_lpk,
      count: data.count
    }))

    const avg_profile_completeness = completenessData?.length
      ? Math.round(completenessData.reduce((sum, item) => sum + (item.profile_completeness || 0), 0) / completenessData.length)
      : 0

    return {
      total: total || 0,
      by_status_pendaftaran,
      by_status_verifikasi,
      by_ketersediaan,
      by_jenis_kelamin,
      by_lpk,
      avg_profile_completeness,
      recent_registrations: recentCount || 0
    }
  }

  // Get available students for job order
  static async getAvailableForJobOrder(jobOrderId?: string) {
    let query = supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(nama_lpk)
      `)
      .eq('status_pendaftaran', 'approved')
      .eq('status_verifikasi', 'terverifikasi')
      .in('ketersediaan', ['siap', 'kondisional'])

    // Exclude already placed students
    const { data: placedStudents } = await supabase
      .from('penempatan_siswa')
      .select('siswa_id')
      .in('status_penempatan', ['ditempatkan', 'berangkat', 'aktif'])

    if (placedStudents && placedStudents.length > 0) {
      const placedIds = placedStudents.map(p => p.siswa_id)
      query = query.not('id', 'in', `(${placedIds.join(',')})`)
    }

    query = query.order('profile_completeness', { ascending: false })

    const { data, error } = await query
    return formatSupabaseResponse(data, error)
  }

  // Utility methods
  static async checkNikExists(nik: string, excludeId?: string) {
    let query = supabase
      .from('siswa')
      .select('id')
      .eq('nik', nik)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error('Failed to check NIK')
    }

    return (data?.length || 0) > 0
  }

  static async getUniqueValues(field: string) {
    const { data, error } = await supabase
      .from('siswa')
      .select(field)
      .order(field)

    if (error) {
      throw new Error(`Failed to fetch unique ${field}`)
    }

    return [...new Set(data?.map(item => (item as any)[field]).filter(Boolean) || [])]
  }

  // Document/Attachment management
  static async addAttachment(attachment: Omit<SiswaAttachment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('siswa_attachments')
      .insert(attachment)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  static async deleteAttachment(attachmentId: string) {
    const { data, error } = await supabase
      .from('siswa_attachments')
      .delete()
      .eq('id', attachmentId)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  static async getAttachmentsByType(siswaId: string, jenisDokumen: string) {
    const { data, error } = await supabase
      .from('siswa_attachments')
      .select('*')
      .eq('siswa_id', siswaId)
      .eq('jenis_dokumen', jenisDokumen)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }
}
