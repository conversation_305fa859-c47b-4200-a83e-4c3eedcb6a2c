import { supabase } from '@/lib/supabase'

export class DashboardService {
  // Get overall dashboard statistics
  static async getOverallStats() {
    try {
      // Get counts for all main entities
      const [
        lpkCount,
        kumiaiCount,
        perusahaanCount,
        jobOrderCount,
        siswaCount,
        penempatanCount
      ] = await Promise.all([
        supabase.from('lpk_mitra').select('id', { count: 'exact' }),
        supabase.from('kumiai').select('id', { count: 'exact' }),
        supabase.from('perusahaan_penerima').select('id', { count: 'exact' }),
        supabase.from('job_order').select('id', { count: 'exact' }),
        supabase.from('siswa').select('id', { count: 'exact' }),
        supabase.from('penempatan_siswa').select('id', { count: 'exact' })
      ])

      return {
        lpk_mitra: lpkCount.count || 0,
        kumiai: kumiaiCount.count || 0,
        perusahaan: perusahaanCount.count || 0,
        job_order: jobOrderCount.count || 0,
        siswa: siswaCount.count || 0,
        penempatan: penempatanCount.count || 0
      }
    } catch (error) {
      console.error('Error fetching overall stats:', error)
      throw new Error('Failed to fetch dashboard statistics')
    }
  }

  // Get detailed statistics for dashboard cards
  static async getDetailedStats() {
    try {
      const [
        siswaStats,
        jobOrderStats,
        penempatanStats,
        lpkStats
      ] = await Promise.all([
        this.getSiswaStats(),
        this.getJobOrderStats(),
        this.getPenempatanStats(),
        this.getLpkStats()
      ])

      return {
        siswa: siswaStats,
        jobOrder: jobOrderStats,
        penempatan: penempatanStats,
        lpk: lpkStats
      }
    } catch (error) {
      console.error('Error fetching detailed stats:', error)
      throw new Error('Failed to fetch detailed statistics')
    }
  }

  // Get Siswa statistics
  private static async getSiswaStats() {
    const [total, approved, pending, rejected] = await Promise.all([
      supabase.from('siswa').select('id', { count: 'exact' }),
      supabase.from('siswa').select('id', { count: 'exact' }).eq('status_pendaftaran', 'approved'),
      supabase.from('siswa').select('id', { count: 'exact' }).in('status_pendaftaran', ['draft', 'submitted', 'review']),
      supabase.from('siswa').select('id', { count: 'exact' }).eq('status_pendaftaran', 'rejected')
    ])

    return {
      total: total.count || 0,
      approved: approved.count || 0,
      pending: pending.count || 0,
      rejected: rejected.count || 0
    }
  }

  // Get Job Order statistics
  private static async getJobOrderStats() {
    const [total, published, closed, draft] = await Promise.all([
      supabase.from('job_order').select('id', { count: 'exact' }),
      supabase.from('job_order').select('id', { count: 'exact' }).eq('status', 'published'),
      supabase.from('job_order').select('id', { count: 'exact' }).eq('status', 'closed'),
      supabase.from('job_order').select('id', { count: 'exact' }).eq('status', 'draft')
    ])

    return {
      total: total.count || 0,
      published: published.count || 0,
      closed: closed.count || 0,
      draft: draft.count || 0
    }
  }

  // Get Penempatan statistics
  private static async getPenempatanStats() {
    const [total, active, berangkat, selesai, ditempatkan] = await Promise.all([
      supabase.from('penempatan_siswa').select('id', { count: 'exact' }),
      supabase.from('penempatan_siswa').select('id', { count: 'exact' }).eq('status_penempatan', 'aktif'),
      supabase.from('penempatan_siswa').select('id', { count: 'exact' }).eq('status_penempatan', 'berangkat'),
      supabase.from('penempatan_siswa').select('id', { count: 'exact' }).eq('status_penempatan', 'selesai'),
      supabase.from('penempatan_siswa').select('id', { count: 'exact' }).eq('status_penempatan', 'ditempatkan')
    ])

    return {
      total: total.count || 0,
      active: active.count || 0,
      berangkat: berangkat.count || 0,
      selesai: selesai.count || 0,
      ditempatkan: ditempatkan.count || 0
    }
  }

  // Get LPK statistics
  private static async getLpkStats() {
    const [total, active, inactive] = await Promise.all([
      supabase.from('lpk_mitra').select('id', { count: 'exact' }),
      supabase.from('lpk_mitra').select('id', { count: 'exact' }).eq('status', 'aktif'),
      supabase.from('lpk_mitra').select('id', { count: 'exact' }).eq('status', 'nonaktif')
    ])

    return {
      total: total.count || 0,
      active: active.count || 0,
      inactive: inactive.count || 0
    }
  }

  // Get monthly placement data for charts
  static async getMonthlyPlacementData(year?: number) {
    const currentYear = year || new Date().getFullYear()
    const startDate = `${currentYear}-01-01`
    const endDate = `${currentYear}-12-31`

    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select('tanggal_penempatan')
      .gte('tanggal_penempatan', startDate)
      .lte('tanggal_penempatan', endDate)

    if (error) {
      throw new Error('Failed to fetch monthly placement data')
    }

    // Group by month
    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: new Date(0, i).toLocaleString('id-ID', { month: 'short' }),
      count: 0
    }))

    data?.forEach(item => {
      const month = new Date(item.tanggal_penempatan).getMonth()
      monthlyData[month].count++
    })

    return monthlyData
  }

  // Get placement by prefecture data for charts
  static async getPlacementByPrefecture() {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        perusahaan_penerima(prefektur)
      `)
      .in('status_penempatan', ['aktif', 'berangkat', 'ditempatkan'])

    if (error) {
      throw new Error('Failed to fetch placement by prefecture')
    }

    // Group by prefecture
    const prefectureCount: { [key: string]: number } = {}
    
    data?.forEach(item => {
      const prefecture = item.perusahaan_penerima?.prefektur
      if (prefecture) {
        prefectureCount[prefecture] = (prefectureCount[prefecture] || 0) + 1
      }
    })

    return Object.entries(prefectureCount)
      .map(([prefecture, count]) => ({ prefecture, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10) // Top 10 prefectures
  }

  // Get recent activities
  static async getRecentActivities(limit: number = 10) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        id,
        created_at,
        status_penempatan,
        siswa(nama_lengkap),
        perusahaan_penerima(nama_perusahaan),
        job_order(judul_pekerjaan)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error('Failed to fetch recent activities')
    }

    return data || []
  }

  // Get gender distribution
  static async getGenderDistribution() {
    const { data, error } = await supabase
      .from('siswa')
      .select('jenis_kelamin')
      .eq('status_pendaftaran', 'approved')

    if (error) {
      throw new Error('Failed to fetch gender distribution')
    }

    const genderCount = { L: 0, P: 0 }
    data?.forEach(item => {
      if (item.jenis_kelamin === 'L' || item.jenis_kelamin === 'P') {
        genderCount[item.jenis_kelamin]++
      }
    })

    return [
      { gender: 'Laki-laki', count: genderCount.L },
      { gender: 'Perempuan', count: genderCount.P }
    ]
  }

  // Get education level distribution
  static async getEducationDistribution() {
    const { data, error } = await supabase
      .from('siswa')
      .select('pendidikan_terakhir')
      .eq('status_pendaftaran', 'approved')

    if (error) {
      throw new Error('Failed to fetch education distribution')
    }

    const educationCount: { [key: string]: number } = {}
    data?.forEach(item => {
      const education = item.pendidikan_terakhir
      educationCount[education] = (educationCount[education] || 0) + 1
    })

    return Object.entries(educationCount)
      .map(([education, count]) => ({ education, count }))
      .sort((a, b) => b.count - a.count)
  }
}
