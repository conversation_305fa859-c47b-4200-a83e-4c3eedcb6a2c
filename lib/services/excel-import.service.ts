import * as XLSX from 'xlsx'
import { supabase } from '@/lib/supabase'
import { 
  ExcelRowData, 
  ExcelColumnMapping, 
  ImportValidationError, 
  ImportResult,
  EntityAutoCreateResult,
  ImportPreviewData,
  DEFAULT_COLUMN_MAPPINGS,
  REQUIRED_FIELDS
} from '@/lib/types/excel-import'
import { LpkMitra, Kumiai, PerusahaanPenerima } from '@/lib/types/database'

export class ExcelImportService {
  /**
   * Read Excel file and convert to JSON data
   */
  static async readExcelFile(file: File): Promise<{ headers: string[], data: any[] }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          
          // Convert to JSON with headers
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          if (jsonData.length === 0) {
            reject(new Error('File Excel kosong'))
            return
          }
          
          const headers = jsonData[0] as string[]
          const rows = jsonData.slice(1).filter((row: any) => row && Array.isArray(row) && row.some((cell: any) => cell !== undefined && cell !== ''))
          
          resolve({
            headers,
            data: rows
          })
        } catch (error) {
          reject(new Error(`Error membaca file Excel: ${error}`))
        }
      }
      
      reader.onerror = () => reject(new Error('Error membaca file'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * Auto-detect column mapping based on headers
   */
  static detectColumnMapping(headers: string[]): ExcelColumnMapping {
    let bestMapping: ExcelColumnMapping = {}
    let bestScore = 0
    
    // Try each predefined mapping
    Object.entries(DEFAULT_COLUMN_MAPPINGS).forEach(([mappingName, mapping]) => {
      const score = this.calculateMappingScore(headers, mapping)
      if (score > bestScore) {
        bestScore = score
        bestMapping = mapping
      }
    })
    
    // Filter mapping to only include columns that exist in headers
    const filteredMapping: ExcelColumnMapping = {}
    headers.forEach(header => {
      if (bestMapping[header]) {
        filteredMapping[header] = bestMapping[header]
      }
    })
    
    return filteredMapping
  }
  
  private static calculateMappingScore(headers: string[], mapping: ExcelColumnMapping): number {
    let score = 0
    headers.forEach(header => {
      if (mapping[header]) {
        score += 1
        // Give extra score for required fields
        if (REQUIRED_FIELDS.includes(mapping[header])) {
          score += 2
        }
      }
    })
    return score
  }

  /**
   * Convert raw Excel data to typed ExcelRowData
   */
  static convertToExcelRowData(headers: string[], rows: any[], mapping: ExcelColumnMapping): ExcelRowData[] {
    return rows.map(row => {
      const rowData: Partial<ExcelRowData> = {}
      
      headers.forEach((header, index) => {
        const field = mapping[header]
        if (field && row[index] !== undefined && row[index] !== '') {
          rowData[field] = this.convertCellValue(field, row[index])
        }
      })
      
      return rowData as ExcelRowData
    })
  }

  private static convertCellValue(field: keyof ExcelRowData, value: any): any {
    if (value === undefined || value === null || value === '') {
      return undefined
    }

    // Date fields
    if (field.includes('tanggal_') || field === 'tanggal_lahir') {
      if (typeof value === 'number') {
        // Excel date serial number
        const date = XLSX.SSF.parse_date_code(value)
        return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`
      }
      if (typeof value === 'string') {
        const parsed = new Date(value)
        if (!isNaN(parsed.getTime())) {
          return parsed.toISOString().split('T')[0]
        }
      }
      return value
    }

    // Number fields
    if (field === 'tahun_lulus' || field === 'gaji_aktual') {
      const num = Number(value)
      return isNaN(num) ? value : num
    }

    // Gender normalization
    if (field === 'jenis_kelamin') {
      const gender = String(value).toUpperCase()
      if (gender === 'LAKI-LAKI' || gender === 'L' || gender === 'MALE') return 'L'
      if (gender === 'PEREMPUAN' || gender === 'P' || gender === 'FEMALE') return 'P'
      return value
    }

    // Education level normalization
    if (field === 'pendidikan_terakhir') {
      const education = String(value).toUpperCase()
      if (education.includes('SMA') || education.includes('SMU')) return 'SMA'
      if (education.includes('SMK') || education.includes('STM')) return 'SMK'
      if (education.includes('SMP') || education.includes('SLTP')) return 'SMP'
      if (education.includes('SD') || education.includes('SLTA')) return 'SD'
      if (education.includes('D3') || education.includes('DIPLOMA')) return 'D3'
      if (education.includes('S1') || education.includes('SARJANA')) return 'S1'
      return value
    }

    return String(value).trim()
  }

  /**
   * Validate Excel data
   */
  static validateExcelData(data: ExcelRowData[]): ImportValidationError[] {
    const errors: ImportValidationError[] = []
    
    data.forEach((row, index) => {
      const rowNumber = index + 2 // Excel row number (header + 1-based)
      
      // Check required fields
      REQUIRED_FIELDS.forEach(field => {
        if (!row[field] || String(row[field]).trim() === '') {
          errors.push({
            row: rowNumber,
            column: field,
            field,
            value: row[field],
            error: `Field ${field} wajib diisi`,
            severity: 'error'
          })
        }
      })
      
      // Validate NIK (should be 16 digits)
      if (row.nik && !/^\d{16}$/.test(String(row.nik))) {
        errors.push({
          row: rowNumber,
          column: 'nik',
          field: 'nik',
          value: row.nik,
          error: 'NIK harus 16 digit angka',
          severity: 'error'
        })
      }
      
      // Validate gender
      if (row.jenis_kelamin && !['L', 'P'].includes(row.jenis_kelamin)) {
        errors.push({
          row: rowNumber,
          column: 'jenis_kelamin',
          field: 'jenis_kelamin',
          value: row.jenis_kelamin,
          error: 'Jenis kelamin harus L atau P',
          severity: 'error'
        })
      }
      
      // Validate education level
      if (row.pendidikan_terakhir && !['SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1'].includes(row.pendidikan_terakhir)) {
        errors.push({
          row: rowNumber,
          column: 'pendidikan_terakhir',
          field: 'pendidikan_terakhir',
          value: row.pendidikan_terakhir,
          error: 'Pendidikan terakhir harus SD, SMP, SMA, SMK, D3, atau S1',
          severity: 'error'
        })
      }
      
      // Validate phone number
      if (row.nomor_hp && !/^(\+62|62|0)[0-9]{8,12}$/.test(String(row.nomor_hp).replace(/[\s-]/g, ''))) {
        errors.push({
          row: rowNumber,
          column: 'nomor_hp',
          field: 'nomor_hp',
          value: row.nomor_hp,
          error: 'Format nomor HP tidak valid',
          severity: 'warning'
        })
      }
      
      // Validate email
      if (row.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(row.email))) {
        errors.push({
          row: rowNumber,
          column: 'email',
          field: 'email',
          value: row.email,
          error: 'Format email tidak valid',
          severity: 'warning'
        })
      }
      
      // Validate graduation year
      if (row.tahun_lulus) {
        const year = Number(row.tahun_lulus)
        const currentYear = new Date().getFullYear()
        if (isNaN(year) || year < 1950 || year > currentYear + 5) {
          errors.push({
            row: rowNumber,
            column: 'tahun_lulus',
            field: 'tahun_lulus',
            value: row.tahun_lulus,
            error: `Tahun lulus tidak valid (${year})`,
            severity: 'warning'
          })
        }
      }
    })
    
    return errors
  }

  /**
   * Create preview data for user confirmation
   */
  static async createPreviewData(file: File): Promise<ImportPreviewData> {
    const { headers, data } = await this.readExcelFile(file)
    const mapping = this.detectColumnMapping(headers)
    const convertedData = this.convertToExcelRowData(headers, data, mapping)
    const errors = this.validateExcelData(convertedData)
    
    const errorRows = new Set(errors.filter(e => e.severity === 'error').map(e => e.row))
    const warningRows = new Set(errors.filter(e => e.severity === 'warning').map(e => e.row))
    
    return {
      headers,
      data: convertedData,
      mapping,
      errors,
      statistics: {
        totalRows: convertedData.length,
        validRows: convertedData.length - errorRows.size,
        errorRows: errorRows.size,
        warningRows: warningRows.size
      }
    }
  }



  /**
   * Find or create LPK entity
   */
  static async findOrCreateLPK(lpkData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult> {
    const { nama_lpk, alamat_lpk, kota_lpk, provinsi_lpk, kontak_person_lpk, nomor_telepon_lpk, email_lpk } = lpkData
    
    // LPK name is required now
    if (!nama_lpk?.trim()) {
      throw new Error('Nama LPK harus diisi')
    }

    // Try to find existing LPK by name
    const { data: existingLPK } = await supabase
      .from('lpk_mitra')
      .select('id, nama_lpk')
      .ilike('nama_lpk', nama_lpk)
      .single()

    if (existingLPK) {
      return {
        id: existingLPK.id,
        name: existingLPK.nama_lpk,
        isNew: false
      }
    }

    // Create new LPK
    const newLPK: Omit<LpkMitra, 'id' | 'created_at' | 'updated_at'> = {
      nama_lpk,
      alamat_lengkap: alamat_lpk || 'Alamat tidak tersedia',
      kota: kota_lpk || 'Kota tidak tersedia',
      provinsi: provinsi_lpk || 'Provinsi tidak tersedia',
      nama_pimpinan: 'Belum diisi',
      kontak_person: kontak_person_lpk || 'Belum diisi',
      nomor_telepon: nomor_telepon_lpk || '-',
      email: email_lpk || '',
      website: undefined,
      status: 'aktif',
      tanggal_kerjasama: undefined,
      catatan: 'Auto-created dari import Excel'
    }

    const { data: createdLPK, error } = await supabase
      .from('lpk_mitra')
      .insert(newLPK)
      .select('id, nama_lpk')
      .single()

    if (error) {
      throw new Error(`Gagal membuat LPK baru: ${error.message}`)
    }

    return {
      id: createdLPK.id,
      name: createdLPK.nama_lpk,
      isNew: true
    }
  }

  /**
   * Find or create Kumiai entity
   */
  static async findOrCreateKumiai(kumiaiData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult | null> {
    const { nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur, kontak_person_kumiai, nomor_telepon_kumiai, email_kumiai } = kumiaiData
    
    if (!nama_kumiai && !kode_kumiai) {
      return null // Kumiai is optional
    }

    // Try to find existing Kumiai by name or code
    let query = supabase.from('kumiai').select('id, nama_kumiai')
    
    if (kode_kumiai) {
      query = query.eq('kode_kumiai', kode_kumiai)
    } else {
      query = query.ilike('nama_kumiai', nama_kumiai!)
    }

    const { data: existingKumiai } = await query.single()

    if (existingKumiai) {
      return {
        id: existingKumiai.id,
        name: existingKumiai.nama_kumiai,
        isNew: false
      }
    }

    // Create new Kumiai
    const newKumiai: Omit<Kumiai, 'id' | 'created_at' | 'updated_at'> = {
      nama_kumiai: nama_kumiai || 'Kumiai dari Import',
      kode_kumiai: kode_kumiai || `IMPORT_${Date.now()}`,
      alamat_jepang: alamat_jepang || 'Alamat Jepang tidak tersedia',
      kota_jepang: kota_jepang || 'Kota tidak tersedia',
      prefektur: prefektur || 'Prefektur tidak tersedia',
      kontak_person: kontak_person_kumiai || 'Belum diisi',
      nomor_telepon: nomor_telepon_kumiai || '-',
      email: email_kumiai || '',
      website: undefined,
      status: 'aktif',
      keterangan: 'Auto-created dari import Excel'
    }

    const { data: createdKumiai, error } = await supabase
      .from('kumiai')
      .insert(newKumiai)
      .select('id, nama_kumiai')
      .single()

    if (error) {
      throw new Error(`Gagal membuat Kumiai baru: ${error.message}`)
    }

    return {
      id: createdKumiai.id,
      name: createdKumiai.nama_kumiai,
      isNew: true
    }
  }

  /**
   * Find or create Perusahaan entity
   */
  static async findOrCreatePerusahaan(perusahaanData: Partial<ExcelRowData>, kumiaiId?: string): Promise<EntityAutoCreateResult | null> {
    const { nama_perusahaan, alamat_perusahaan, kota_perusahaan, prefektur_perusahaan, bidang_usaha, kontak_person_perusahaan, nomor_telepon_perusahaan, email_perusahaan } = perusahaanData
    
    if (!nama_perusahaan || !kumiaiId) {
      return null // Perusahaan is optional, requires Kumiai
    }

    // Try to find existing Perusahaan by name
    const { data: existingPerusahaan } = await supabase
      .from('perusahaan_penerima')
      .select('id, nama_perusahaan')
      .ilike('nama_perusahaan', nama_perusahaan)
      .single()

    if (existingPerusahaan) {
      return {
        id: existingPerusahaan.id,
        name: existingPerusahaan.nama_perusahaan,
        isNew: false
      }
    }

    // Create new Perusahaan
    const newPerusahaan: Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'> = {
      kumiai_id: kumiaiId,
      nama_perusahaan,
      alamat_jepang: alamat_perusahaan || 'Alamat tidak tersedia',
      kota_jepang: kota_perusahaan || 'Kota tidak tersedia',
      prefektur: prefektur_perusahaan || 'Prefektur tidak tersedia',
      bidang_usaha: bidang_usaha || 'Belum diisi',
      kontak_person: kontak_person_perusahaan || 'Belum diisi',
      nomor_telepon: nomor_telepon_perusahaan || '-',
      email: email_perusahaan || '',
      website: undefined,
      status: 'aktif',
      keterangan: 'Auto-created dari import Excel'
    }

    const { data: createdPerusahaan, error } = await supabase
      .from('perusahaan_penerima')
      .insert(newPerusahaan)
      .select('id, nama_perusahaan')
      .single()

    if (error) {
      throw new Error(`Gagal membuat Perusahaan baru: ${error.message}`)
    }

    return {
      id: createdPerusahaan.id,
      name: createdPerusahaan.nama_perusahaan,
      isNew: true
    }
  }

  /**
   * Import Excel data to database
   */
  static async importData(previewData: ImportPreviewData): Promise<ImportResult> {
    const { data, errors } = previewData
    const result: ImportResult = {
      success: false,
      totalRows: data.length,
      successfulImports: 0,
      errors: [...errors],
      warnings: [],
      createdEntities: {
        lpk: 0,
        kumiai: 0,
        perusahaan: 0,
        siswa: 0,
        penempatan: 0
      },
      message: ''
    }

    // Only process rows without critical errors
    const validRows = data.filter((_, index) => {
      const rowNumber = index + 2
      return !errors.some(e => e.row === rowNumber && e.severity === 'error')
    })

    if (validRows.length === 0) {
      result.message = 'Tidak ada data valid untuk diimport'
      return result
    }

    const processedEntities = {
      lpk: new Map<string, string>(),
      kumiai: new Map<string, string>(),
      perusahaan: new Map<string, string>()
    }

    try {
      for (const rowData of validRows) {
        try {
          // 1. Find or create LPK
          const lpkResult = await this.findOrCreateLPK(rowData)
          if (lpkResult.isNew && !processedEntities.lpk.has(lpkResult.name)) {
            result.createdEntities.lpk++
            processedEntities.lpk.set(lpkResult.name, lpkResult.id)
          }

          // 2. Find or create Kumiai (optional)
          let kumiaiResult = null
          if (rowData.nama_kumiai || rowData.kode_kumiai) {
            kumiaiResult = await this.findOrCreateKumiai(rowData)
            if (kumiaiResult?.isNew && !processedEntities.kumiai.has(kumiaiResult.name)) {
              result.createdEntities.kumiai++
              processedEntities.kumiai.set(kumiaiResult.name, kumiaiResult.id)
            }
          }

          // 3. Find or create Perusahaan (optional)
          let perusahaanResult = null
          if (rowData.nama_perusahaan && kumiaiResult) {
            perusahaanResult = await this.findOrCreatePerusahaan(rowData, kumiaiResult.id)
            if (perusahaanResult?.isNew && !processedEntities.perusahaan.has(perusahaanResult.name)) {
              result.createdEntities.perusahaan++
              processedEntities.perusahaan.set(perusahaanResult.name, perusahaanResult.id)
            }
          }

          // 4. Create Siswa
          const siswaData = {
            lpk_id: lpkResult.id,
            nama_lengkap: rowData.nama_lengkap,
            nik: rowData.nik,
            tempat_lahir: rowData.tempat_lahir,
            tanggal_lahir: rowData.tanggal_lahir,
            jenis_kelamin: rowData.jenis_kelamin,
            agama: rowData.agama,
            status_pernikahan: rowData.status_pernikahan || 'belum_menikah',
            alamat_lengkap: rowData.alamat_lengkap,
            kelurahan: rowData.kelurahan,
            kecamatan: rowData.kecamatan,
            kota_kabupaten: rowData.kota_kabupaten,
            provinsi: rowData.provinsi,
            kode_pos: rowData.kode_pos,
            nomor_hp: rowData.nomor_hp,
            email: rowData.email,
            pendidikan_terakhir: rowData.pendidikan_terakhir,
            nama_sekolah: rowData.nama_sekolah,
            tahun_lulus: rowData.tahun_lulus,
            jurusan: rowData.jurusan,
            nama_ayah: rowData.nama_ayah,
            nama_ibu: rowData.nama_ibu,
            alamat_keluarga: rowData.alamat_keluarga,
            nomor_hp_keluarga: rowData.nomor_hp_keluarga,
            status_pendaftaran: rowData.status_pendaftaran || 'approved',
            tanggal_daftar: rowData.tanggal_daftar || new Date().toISOString().split('T')[0],
            catatan: rowData.catatan
          }

          const { data: createdSiswa, error: siswaError } = await supabase
            .from('siswa')
            .insert(siswaData)
            .select('id')
            .single()

          if (siswaError) {
            throw new Error(`Gagal membuat siswa: ${siswaError.message}`)
          }

          result.createdEntities.siswa++

          // 5. Create Penempatan (optional)
          if (rowData.tanggal_penempatan && perusahaanResult && kumiaiResult) {
            const penempatanData = {
              siswa_id: createdSiswa.id,
              perusahaan_id: perusahaanResult.id,
              kumiai_id: kumiaiResult.id,
              tanggal_penempatan: rowData.tanggal_penempatan,
              tanggal_keberangkatan: rowData.tanggal_keberangkatan,
              tanggal_kepulangan: rowData.tanggal_kepulangan,
              status_penempatan: rowData.status_penempatan || 'ditempatkan',
              posisi_kerja: rowData.posisi_kerja || 'Staff',
              gaji_aktual: rowData.gaji_aktual || 0,
              alamat_kerja: rowData.alamat_perusahaan || 'Alamat kerja tidak tersedia'
            }

            const { error: penempatanError } = await supabase
              .from('penempatan_siswa')
              .insert(penempatanData)

            if (penempatanError) {
              // Log warning but don't fail the import
              result.warnings.push({
                row: data.indexOf(rowData) + 2,
                column: 'penempatan',
                field: 'tanggal_penempatan',
                value: rowData.tanggal_penempatan,
                error: `Gagal membuat penempatan: ${penempatanError.message}`,
                severity: 'warning'
              })
            } else {
              result.createdEntities.penempatan++
            }
          }

          result.successfulImports++
        } catch (error) {
          result.errors.push({
            row: data.indexOf(rowData) + 2,
            column: 'general',
            field: 'nama_lengkap',
            value: rowData.nama_lengkap,
            error: `Error memproses baris: ${error}`,
            severity: 'error'
          })
        }
      }

      result.success = result.successfulImports > 0
      result.message = `Import selesai: ${result.successfulImports}/${result.totalRows} data berhasil diimport`

    } catch (error) {
      result.success = false
      result.message = `Error saat import: ${error}`
    }

    return result
  }

  static downloadTemplate(): void {
    // Buat workbook baru
    const wb = XLSX.utils.book_new();

    // Header dengan semua kolom termasuk LPK, Kumiai, Perusahaan, Penempatan
    const headers = [
      // Data Pribadi
      'nama_lengkap', 'nik', 'tanggal_lahir', 'tempat_lahir', 'jenis_kelamin',
      'agama', 'alamat', 'provinsi', 'kota_kabupaten', 'kecamatan', 'kelurahan',
      'kode_pos', 'nomor_telepon', 'email',
      
      // Pendidikan
      'pendidikan_terakhir', 'nama_sekolah', 'jurusan', 'tahun_lulus',
      
      // Data Keluarga
      'nama_ayah', 'nama_ibu', 'nomor_telepon_orangtua', 'alamat_orangtua',
      'pekerjaan_ayah', 'pekerjaan_ibu', 'penghasilan_orangtua',
      
      // Data LPK, Kumiai, Perusahaan, Penempatan
      'nama_lpk', 'alamat_lpk', 'nomor_telepon_lpk',
      'nama_kumiai', 'alamat_kumiai', 'nomor_telepon_kumiai',
      'nama_perusahaan', 'alamat_perusahaan', 'nomor_telepon_perusahaan',
      'prefecture', 'kota_penempatan', 'tanggal_mulai_penempatan', 'status_penempatan'
    ];

    // Data contoh
    const sampleData = [
      [
        // Data Pribadi
        'Ahmad Susanto', '1234567890123456', '2000-01-15', 'Jakarta', 'L',
        'Islam', 'Jl. Merdeka No. 123', 'DKI Jakarta', 'Jakarta Pusat', 'Gambir', 'Gambir',
        '10110', '081234567890', '<EMAIL>',
        
        // Pendidikan
        'SMA', 'SMA Negeri 1 Jakarta', 'IPA', '2018',
        
        // Data Keluarga
        'Budi Susanto', 'Siti Susanti', '0819********', 'Jl. Merdeka No. 123',
        'Pegawai Swasta', 'Ibu Rumah Tangga', '5000000',
        
        // Data LPK, Kumiai, Perusahaan, Penempatan
        'LPK Nusantara Jaya', 'Jl. Raya Jakarta No. 100', '021-12345678',
        'Kumiai Tokyo Utara', 'Tokyo, Jepang', '+81-3-1234-5678',
        'PT. Yamaha Motor Indonesia', 'Pulogadung, Jakarta', '021-********',
        'Tokyo', 'Shibuya', '2024-04-01', 'aktif'
      ],
      [
        // Data Pribadi
        'Sari Wahyuni', '2345678901234567', '1999-05-22', 'Bandung', 'P',
        'Islam', 'Jl. Sudirman No. 456', 'Jawa Barat', 'Bandung', 'Coblong', 'Dago',
        '40135', '081345678901', '<EMAIL>',
        
        // Pendidikan
        'SMK', 'SMK Negeri 2 Bandung', 'Teknik Informatika', '2017',
        
        // Data Keluarga
        'Andi Wahyudi', 'Rina Sari', '081********0', 'Jl. Sudirman No. 456',
        'Wiraswasta', 'Guru', '7500000',
        
        // Data LPK, Kumiai, Perusahaan, Penempatan
        'LPK Sukses Mandiri', 'Jl. Asia Afrika No. 200', '022-98765432',
        'Kumiai Osaka Selatan', 'Osaka, Jepang', '+81-6-9876-5432',
        'Honda Manufacturing Indonesia', 'Karawang, Jawa Barat', '0267-123456',
        'Osaka', 'Namba', '2024-06-15', 'aktif'
      ],
      [
        // Data Pribadi
        'Budi Pratama', '****************', '1998-12-08', 'Surabaya', 'L',
        'Kristen', 'Jl. Pemuda No. 789', 'Jawa Timur', 'Surabaya', 'Gubeng', 'Airlangga',
        '60286', '************', '<EMAIL>',
        
        // Pendidikan
        'SMK', 'SMK Negeri 5 Surabaya', 'Teknik Mesin', '2016',
        
        // Data Keluarga
        'Surya Pratama', 'Dewi Sari', '************', 'Jl. Pemuda No. 789',
        'PNS', 'Pegawai Bank', '8500000',
        
        // Data LPK, Kumiai, Perusahaan, Penempatan
        'LPK Harapan Bangsa', 'Jl. Diponegoro No. 50', '031-********',
        'Kumiai Kyoto Tengah', 'Kyoto, Jepang', '+81-75-1234-5678',
        'Denso Indonesia', 'Bekasi, Jawa Barat', '021-********',
        'Kyoto', 'Fushimi', '2024-08-20', 'aktif'
      ]
    ];

    // Kombinasi header dan data
    const wsData = [headers, ...sampleData];

    // Buat worksheet
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // Set lebar kolom agar mudah dibaca
    const colWidths = headers.map(() => ({ wch: 20 }));
    ws['!cols'] = colWidths;

    // Tambahkan worksheet ke workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Template Import Siswa');

    // Download sebagai Excel
    XLSX.writeFile(wb, 'template_import_siswa.xlsx');
  }
} 