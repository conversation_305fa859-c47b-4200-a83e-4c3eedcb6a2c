import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { JobOrder } from '@/lib/types/database'

export class JobOrderService {
  // Get all Job Orders with optional filtering
  static async getAll(filters?: {
    status?: string
    perusahaan_id?: string
    kumiai_id?: string
    bidang_kerja?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(nama_perusahaan, prefektur),
        kumiai(nama_kumiai, kode_kumiai)
      `)

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.perusahaan_id) {
      query = query.eq('perusahaan_id', filters.perusahaan_id)
    }

    if (filters?.kumiai_id) {
      query = query.eq('kumiai_id', filters.kumiai_id)
    }

    if (filters?.bidang_kerja) {
      query = query.eq('bidang_kerja', filters.bidang_kerja)
    }

    if (filters?.search) {
      query = query.or(`judul_pekerjaan.ilike.%${filters.search}%,posisi.ilike.%${filters.search}%,bidang_kerja.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Job Order by ID with related data
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(*),
        kumiai(*),
        penempatan_siswa(
          id,
          siswa_id,
          status_penempatan,
          siswa(nama_lengkap, lpk_id, lpk_mitra(nama_lpk))
        )
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Job Order
  static async create(jobOrderData: Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('job_order')
      .insert(jobOrderData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Job Order
  static async update(id: string, jobOrderData: Partial<Omit<JobOrder, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('job_order')
      .update({
        ...jobOrderData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Job Order
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('job_order')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Job Order statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })

    const { data: publishedData, error: publishedError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })
      .eq('status', 'published')

    const { data: closedData, error: closedError } = await supabase
      .from('job_order')
      .select('id', { count: 'exact' })
      .eq('status', 'closed')

    if (totalError || publishedError || closedError) {
      throw new Error('Failed to fetch Job Order statistics')
    }

    return {
      total: totalData?.length || 0,
      published: publishedData?.length || 0,
      closed: closedData?.length || 0,
      draft: (totalData?.length || 0) - (publishedData?.length || 0) - (closedData?.length || 0)
    }
  }

  // Get Job Orders with placement statistics
  static async getWithPlacementStats() {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        *,
        perusahaan_penerima(nama_perusahaan),
        kumiai(nama_kumiai),
        penempatan_siswa(count)
      `)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique bidang_kerja for filter options
  static async getUniqueBidangKerja() {
    const { data, error } = await supabase
      .from('job_order')
      .select('bidang_kerja')
      .order('bidang_kerja')

    if (error) {
      throw new Error('Failed to fetch bidang kerja')
    }

    const uniqueBidangKerja = [...new Set(data?.map(item => item.bidang_kerja) || [])]
    return uniqueBidangKerja
  }

  // Get active Job Orders for placement
  static async getActiveJobOrders() {
    const { data, error } = await supabase
      .from('job_order')
      .select(`
        id,
        judul_pekerjaan,
        posisi,
        jumlah_kuota,
        kuota_terisi,
        perusahaan_penerima(nama_perusahaan),
        kumiai(nama_kumiai)
      `)
      .eq('status', 'published')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error('Failed to get active job orders')
    }

    // Filter job orders that still have available quota
    const availableJobOrders = data?.filter(job => job.kuota_terisi < job.jumlah_kuota) || []

    return formatSupabaseResponse(availableJobOrders, null)
  }

  // Update quota when student is placed
  static async updateQuota(id: string, increment: number = 1) {
    // First get current quota
    const { data: currentJob, error: getCurrentError } = await supabase
      .from('job_order')
      .select('kuota_terisi')
      .eq('id', id)
      .single()

    if (getCurrentError) {
      throw new Error('Failed to get current quota')
    }

    const { data, error } = await supabase
      .from('job_order')
      .update({
        kuota_terisi: currentJob.kuota_terisi + increment,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get available students for job order (not yet departed to Japan)
  static async getAvailableStudents(jobOrderId?: string) {
    // Get the final stage ID (Pemberangkatan ke Jepang)
    const { data: finalStage, error: stageError } = await supabase
      .from('timeline_stages')
      .select('id')
      .eq('nama_stage', 'Pemberangkatan ke Jepang')
      .single()

    if (stageError) {
      throw new Error('Failed to get final timeline stage')
    }

    // Get students who haven't completed the final stage (not departed yet)
    const { data: availableStudents, error: studentsError } = await supabase
      .from('siswa')
      .select(`
        *,
        lpk_mitra(nama_lpk, kota),
        siswa_timeline_progress!inner(
          id,
          status,
          timeline_stage_id
        )
      `)
      .eq('status_pendaftaran', 'approved')
      .eq('siswa_timeline_progress.timeline_stage_id', finalStage.id)
      .neq('siswa_timeline_progress.status', 'selesai')
      .order('created_at', { ascending: false })

    if (studentsError) {
      throw new Error('Failed to get available students')
    }

    // If jobOrderId is provided, also exclude students already assigned to this job order
    if (jobOrderId) {
      const assignedStudentIds = availableStudents
        .filter(student => student.penempatan_siswa?.some((p: any) => p.job_order_id === jobOrderId))
        .map(student => student.id)

      return formatSupabaseResponse(
        availableStudents.filter(student => !assignedStudentIds.includes(student.id)),
        null
      )
    }

    return formatSupabaseResponse(availableStudents, null)
  }

  // Get students assigned to a job order
  static async getAssignedStudents(jobOrderId: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .select(`
        *,
        siswa(
          *,
          lpk_mitra(nama_lpk)
        )
      `)
      .eq('job_order_id', jobOrderId)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Assign students to job order
  static async assignStudents(jobOrderId: string, studentIds: string[]) {
    // Get job order details
    const jobOrder = await this.getById(jobOrderId)
    if (!jobOrder) {
      throw new Error('Job Order not found')
    }

    // Check if quota is available
    const availableQuota = jobOrder.jumlah_kuota - jobOrder.kuota_terisi
    if (studentIds.length > availableQuota) {
      throw new Error(`Not enough quota. Available: ${availableQuota}, Requested: ${studentIds.length}`)
    }

    // Create penempatan records for each student
    const penempatanData = studentIds.map(siswaId => ({
      siswa_id: siswaId,
      job_order_id: jobOrderId,
      perusahaan_id: jobOrder.perusahaan_id,
      kumiai_id: jobOrder.kumiai_id,
      tanggal_penempatan: new Date().toISOString().split('T')[0],
      status_penempatan: 'ditempatkan',
      posisi_kerja: jobOrder.posisi,
      gaji_aktual: jobOrder.gaji_pokok + jobOrder.tunjangan,
      alamat_kerja: jobOrder.perusahaan_penerima?.alamat_jepang || '',
    }))

    const { data, error } = await supabase
      .from('penempatan_siswa')
      .insert(penempatanData)
      .select()

    if (error) {
      throw new Error(`Failed to assign students: ${error.message}`)
    }

    // Update job order quota
    await this.updateQuota(jobOrderId, studentIds.length)

    // Progress timeline for assigned students
    await this.progressStudentTimelines(studentIds, 'assigned_to_job_order')

    return formatSupabaseResponse(data, null)
  }

  // Remove student from job order
  static async removeStudent(jobOrderId: string, siswaId: string) {
    const { data, error } = await supabase
      .from('penempatan_siswa')
      .delete()
      .eq('job_order_id', jobOrderId)
      .eq('siswa_id', siswaId)
      .select()

    if (error) {
      throw new Error(`Failed to remove student: ${error.message}`)
    }

    // Update job order quota (decrease by 1)
    await this.updateQuota(jobOrderId, -1)

    return formatSupabaseResponse(data, null)
  }

  // Get job order with timeline statistics
  static async getJobOrderWithTimelineStats(jobOrderId: string) {
    const jobOrder = await this.getById(jobOrderId)
    if (!jobOrder) {
      throw new Error('Job Order not found')
    }

    const assignedStudents = await this.getAssignedStudents(jobOrderId)
    
    // Get timeline statistics for assigned students
    const timelineStats = await Promise.all(
      assignedStudents.map(async (placement: any) => {
        const { data: timelineData, error } = await supabase
          .from('v_siswa_timeline_progress')
          .select('nama_stage, status, siswa_id, nama_lengkap')
          .eq('siswa_id', placement.siswa_id)
          .order('urutan', { ascending: true })

        if (error) {
          console.error('Error fetching timeline for student:', placement.siswa_id)
          return null
        }

        const completedStages = timelineData?.filter(stage => stage.status === 'selesai').length || 0
        const totalStages = timelineData?.length || 0
        const currentStage = timelineData?.find(stage => stage.status === 'berlangsung')?.nama_stage
        
        return {
          siswa_id: placement.siswa_id,
          nama_lengkap: placement.siswa?.nama_lengkap,
          progress_percentage: Math.round((completedStages / totalStages) * 100),
          current_stage: currentStage || 'Belum dimulai',
          completed_stages: completedStages,
          total_stages: totalStages
        }
      })
    )

    return {
      jobOrder,
      assignedStudents,
      timelineStats: timelineStats.filter(stat => stat !== null)
    }
  }

  // Progress student timelines based on job order assignment
  static async progressStudentTimelines(studentIds: string[], action: 'assigned_to_job_order' | 'departed_to_japan') {
    try {
      // Get relevant timeline stages
      const { data: stages, error: stageError } = await supabase
        .from('timeline_stages')
        .select('id, nama_stage, urutan')
        .order('urutan', { ascending: true })

      if (stageError) {
        console.error('Error fetching timeline stages:', stageError)
        return
      }

      // Define stage progression rules
      const stageProgressions = {
        'assigned_to_job_order': {
          // When assigned to job order, progress to "Seleksi Administrasi" if not already there
          targetStage: 'Seleksi Administrasi',
          note: 'Siswa telah ditugaskan ke job order dan siap untuk seleksi administrasi'
        },
        'departed_to_japan': {
          // When departed to Japan, complete the final stage
          targetStage: 'Pemberangkatan ke Jepang',
          note: 'Siswa telah berangkat ke Jepang'
        }
      }

      const progression = stageProgressions[action]
      const targetStage = stages?.find(stage => stage.nama_stage === progression.targetStage)

      if (!targetStage) {
        console.error(`Target stage "${progression.targetStage}" not found`)
        return
      }

      // Update timeline progress for each student
      const updatePromises = studentIds.map(async (siswaId) => {
        try {
          // Get current progress
          const { data: currentProgress, error: progressError } = await supabase
            .from('siswa_timeline_progress')
            .select('*')
            .eq('siswa_id', siswaId)
            .eq('timeline_stage_id', targetStage.id)
            .single()

          if (progressError && progressError.code !== 'PGRST116') {
            console.error(`Error fetching progress for student ${siswaId}:`, progressError)
            return
          }

          // Update or insert progress
          const progressData = {
            siswa_id: siswaId,
            timeline_stage_id: targetStage.id,
            status: action === 'departed_to_japan' ? 'selesai' : 'berlangsung',
            tanggal_mulai: action === 'departed_to_japan' ? currentProgress?.tanggal_mulai : new Date().toISOString().split('T')[0],
            tanggal_selesai: action === 'departed_to_japan' ? new Date().toISOString().split('T')[0] : null,
            catatan: progression.note,
            updated_at: new Date().toISOString()
          }

          const { error: updateError } = await supabase
            .from('siswa_timeline_progress')
            .upsert(progressData)

          if (updateError) {
            console.error(`Error updating timeline for student ${siswaId}:`, updateError)
          }
        } catch (error) {
          console.error(`Error processing timeline for student ${siswaId}:`, error)
        }
      })

      await Promise.all(updatePromises)
    } catch (error) {
      console.error('Error progressing student timelines:', error)
    }
  }
}
