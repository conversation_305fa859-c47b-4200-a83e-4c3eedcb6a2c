import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { PerusahaanPenerima } from '@/lib/types/database'

export class PerusahaanService {
  // Get all Perusahaan with optional filtering
  static async getAll(filters?: {
    status?: string
    kumiai_id?: string
    prefektur?: string
    bidang_usaha?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('perusahaan_penerima')
      .select(`
        *,
        kumiai(nama_kumiai, kode_kumiai)
      `)

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.kumiai_id) {
      query = query.eq('kumiai_id', filters.kumiai_id)
    }

    if (filters?.prefektur) {
      query = query.eq('prefektur', filters.prefektur)
    }

    if (filters?.bidang_usaha) {
      query = query.eq('bidang_usaha', filters.bidang_usaha)
    }

    if (filters?.search) {
      query = query.or(`nama_perusahaan.ilike.%${filters.search}%,kontak_person.ilike.%${filters.search}%,email.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Perusahaan by ID with related data
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .select(`
        *,
        kumiai(*),
        job_order(
          id,
          judul_pekerjaan,
          status,
          jumlah_kuota,
          kuota_terisi
        ),
        penempatan_siswa(
          id,
          status_penempatan,
          siswa(nama_lengkap)
        )
      `)
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Perusahaan
  static async create(perusahaanData: Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .insert(perusahaanData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Perusahaan
  static async update(id: string, perusahaanData: Partial<Omit<PerusahaanPenerima, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .update({
        ...perusahaanData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Perusahaan
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Perusahaan statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('perusahaan_penerima')
      .select('id', { count: 'exact' })

    const { data: activeData, error: activeError } = await supabase
      .from('perusahaan_penerima')
      .select('id', { count: 'exact' })
      .eq('status', 'aktif')

    if (totalError || activeError) {
      throw new Error('Failed to fetch Perusahaan statistics')
    }

    return {
      total: totalData?.length || 0,
      active: activeData?.length || 0,
      inactive: (totalData?.length || 0) - (activeData?.length || 0)
    }
  }

  // Get Perusahaan with job order count
  static async getWithJobOrderCount() {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .select(`
        *,
        kumiai(nama_kumiai),
        job_order(count)
      `)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique prefectures for filter options
  static async getUniquePrefectures() {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .select('prefektur')
      .order('prefektur')

    if (error) {
      throw new Error('Failed to fetch prefectures')
    }

    const uniquePrefectures = [...new Set(data?.map(item => item.prefektur) || [])]
    return uniquePrefectures
  }

  // Get unique bidang usaha for filter options
  static async getUniqueBidangUsaha() {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .select('bidang_usaha')
      .order('bidang_usaha')

    if (error) {
      throw new Error('Failed to fetch bidang usaha')
    }

    const uniqueBidangUsaha = [...new Set(data?.map(item => item.bidang_usaha) || [])]
    return uniqueBidangUsaha
  }

  // Get Perusahaan options for dropdowns
  static async getOptions(kumiaiId?: string) {
    let query = supabase
      .from('perusahaan_penerima')
      .select('id, nama_perusahaan, prefektur')
      .eq('status', 'aktif')
      .order('nama_perusahaan')

    if (kumiaiId) {
      query = query.eq('kumiai_id', kumiaiId)
    }

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Perusahaan by Kumiai
  static async getByKumiai(kumiaiId: string) {
    const { data, error } = await supabase
      .from('perusahaan_penerima')
      .select(`
        *,
        job_order(count),
        penempatan_siswa(count)
      `)
      .eq('kumiai_id', kumiaiId)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }
}
