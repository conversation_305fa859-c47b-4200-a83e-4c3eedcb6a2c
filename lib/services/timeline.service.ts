import { supabase, formatSupabaseResponse } from '@/lib/supabase'
import type { 
  TimelineStage, 
  SiswaTimelineProgress, 
  TimelineProgressWithStage,
  StudentTimelineView,
  TimelineStats,
  TimelineStageWithProgress
} from '@/lib/types/timeline'

export class TimelineService {
  // Get all timeline stages
  static async getTimelineStages() {
    const { data, error } = await supabase
      .from('timeline_stages')
      .select('*')
      .eq('is_aktif', true)
      .order('urutan', { ascending: true })

    return formatSupabaseResponse(data, error)
  }

  // Get student timeline progress
  static async getStudentTimeline(siswaId: string): Promise<TimelineStageWithProgress[]> {
    const { data, error } = await supabase
      .from('timeline_stages')
      .select(`
        *,
        siswa_timeline_progress!left(
          id,
          status,
          tanggal_mulai,
          tanggal_selesai,
          catatan,
          dokumen_pendukung,
          created_at,
          updated_at
        )
      `)
      .eq('is_aktif', true)
      .eq('siswa_timeline_progress.siswa_id', siswaId)
      .order('urutan', { ascending: true })

    if (error) {
      throw new Error(`Error fetching timeline: ${error.message}`)
    }

    // Transform the data to match our interface
    const timeline: TimelineStageWithProgress[] = (data || []).map((stage: any) => ({
      ...stage,
      progress: stage.siswa_timeline_progress?.[0] || null
    }))

    return timeline
  }

  // Get student timeline stats
  static async getStudentTimelineStats(siswaId: string): Promise<TimelineStats> {
    const timeline = await this.getStudentTimeline(siswaId)
    
    const totalStages = timeline.length
    const completedStages = timeline.filter(stage => stage.progress?.status === 'selesai').length
    const currentStage = timeline.find(stage => stage.progress?.status === 'berlangsung')
    const progressPercentage = Math.round((completedStages / totalStages) * 100)

    return {
      total_stages: totalStages,
      completed_stages: completedStages,
      current_stage: currentStage?.nama_stage,
      progress_percentage: progressPercentage
    }
  }

  // Update timeline progress
  static async updateTimelineProgress(
    siswaId: string, 
    timelineStageId: string, 
    updates: Partial<SiswaTimelineProgress>
  ) {
    const { data, error } = await supabase
      .from('siswa_timeline_progress')
      .upsert({
        siswa_id: siswaId,
        timeline_stage_id: timelineStageId,
        ...updates
      })
      .select()

    return formatSupabaseResponse(data, error)
  }

  // Initialize timeline for student (backup if trigger fails)
  static async initializeStudentTimeline(siswaId: string) {
    const { data, error } = await supabase.rpc('init_student_timeline', {
      student_id: siswaId
    })

    return formatSupabaseResponse(data, error)
  }

  // Mark stage as started
  static async startStage(siswaId: string, timelineStageId: string, catatan?: string) {
    return this.updateTimelineProgress(siswaId, timelineStageId, {
      status: 'berlangsung',
      tanggal_mulai: new Date().toISOString().split('T')[0],
      catatan
    })
  }

  // Mark stage as completed
  static async completeStage(siswaId: string, timelineStageId: string, catatan?: string) {
    return this.updateTimelineProgress(siswaId, timelineStageId, {
      status: 'selesai',
      tanggal_selesai: new Date().toISOString().split('T')[0],
      catatan
    })
  }

  // Mark stage as cancelled
  static async cancelStage(siswaId: string, timelineStageId: string, catatan?: string) {
    return this.updateTimelineProgress(siswaId, timelineStageId, {
      status: 'dibatalkan',
      catatan
    })
  }

  // Get detailed timeline progress view
  static async getDetailedTimelineProgress(siswaId: string) {
    const { data, error } = await supabase
      .from('v_siswa_timeline_progress')
      .select('*')
      .eq('siswa_id', siswaId)
      .order('urutan', { ascending: true })

    return formatSupabaseResponse(data, error)
  }

  // Update timeline with documents
  static async updateTimelineWithDocuments(
    siswaId: string, 
    timelineStageId: string, 
    dokumenIds: string[],
    catatan?: string
  ) {
    return this.updateTimelineProgress(siswaId, timelineStageId, {
      dokumen_pendukung: { document_ids: dokumenIds },
      catatan
    })
  }

  // Get students by timeline stage
  static async getStudentsByStage(stageId: string, status?: string) {
    let query = supabase
      .from('siswa_timeline_progress')
      .select(`
        *,
        siswa(
          id,
          nama_lengkap,
          nik,
          lpk_mitra(nama_lpk)
        ),
        timeline_stages(nama_stage, deskripsi)
      `)
      .eq('timeline_stage_id', stageId)

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Bulk update timeline progress
  static async bulkUpdateProgress(updates: Array<{
    siswa_id: string
    timeline_stage_id: string
    status: string
    catatan?: string
  }>) {
    const { data, error } = await supabase
      .from('siswa_timeline_progress')
      .upsert(updates)
      .select()

    return formatSupabaseResponse(data, error)
  }
} 