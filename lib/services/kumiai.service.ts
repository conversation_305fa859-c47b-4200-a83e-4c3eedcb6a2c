import { supabase, formatSupabaseResponse, formatSupabaseSingleResponse } from '@/lib/supabase'
import { Kumiai } from '@/lib/types/database'

export class KumiaiService {
  // Get all Kumiai with optional filtering
  static async getAll(filters?: {
    status?: string
    prefektur?: string
    search?: string
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('kumiai')
      .select('*')

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.prefektur) {
      query = query.eq('prefektur', filters.prefektur)
    }

    if (filters?.search) {
      query = query.or(`nama_kumiai.ilike.%${filters.search}%,kode_kumiai.ilike.%${filters.search}%,kontak_person.ilike.%${filters.search}%`)
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const from = (filters.page - 1) * filters.limit
      const to = from + filters.limit - 1
      query = query.range(from, to)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data, error } = await query

    return formatSupabaseResponse(data, error)
  }

  // Get Kumiai by ID
  static async getById(id: string) {
    const { data, error } = await supabase
      .from('kumiai')
      .select('*')
      .eq('id', id)
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Create new Kumiai
  static async create(kumiaiData: Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('kumiai')
      .insert(kumiaiData)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Update Kumiai
  static async update(id: string, kumiaiData: Partial<Omit<Kumiai, 'id' | 'created_at' | 'updated_at'>>) {
    const { data, error } = await supabase
      .from('kumiai')
      .update({
        ...kumiaiData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Delete Kumiai
  static async delete(id: string) {
    const { data, error } = await supabase
      .from('kumiai')
      .delete()
      .eq('id', id)
      .select()
      .single()

    return formatSupabaseSingleResponse(data, error)
  }

  // Get Kumiai statistics
  static async getStatistics() {
    const { data: totalData, error: totalError } = await supabase
      .from('kumiai')
      .select('id', { count: 'exact' })

    const { data: activeData, error: activeError } = await supabase
      .from('kumiai')
      .select('id', { count: 'exact' })
      .eq('status', 'aktif')

    if (totalError || activeError) {
      throw new Error('Failed to fetch Kumiai statistics')
    }

    return {
      total: totalData?.length || 0,
      active: activeData?.length || 0,
      inactive: (totalData?.length || 0) - (activeData?.length || 0)
    }
  }

  // Get Kumiai with company count
  static async getWithCompanyCount() {
    const { data, error } = await supabase
      .from('kumiai')
      .select(`
        *,
        perusahaan_penerima(count)
      `)
      .order('created_at', { ascending: false })

    return formatSupabaseResponse(data, error)
  }

  // Get unique prefectures for filter options
  static async getUniquePrefectures() {
    const { data, error } = await supabase
      .from('kumiai')
      .select('prefektur')
      .order('prefektur')

    if (error) {
      throw new Error('Failed to fetch prefectures')
    }

    const uniquePrefectures = [...new Set(data?.map(item => item.prefektur) || [])]
    return uniquePrefectures
  }

  // Get Kumiai options for dropdowns
  static async getOptions() {
    const { data, error } = await supabase
      .from('kumiai')
      .select('id, nama_kumiai, kode_kumiai')
      .eq('status', 'aktif')
      .order('nama_kumiai')

    return formatSupabaseResponse(data, error)
  }
}
