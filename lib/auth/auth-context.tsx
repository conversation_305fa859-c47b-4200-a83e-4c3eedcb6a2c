'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { RBACService } from '@/lib/services/rbac.service'
import type { UserProfile, UserPermissions, AccessControlContext } from '@/lib/types/rbac'
import { AuthLoading, AuthRedirect } from '@/components/auth/auth-loading'

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  permissions: UserPermissions
  accessContext: AccessControlContext | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  hasPermission: (module: string, action: string) => boolean
  hasRole: (roleName: string) => boolean
  refreshUserData: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [permissions, setPermissions] = useState<UserPermissions>({})
  const [accessContext, setAccessContext] = useState<AccessControlContext | null>(null)
  const [loading, setLoading] = useState(true)

  const loadUserData = async (currentUser: User) => {
    try {
      // Load user profile
      const profile = await RBACService.getUserById(currentUser.id)
      setUserProfile(profile)

      if (profile) {
        // Load permissions
        const userPermissions = await RBACService.getUserPermissions(currentUser.id)
        setPermissions(userPermissions)

        // Load access context
        const context = await RBACService.getAccessControlContext(currentUser.id)
        setAccessContext(context)
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const refreshUserData = async () => {
    if (user) {
      await loadUserData(user)
    }
  }

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      if (session?.user) {
        loadUserData(session.user)
      }
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null)
      
      if (session?.user) {
        await loadUserData(session.user)
      } else {
        setUserProfile(null)
        setPermissions({})
        setAccessContext(null)
      }
      
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  const hasPermission = (module: string, action: string): boolean => {
    return permissions[module]?.[action] === true
  }

  const hasRole = (roleName: string): boolean => {
    return accessContext?.roles.includes(roleName) === true
  }

  const value: AuthContextType = {
    user,
    userProfile,
    permissions,
    accessContext,
    loading,
    signIn,
    signOut,
    hasPermission,
    hasRole,
    refreshUserData,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission?: { module: string; action: string }
) {
  return function AuthenticatedComponent(props: P) {
    const { user, hasPermission, loading } = useAuth()

    if (loading) {
      return <AuthLoading message="Checking authentication..." />
    }

    if (!user) {
      // Don't redirect if we're already on login page
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
      return <AuthRedirect />
    }

    if (requiredPermission && !hasPermission(requiredPermission.module, requiredPermission.action)) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
            <button
              onClick={() => window.location.href = '/'}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Hook for permission-based rendering
export function usePermission(module: string, action: string) {
  const { hasPermission } = useAuth()
  return hasPermission(module, action)
}

// Hook for role-based rendering
export function useRole(roleName: string) {
  const { hasRole } = useAuth()
  return hasRole(roleName)
}
