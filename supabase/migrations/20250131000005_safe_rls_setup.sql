-- =====================================================
-- Safe RLS Setup - Handles Missing Columns/Tables
-- Dashboard Magang Jepang - Data Access Control
-- =====================================================

-- =====================================================
-- 1. Ensure all required columns exist
-- =====================================================

-- Add missing columns to user_profiles
DO $$
BEGIN
    -- Add lpk_mitra_id if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'lpk_mitra_id' AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN lpk_mitra_id UUID;
        -- Add foreign key constraint if lpk_mitra table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lpk_mitra' AND table_schema = 'public') THEN
            ALTER TABLE user_profiles ADD CONSTRAINT fk_user_profiles_lpk_mitra 
            FOREIGN KEY (lpk_mitra_id) REFERENCES lpk_mitra(id);
        END IF;
        RAISE NOTICE 'Added lpk_mitra_id column to user_profiles';
    END IF;
    
    -- Add phone if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'phone' AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN phone VARCHAR(20);
        RAISE NOTICE 'Added phone column to user_profiles';
    END IF;
    
    -- Add position if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'position' AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN position VARCHAR(100);
        RAISE NOTICE 'Added position column to user_profiles';
    END IF;
    
    -- Add department if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'department' AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN department VARCHAR(100);
        RAISE NOTICE 'Added department column to user_profiles';
    END IF;
END $$;

-- =====================================================
-- 2. Create safe helper functions
-- =====================================================

-- Safe function to check user role
CREATE OR REPLACE FUNCTION auth.user_has_role(role_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = role_name
        AND ura.is_active = true
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Safe function to get user's LPK Mitra ID
CREATE OR REPLACE FUNCTION auth.user_lpk_mitra_id()
RETURNS uuid AS $$
DECLARE
    lpk_id uuid;
BEGIN
    -- Try to get LPK Mitra ID from user_role_assignments first, then user_profiles
    SELECT COALESCE(ura.lpk_mitra_id, up.lpk_mitra_id) INTO lpk_id
    FROM user_profiles up
    LEFT JOIN user_role_assignments ura ON up.id = ura.user_id 
        AND ura.is_active = true 
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    WHERE up.id = auth.uid()
    LIMIT 1;
    
    RETURN lpk_id;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. Enable RLS on tables (only if they exist)
-- =====================================================

DO $$
DECLARE
    table_names text[] := ARRAY['user_profiles', 'siswa', 'lpk_mitra', 'kumiai', 'perusahaan_penerima', 'job_order', 'penempatan_siswa', 'program_pendidikan', 'dokumen_siswa'];
    table_name text;
BEGIN
    FOREACH table_name IN ARRAY table_names
    LOOP
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = table_names AND table_schema = 'public') THEN
            EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', table_name);
            RAISE NOTICE 'Enabled RLS on table: %', table_name;
        ELSE
            RAISE NOTICE 'Table % does not exist, skipping RLS', table_name;
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- 4. Create RLS Policies (only for existing tables)
-- =====================================================

-- User Profiles RLS Policies
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles' AND table_schema = 'public') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
        DROP POLICY IF EXISTS "Administrators can view all profiles" ON user_profiles;
        DROP POLICY IF EXISTS "Pimpinan can view all profiles" ON user_profiles;
        DROP POLICY IF EXISTS "Only administrators can modify profiles" ON user_profiles;
        
        -- Create new policies
        CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (id = auth.uid());
        CREATE POLICY "Administrators can view all profiles" ON user_profiles FOR SELECT USING (auth.user_has_role('administrator'));
        CREATE POLICY "Pimpinan can view all profiles" ON user_profiles FOR SELECT USING (auth.user_has_role('pimpinan'));
        CREATE POLICY "Only administrators can modify profiles" ON user_profiles FOR ALL USING (auth.user_has_role('administrator'));
        
        RAISE NOTICE 'Created RLS policies for user_profiles';
    END IF;
END $$;

-- LPK Mitra RLS Policies
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lpk_mitra' AND table_schema = 'public') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "LPK Mitra can view own data" ON lpk_mitra;
        DROP POLICY IF EXISTS "Administrators can view all LPK Mitra" ON lpk_mitra;
        DROP POLICY IF EXISTS "Divisi Recruitment can view all LPK Mitra" ON lpk_mitra;
        DROP POLICY IF EXISTS "Pimpinan can view all LPK Mitra" ON lpk_mitra;
        DROP POLICY IF EXISTS "Only administrators can modify LPK Mitra" ON lpk_mitra;
        
        -- Create new policies
        CREATE POLICY "LPK Mitra can view own data" ON lpk_mitra FOR SELECT USING (
            auth.user_has_role('lpk_mitra') AND id = auth.user_lpk_mitra_id()
        );
        CREATE POLICY "Administrators can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (auth.user_has_role('administrator'));
        CREATE POLICY "Divisi Recruitment can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (auth.user_has_role('divisi_recruitment'));
        CREATE POLICY "Pimpinan can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (auth.user_has_role('pimpinan'));
        CREATE POLICY "Only administrators can modify LPK Mitra" ON lpk_mitra FOR ALL USING (auth.user_has_role('administrator'));
        
        RAISE NOTICE 'Created RLS policies for lpk_mitra';
    END IF;
END $$;

-- Siswa RLS Policies
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'siswa' AND table_schema = 'public') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "LPK Mitra can view own students" ON siswa;
        DROP POLICY IF EXISTS "LPK Mitra can modify own students" ON siswa;
        DROP POLICY IF EXISTS "Administrators can view all students" ON siswa;
        DROP POLICY IF EXISTS "Administrators can modify all students" ON siswa;
        DROP POLICY IF EXISTS "Divisi Education can access students" ON siswa;
        DROP POLICY IF EXISTS "Divisi Recruitment can view students" ON siswa;
        DROP POLICY IF EXISTS "Pemberangkatan can access students" ON siswa;
        DROP POLICY IF EXISTS "Pimpinan can view all students" ON siswa;
        
        -- Create new policies
        CREATE POLICY "LPK Mitra can view own students" ON siswa FOR SELECT USING (
            auth.user_has_role('lpk_mitra') AND lpk_mitra_id = auth.user_lpk_mitra_id()
        );
        CREATE POLICY "LPK Mitra can modify own students" ON siswa FOR ALL USING (
            auth.user_has_role('lpk_mitra') AND lpk_mitra_id = auth.user_lpk_mitra_id()
        );
        CREATE POLICY "Administrators can view all students" ON siswa FOR SELECT USING (auth.user_has_role('administrator'));
        CREATE POLICY "Administrators can modify all students" ON siswa FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Divisi Education can access students" ON siswa FOR ALL USING (auth.user_has_role('divisi_education'));
        CREATE POLICY "Divisi Recruitment can view students" ON siswa FOR SELECT USING (auth.user_has_role('divisi_recruitment'));
        CREATE POLICY "Pemberangkatan can access students" ON siswa FOR ALL USING (auth.user_has_role('pemberangkatan'));
        CREATE POLICY "Pimpinan can view all students" ON siswa FOR SELECT USING (auth.user_has_role('pimpinan'));
        
        RAISE NOTICE 'Created RLS policies for siswa';
    END IF;
END $$;

-- Job Order RLS Policies
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'job_order' AND table_schema = 'public') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Administrators can access all job orders" ON job_order;
        DROP POLICY IF EXISTS "Divisi Recruitment can access job orders" ON job_order;
        DROP POLICY IF EXISTS "Other roles can view job orders" ON job_order;
        
        -- Create new policies
        CREATE POLICY "Administrators can access all job orders" ON job_order FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Divisi Recruitment can access job orders" ON job_order FOR ALL USING (auth.user_has_role('divisi_recruitment'));
        CREATE POLICY "Other roles can view job orders" ON job_order FOR SELECT USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('divisi_education') OR 
            auth.user_has_role('pemberangkatan') OR
            auth.user_has_role('pimpinan')
        );
        
        RAISE NOTICE 'Created RLS policies for job_order';
    END IF;
END $$;

-- =====================================================
-- 5. Grant permissions
-- =====================================================

GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_has_role(text) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_lpk_mitra_id() TO authenticated;

-- =====================================================
-- 6. Success message
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Safe RLS setup completed successfully!';
    RAISE NOTICE 'Added missing columns to user_profiles';
    RAISE NOTICE 'Created safe helper functions';
    RAISE NOTICE 'Enabled RLS on existing tables';
    RAISE NOTICE 'Created RLS policies for data access control';
END $$;
