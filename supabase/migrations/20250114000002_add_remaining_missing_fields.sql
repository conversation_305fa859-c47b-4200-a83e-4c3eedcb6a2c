-- =====================================================
-- Add Remaining Missing Siswa Fields - Supplementary Migration
-- Fix naming mismatches and add physical measurement fields
-- =====================================================

-- Add missing physical measurement fields that were in the form but not in first migration
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS lingkar_kepala DECIMAL(5,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS lingkar_pinggang DECIMAL(5,2);

-- Add the tanggal_masuk_lpk field that the form expects
-- Note: Database already has 'tgl_masuk_lpk' but form uses 'tanggal_masuk_lpk'
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tanggal_masuk_lpk DATE;

-- Copy data from existing tgl_masuk_lpk to new tanggal_masuk_lpk field if exists
UPDATE siswa SET tanggal_masuk_lpk = tgl_masuk_lpk 
WHERE tanggal_masuk_lpk IS NULL AND tgl_masuk_lpk IS NOT NULL;

-- Add indexes for new fields
CREATE INDEX IF NOT EXISTS idx_siswa_tanggal_masuk_lpk ON siswa(tanggal_masuk_lpk);
CREATE INDEX IF NOT EXISTS idx_siswa_lingkar_kepala ON siswa(lingkar_kepala);
CREATE INDEX IF NOT EXISTS idx_siswa_lingkar_pinggang ON siswa(lingkar_pinggang);

-- Add comments for the new fields
COMMENT ON COLUMN siswa.lingkar_kepala IS 'Lingkar kepala dalam cm';
COMMENT ON COLUMN siswa.lingkar_pinggang IS 'Lingkar pinggang dalam cm';
COMMENT ON COLUMN siswa.tanggal_masuk_lpk IS 'Tanggal masuk LPK (alias untuk tgl_masuk_lpk)';

-- Add constraints for physical measurements
ALTER TABLE siswa ADD CONSTRAINT chk_lingkar_kepala_range CHECK (lingkar_kepala > 40 AND lingkar_kepala < 80);
ALTER TABLE siswa ADD CONSTRAINT chk_lingkar_pinggang_range CHECK (lingkar_pinggang > 50 AND lingkar_pinggang < 200);

-- Update the profile completeness function to include new fields
CREATE OR REPLACE FUNCTION calculate_profile_completeness(siswa_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
    completeness_score INTEGER := 0;
    total_fields INTEGER := 55; -- Increased to account for new fields
    siswa_record RECORD;
BEGIN
    SELECT * INTO siswa_record FROM siswa WHERE id = siswa_id_param;
    
    IF siswa_record IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Required fields (core data) - 20 points
    IF siswa_record.nama_lengkap IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nik IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.tempat_lahir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.tanggal_lahir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.jenis_kelamin IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.agama IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.alamat_lengkap IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nomor_hp IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.pendidikan_terakhir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nama_sekolah IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    
    -- High-value optional fields - 20 points
    IF siswa_record.email IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.nomor_wa IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.tinggi_badan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.berat_badan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.golongan_darah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.ipk IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.hobi IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.tujuan_ke_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.level_bahasa_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.ketersediaan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Additional enhanced fields - 10 points  
    IF siswa_record.nama_lengkap_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.bakat_khusus IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.minat_kerja IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.target_kerja_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.sertifikat_keahlian IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Family data - 5 points
    IF siswa_record.nama_ayah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.nama_ibu IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.pekerjaan_ayah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.pekerjaan_ibu IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Health data - 3 points
    IF siswa_record.riwayat_penyakit IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.alergi IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Physical measurements - 2 points (NEW)
    IF siswa_record.lingkar_kepala IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.lingkar_pinggang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Check related data existence for bonus points - 5 points
    IF EXISTS(SELECT 1 FROM siswa_pendidikan WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 2; 
    END IF;
    
    IF EXISTS(SELECT 1 FROM siswa_pengalaman_kerja WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 2; 
    END IF;
    
    IF EXISTS(SELECT 1 FROM siswa_keluarga WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 1; 
    END IF;
    
    -- Convert to percentage and cap at 100
    RETURN LEAST(ROUND((completeness_score::NUMERIC / total_fields) * 100), 100);
END;
$$ LANGUAGE plpgsql;

-- Update existing records' completeness
UPDATE siswa SET profile_completeness = calculate_profile_completeness(id);

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Remaining missing siswa fields migration completed successfully!';
    RAISE NOTICE '📏 Added physical measurement fields: lingkar_kepala, lingkar_pinggang';
    RAISE NOTICE '📅 Added tanggal_masuk_lpk field to match form expectations';
    RAISE NOTICE '🔄 Updated profile completeness calculation';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 All form fields should now be supported!';
END $$; 