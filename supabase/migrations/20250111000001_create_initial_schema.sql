-- =====================================================
-- Initial Schema Migration for Dashboard Magang Jepang
-- Supabase PostgreSQL Version
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'operator', 'lpk_admin', 'viewer');
CREATE TYPE gender_type AS ENUM ('L', 'P');
CREATE TYPE education_level AS ENUM ('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1');
CREATE TYPE status_type AS ENUM ('aktif', 'nonaktif', 'suspended');
CREATE TYPE registration_status AS ENUM ('draft', 'submitted', 'review', 'approved', 'rejected');
CREATE TYPE placement_status AS ENUM ('ditempatkan', 'berangkat', 'aktif', 'selesai', 'dibatalkan');
CREATE TYPE job_status AS ENUM ('draft', 'published', 'closed', 'cancelled');
CREATE TYPE document_status AS ENUM ('pending', 'approved', 'rejected');

-- =====================================================
-- User Management Tables
-- =====================================================

-- User Profiles (extends Supabase auth.users)
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    role user_role DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- Master Data Tables
-- =====================================================

-- LPK Mitra (Training Institutions)
CREATE TABLE lpk_mitra (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_lpk VARCHAR(200) NOT NULL,
    alamat_lengkap TEXT NOT NULL,
    kota VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    nama_pimpinan VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    website VARCHAR(200),
    status status_type DEFAULT 'aktif',
    tanggal_kerjasama DATE,
    catatan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Kumiai (Japanese Cooperatives)
CREATE TABLE kumiai (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_kumiai VARCHAR(200) NOT NULL,
    kode_kumiai VARCHAR(50) UNIQUE NOT NULL,
    alamat_jepang TEXT NOT NULL,
    kota_jepang VARCHAR(100) NOT NULL,
    prefektur VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    website VARCHAR(200),
    status status_type DEFAULT 'aktif',
    keterangan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Perusahaan Penerima (Receiving Companies)
CREATE TABLE perusahaan_penerima (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    kumiai_id UUID REFERENCES kumiai(id) NOT NULL,
    nama_perusahaan VARCHAR(200) NOT NULL,
    alamat_jepang TEXT NOT NULL,
    kota_jepang VARCHAR(100) NOT NULL,
    prefektur VARCHAR(100) NOT NULL,
    bidang_usaha VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    website VARCHAR(200),
    status status_type DEFAULT 'aktif',
    keterangan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Program Pendidikan (Training Programs)
CREATE TABLE program_pendidikan (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_program VARCHAR(200) NOT NULL,
    deskripsi TEXT,
    durasi_bulan INTEGER NOT NULL,
    biaya DECIMAL(15,2) NOT NULL,
    kurikulum TEXT,
    sertifikat VARCHAR(200),
    status status_type DEFAULT 'aktif',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Jenis Dokumen (Document Types)
CREATE TABLE jenis_dokumen (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_dokumen VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    wajib BOOLEAN DEFAULT true,
    kategori VARCHAR(50) NOT NULL,
    urutan_tampil INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- Main Entity Tables
-- =====================================================

-- Siswa (Students)
CREATE TABLE siswa (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    lpk_id UUID REFERENCES lpk_mitra(id) NOT NULL,
    program_pendidikan_id UUID REFERENCES program_pendidikan(id),
    
    -- Personal Data
    nama_lengkap VARCHAR(200) NOT NULL,
    nik VARCHAR(20) UNIQUE NOT NULL,
    tempat_lahir VARCHAR(100) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    jenis_kelamin gender_type NOT NULL,
    agama VARCHAR(50) NOT NULL,
    status_pernikahan VARCHAR(20) DEFAULT 'belum_menikah',
    
    -- Address
    alamat_lengkap TEXT NOT NULL,
    kelurahan VARCHAR(100) NOT NULL,
    kecamatan VARCHAR(100) NOT NULL,
    kota_kabupaten VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    kode_pos VARCHAR(10),
    
    -- Contact
    nomor_hp VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    
    -- Education
    pendidikan_terakhir education_level NOT NULL,
    nama_sekolah VARCHAR(200) NOT NULL,
    tahun_lulus INTEGER NOT NULL,
    jurusan VARCHAR(100),
    
    -- Family
    nama_ayah VARCHAR(100),
    nama_ibu VARCHAR(100),
    alamat_keluarga TEXT,
    nomor_hp_keluarga VARCHAR(20),
    
    -- Status
    status_pendaftaran registration_status DEFAULT 'draft',
    tanggal_daftar DATE NOT NULL DEFAULT CURRENT_DATE,
    catatan TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job Order
CREATE TABLE job_order (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    perusahaan_id UUID REFERENCES perusahaan_penerima(id) NOT NULL,
    kumiai_id UUID REFERENCES kumiai(id) NOT NULL,
    
    -- Job Details
    judul_pekerjaan VARCHAR(200) NOT NULL,
    deskripsi_pekerjaan TEXT NOT NULL,
    posisi VARCHAR(100) NOT NULL,
    bidang_kerja VARCHAR(100) NOT NULL,
    
    -- Requirements
    jenis_kelamin VARCHAR(10) DEFAULT 'L/P',
    usia_min INTEGER DEFAULT 18,
    usia_max INTEGER DEFAULT 35,
    pendidikan_min education_level DEFAULT 'SMA',
    pengalaman_kerja TEXT,
    keahlian_khusus TEXT,
    
    -- Work Conditions
    gaji_pokok DECIMAL(15,2) NOT NULL,
    tunjangan DECIMAL(15,2) DEFAULT 0,
    jam_kerja_per_hari INTEGER DEFAULT 8,
    hari_kerja_per_minggu INTEGER DEFAULT 5,
    overtime_available BOOLEAN DEFAULT false,
    
    -- Facilities
    akomodasi TEXT,
    transportasi TEXT,
    asuransi TEXT,
    fasilitas_lain TEXT,
    
    -- Quota & Status
    jumlah_kuota INTEGER NOT NULL,
    kuota_terisi INTEGER DEFAULT 0,
    status job_status DEFAULT 'draft',
    tanggal_buka DATE NOT NULL,
    tanggal_tutup DATE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Penempatan Siswa (Student Placement)
CREATE TABLE penempatan_siswa (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) NOT NULL,
    job_order_id UUID REFERENCES job_order(id) NOT NULL,
    perusahaan_id UUID REFERENCES perusahaan_penerima(id) NOT NULL,
    kumiai_id UUID REFERENCES kumiai(id) NOT NULL,
    
    -- Placement Details
    tanggal_penempatan DATE NOT NULL,
    tanggal_keberangkatan DATE,
    tanggal_kepulangan DATE,
    status_penempatan placement_status DEFAULT 'ditempatkan',
    
    -- Work Details
    posisi_kerja VARCHAR(100) NOT NULL,
    gaji_aktual DECIMAL(15,2) NOT NULL,
    alamat_kerja TEXT NOT NULL,
    
    -- Monitoring
    evaluasi_bulanan TEXT,
    catatan_khusus TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Dokumen Siswa (Student Documents)
CREATE TABLE dokumen_siswa (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) NOT NULL,
    jenis_dokumen_id UUID REFERENCES jenis_dokumen(id) NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    path_file VARCHAR(500) NOT NULL,
    ukuran_file BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    status_verifikasi document_status DEFAULT 'pending',
    catatan_verifikasi TEXT,
    tanggal_upload TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- User Profiles
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);

-- LPK Mitra
CREATE INDEX idx_lpk_status ON lpk_mitra(status);
CREATE INDEX idx_lpk_kota ON lpk_mitra(kota);

-- Kumiai
CREATE INDEX idx_kumiai_status ON kumiai(status);
CREATE INDEX idx_kumiai_kode ON kumiai(kode_kumiai);

-- Siswa
CREATE INDEX idx_siswa_lpk_id ON siswa(lpk_id);
CREATE INDEX idx_siswa_nik ON siswa(nik);
CREATE INDEX idx_siswa_status ON siswa(status_pendaftaran);
CREATE INDEX idx_siswa_tanggal_lahir ON siswa(tanggal_lahir);
CREATE INDEX idx_siswa_jenis_kelamin ON siswa(jenis_kelamin);

-- Job Order
CREATE INDEX idx_job_order_status ON job_order(status);
CREATE INDEX idx_job_order_perusahaan ON job_order(perusahaan_id);
CREATE INDEX idx_job_order_kumiai ON job_order(kumiai_id);
CREATE INDEX idx_job_order_tanggal ON job_order(tanggal_buka, tanggal_tutup);

-- Penempatan
CREATE INDEX idx_penempatan_siswa ON penempatan_siswa(siswa_id);
CREATE INDEX idx_penempatan_status ON penempatan_siswa(status_penempatan);
CREATE INDEX idx_penempatan_tanggal ON penempatan_siswa(tanggal_keberangkatan);

-- Dokumen
CREATE INDEX idx_dokumen_siswa ON dokumen_siswa(siswa_id);
CREATE INDEX idx_dokumen_status ON dokumen_siswa(status_verifikasi);

-- =====================================================
-- Triggers for updated_at
-- =====================================================

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lpk_mitra_updated_at BEFORE UPDATE ON lpk_mitra FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kumiai_updated_at BEFORE UPDATE ON kumiai FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_perusahaan_updated_at BEFORE UPDATE ON perusahaan_penerima FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_program_pendidikan_updated_at BEFORE UPDATE ON program_pendidikan FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_jenis_dokumen_updated_at BEFORE UPDATE ON jenis_dokumen FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_siswa_updated_at BEFORE UPDATE ON siswa FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_job_order_updated_at BEFORE UPDATE ON job_order FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_penempatan_updated_at BEFORE UPDATE ON penempatan_siswa FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dokumen_updated_at BEFORE UPDATE ON dokumen_siswa FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
