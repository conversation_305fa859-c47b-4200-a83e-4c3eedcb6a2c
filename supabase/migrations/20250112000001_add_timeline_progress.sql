-- =====================================================
-- Timeline Progress Tables for Student Tracking
-- Supabase PostgreSQL Version
-- =====================================================

-- Create custom types for timeline
CREATE TYPE timeline_stage_category AS ENUM ('preparation', 'selection', 'documentation', 'training', 'deployment');
CREATE TYPE timeline_status AS ENUM ('belum_mulai', 'berlangsung', 'selesai', 'dibatalkan');

-- Tabel Master Timeline Stages
CREATE TABLE timeline_stages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_stage VARCHAR(100) NOT NULL,
    deskripsi TEXT NOT NULL,
    urutan INTEGER NOT NULL UNIQUE,
    icon VARCHAR(50) NOT NULL,
    kategori timeline_stage_category DEFAULT 'preparation',
    is_aktif BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabel Progress Siswa
CREATE TABLE siswa_timeline_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) NOT NULL,
    timeline_stage_id UUID REFERENCES timeline_stages(id) NOT NULL,
    status timeline_status DEFAULT 'belum_mulai',
    tanggal_mulai DATE,
    tanggal_selesai DATE,
    catatan TEXT,
    dokumen_pendukung JSONB,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(siswa_id, timeline_stage_id)
);

-- Insert Default Timeline Stages
INSERT INTO timeline_stages (nama_stage, deskripsi, urutan, icon, kategori) VALUES
('Pre-seleksi', 'Verifikasi dokumen awal dan kelengkapan berkas', 1, 'ClipboardCheck', 'preparation'),
('Pendidikan Pra-Diklat', 'Persiapan dasar sebelum memasuki program diklat', 2, 'BookOpen', 'training'),
('Pendidikan Diklat', 'Program pelatihan teknis dan bahasa Jepang', 3, 'GraduationCap', 'training'),
('Seleksi Administrasi', 'Verifikasi kelengkapan dokumen untuk seleksi', 4, 'FileCheck', 'selection'),
('Wawancara', 'Tes wawancara dengan pihak perusahaan Jepang', 5, 'MessageSquare', 'selection'),
('Pemberkasan', 'Pengurusan dokumen resmi untuk keberangkatan', 6, 'FileText', 'documentation'),
('Pendidikan Pasca-Diklat', 'Pelatihan lanjutan dan orientasi budaya Jepang', 7, 'Award', 'training'),
('Surat Rekomendasi Disnaker', 'Penerbitan surat rekomendasi dari Dinas Tenaga Kerja', 8, 'Award', 'documentation'),
('Pemberangkatan ke Jepang', 'Keberangkatan menuju tempat kerja di Jepang', 9, 'Plane', 'deployment');

-- Create indexes for performance
CREATE INDEX idx_siswa_timeline_siswa_id ON siswa_timeline_progress(siswa_id);
CREATE INDEX idx_siswa_timeline_status ON siswa_timeline_progress(status);
CREATE INDEX idx_timeline_stages_urutan ON timeline_stages(urutan);
CREATE INDEX idx_timeline_stages_kategori ON timeline_stages(kategori);

-- Create view for easy timeline progress retrieval
CREATE OR REPLACE VIEW v_siswa_timeline_progress AS
SELECT 
    stp.id,
    stp.siswa_id,
    s.nama_lengkap,
    ts.nama_stage,
    ts.deskripsi,
    ts.urutan,
    ts.icon,
    ts.kategori,
    stp.status,
    stp.tanggal_mulai,
    stp.tanggal_selesai,
    stp.catatan,
    stp.dokumen_pendukung,
    stp.created_at,
    stp.updated_at
FROM siswa_timeline_progress stp
JOIN timeline_stages ts ON stp.timeline_stage_id = ts.id
JOIN siswa s ON stp.siswa_id = s.id
ORDER BY ts.urutan;

-- Function to initialize timeline for new student (PostgreSQL)
CREATE OR REPLACE FUNCTION init_student_timeline(student_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    stage_record RECORD;
BEGIN
    -- Insert timeline progress for all active stages
    FOR stage_record IN 
        SELECT id FROM timeline_stages WHERE is_aktif = true ORDER BY urutan
    LOOP
        INSERT INTO siswa_timeline_progress (siswa_id, timeline_stage_id, status)
        VALUES (student_id, stage_record.id, 'belum_mulai')
        ON CONFLICT (siswa_id, timeline_stage_id) DO NOTHING;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for updated_at
CREATE OR REPLACE FUNCTION update_timeline_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_timeline_stages_updated_at 
    BEFORE UPDATE ON timeline_stages 
    FOR EACH ROW EXECUTE FUNCTION update_timeline_updated_at();

CREATE TRIGGER update_siswa_timeline_progress_updated_at 
    BEFORE UPDATE ON siswa_timeline_progress 
    FOR EACH ROW EXECUTE FUNCTION update_timeline_updated_at();

-- Trigger to auto-initialize timeline when student is created
CREATE OR REPLACE FUNCTION trigger_init_student_timeline()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM init_student_timeline(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER init_timeline_on_student_insert
    AFTER INSERT ON siswa
    FOR EACH ROW EXECUTE FUNCTION trigger_init_student_timeline();

-- Enable RLS (Row Level Security)
ALTER TABLE timeline_stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa_timeline_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Timeline stages visible to all users" ON timeline_stages
    FOR SELECT USING (true);

CREATE POLICY "Users can view student timeline progress" ON siswa_timeline_progress
    FOR SELECT USING (true);

CREATE POLICY "Users can update student timeline progress" ON siswa_timeline_progress
    FOR UPDATE USING (true);

CREATE POLICY "Users can insert student timeline progress" ON siswa_timeline_progress
    FOR INSERT WITH CHECK (true); 