-- =====================================================
-- Data Migration for Existing Users to New RBAC System
-- Dashboard Magang Jepang - User Access Management
-- =====================================================

-- =====================================================
-- Migrate existing user roles to new system
-- =====================================================

-- Create temporary mapping for old roles to new roles
CREATE TEMP TABLE role_mapping AS
SELECT 
    'admin' as old_role,
    'administrator' as new_role
UNION ALL
SELECT 
    'operator' as old_role,
    'administrator' as new_role  -- Operators become administrators for now
UNION ALL
SELECT 
    'lpk_admin' as old_role,
    'lpk_mitra' as new_role
UNION ALL
SELECT 
    'viewer' as old_role,
    'pimpinan' as new_role;  -- Viewers become pimpinan (read-only access)

-- Insert user role assignments based on existing user_profiles
-- Only if user_role_assignments table exists and user_profiles has data
DO $$
BEGIN
    -- Check if tables exist and have the required columns
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'user_role_assignments' AND table_schema = 'public'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'user_profiles' AND table_schema = 'public'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'user_profiles' AND column_name = 'role' AND table_schema = 'public'
    ) THEN
        -- Insert user role assignments
        INSERT INTO user_role_assignments (user_id, role_id, assigned_by, is_active, assigned_at)
        SELECT
            up.id as user_id,
            r.id as role_id,
            up.id as assigned_by, -- Self-assigned for migration
            up.is_active,
            up.created_at as assigned_at
        FROM user_profiles up
        JOIN role_mapping rm ON up.role::text = rm.old_role
        JOIN roles r ON rm.new_role = r.name
        WHERE up.role IS NOT NULL
        ON CONFLICT (user_id, role_id) DO NOTHING;

        RAISE NOTICE 'User role assignments migrated successfully';
    ELSE
        RAISE NOTICE 'Skipping user role assignment migration - required tables or columns not found';
    END IF;
END $$;

-- =====================================================
-- Create default admin user if none exists
-- =====================================================

-- This will be handled manually or through Supabase Auth
-- Just ensure we have the structure ready

-- =====================================================
-- Update user_profiles table structure
-- =====================================================

-- Add constraint to ensure user_profiles.role matches new enum
-- We'll keep the old role column for backward compatibility during transition
-- ALTER TABLE user_profiles ALTER COLUMN role TYPE user_role USING role::text::user_role;

-- =====================================================
-- Create views for easier role management
-- =====================================================

-- View to see user roles with details
CREATE OR REPLACE VIEW v_user_roles AS
SELECT 
    up.id,
    up.username,
    up.full_name,
    up.email,
    r.name as role_name,
    r.display_name as role_display_name,
    ura.is_active as role_active,
    ura.assigned_at,
    ura.expires_at,
    lm.nama_lpk as lpk_name
FROM user_profiles up
LEFT JOIN user_role_assignments ura ON up.id = ura.user_id AND ura.is_active = true
LEFT JOIN roles r ON ura.role_id = r.id
LEFT JOIN lpk_mitra lm ON ura.lpk_mitra_id = lm.id
ORDER BY up.full_name;

-- View to see role permissions
CREATE OR REPLACE VIEW v_role_permissions AS
SELECT 
    r.name as role_name,
    r.display_name as role_display_name,
    p.name as permission_name,
    p.display_name as permission_display_name,
    p.module,
    p.action
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
ORDER BY r.name, p.module, p.action;

-- View for user permissions (flattened)
CREATE OR REPLACE VIEW v_user_permissions AS
SELECT 
    up.id as user_id,
    up.username,
    up.full_name,
    p.name as permission_name,
    p.display_name as permission_display_name,
    p.module,
    p.action,
    r.name as role_name
FROM user_profiles up
JOIN user_role_assignments ura ON up.id = ura.user_id AND ura.is_active = true
JOIN roles r ON ura.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
ORDER BY up.full_name, p.module, p.action;

-- =====================================================
-- Helper functions for role management
-- =====================================================

-- Function to assign role to user
CREATE OR REPLACE FUNCTION assign_user_role(
    target_user_id UUID,
    role_name VARCHAR,
    assigned_by_user_id UUID DEFAULT auth.uid(),
    lpk_id UUID DEFAULT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Get role ID
    SELECT id INTO role_id FROM roles WHERE name = role_name AND is_active = true;
    
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role % not found or inactive', role_name;
    END IF;
    
    -- Insert or update role assignment
    INSERT INTO user_role_assignments (user_id, role_id, assigned_by, lpk_mitra_id, expires_at)
    VALUES (target_user_id, role_id, assigned_by_user_id, lpk_id, expires_at)
    ON CONFLICT (user_id, role_id) 
    DO UPDATE SET 
        is_active = true,
        assigned_by = assigned_by_user_id,
        lpk_mitra_id = lpk_id,
        expires_at = expires_at,
        assigned_at = NOW();
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revoke role from user
CREATE OR REPLACE FUNCTION revoke_user_role(
    target_user_id UUID,
    role_name VARCHAR
)
RETURNS BOOLEAN AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Get role ID
    SELECT id INTO role_id FROM roles WHERE name = role_name;
    
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role % not found', role_name;
    END IF;
    
    -- Deactivate role assignment
    UPDATE user_role_assignments 
    SET is_active = false 
    WHERE user_id = target_user_id AND role_id = role_id;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's active roles
CREATE OR REPLACE FUNCTION get_user_roles(user_uuid UUID)
RETURNS TABLE(role_name VARCHAR, role_display_name VARCHAR, lpk_name VARCHAR) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.name,
        r.display_name,
        COALESCE(lm.nama_lpk, '') as lpk_name
    FROM user_role_assignments ura
    JOIN roles r ON ura.role_id = r.id
    LEFT JOIN lpk_mitra lm ON ura.lpk_mitra_id = lm.id
    WHERE ura.user_id = user_uuid 
    AND ura.is_active = true
    AND (ura.expires_at IS NULL OR ura.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- Triggers for audit trail
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Grant necessary permissions
-- =====================================================

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Grant permissions on tables
GRANT SELECT ON roles TO authenticated, anon;
GRANT SELECT ON permissions TO authenticated, anon;
GRANT SELECT ON role_permissions TO authenticated, anon;
GRANT SELECT ON user_role_assignments TO authenticated;

-- Grant permissions on views
GRANT SELECT ON v_user_roles TO authenticated;
GRANT SELECT ON v_role_permissions TO authenticated;
GRANT SELECT ON v_user_permissions TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_permission(UUID, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION assign_user_role(UUID, VARCHAR, UUID, UUID, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION revoke_user_role(UUID, VARCHAR) TO authenticated;
