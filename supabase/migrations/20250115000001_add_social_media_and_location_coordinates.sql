-- Migration: Add social media table and location coordinates
-- Date: 2025-01-15

-- Add location coordinates to siswa table
ALTER TABLE siswa 
ADD COLUMN latitude DECIMAL(10, 8),
ADD COLUMN longitude DECIMAL(11, 8),
ADD COLUMN alamat_koordinat TEXT;

-- Create social media table
CREATE TABLE siswa_social_media (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    siswa_id UUID NOT NULL REFERENCES siswa(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    username VARCHAR(100) NOT NULL,
    url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX idx_siswa_social_media_siswa_id ON siswa_social_media(siswa_id);
CREATE INDEX idx_siswa_social_media_platform ON siswa_social_media(platform);

-- Add constraint to ensure unique platform per student
ALTER TABLE siswa_social_media 
ADD CONSTRAINT unique_siswa_platform UNIQUE(siswa_id, platform);

-- Add comment for documentation
COMMENT ON TABLE siswa_social_media IS 'Social media accounts for students';
COMMENT ON COLUMN siswa_social_media.platform IS 'Social media platform (e.g., instagram, facebook, twitter, linkedin, tiktok)';
COMMENT ON COLUMN siswa_social_media.username IS 'Username or handle on the platform';
COMMENT ON COLUMN siswa_social_media.url IS 'Full URL to the profile (optional)';
COMMENT ON COLUMN siswa.latitude IS 'Latitude coordinate for home address';
COMMENT ON COLUMN siswa.longitude IS 'Longitude coordinate for home address';
COMMENT ON COLUMN siswa.alamat_koordinat IS 'Formatted address coordinates for display';

-- Create trigger to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_siswa_social_media_updated_at 
    BEFORE UPDATE ON siswa_social_media 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 