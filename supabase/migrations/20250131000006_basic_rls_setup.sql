-- =====================================================
-- Basic RLS Setup - Safe and Simple
-- Dashboard Magang Jepang - Data Access Control
-- =====================================================

-- This migration creates basic RLS policies that work with existing schema
-- without requiring specific columns that might not exist yet

-- =====================================================
-- 1. Add missing columns to user_profiles safely
-- =====================================================

DO $$
BEGIN
    -- Add lpk_mitra_id if not exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' AND column_name = 'lpk_mitra_id' AND table_schema = 'public'
    ) THEN
        ALTER TABLE user_profiles ADD COLUMN lpk_mitra_id UUID;
        RAISE NOTICE 'Added lpk_mitra_id column to user_profiles';
    END IF;
    
    -- Add other columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'phone' AND table_schema = 'public') THEN
        ALTER TABLE user_profiles ADD COLUMN phone VARCHAR(20);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'position' AND table_schema = 'public') THEN
        ALTER TABLE user_profiles ADD COLUMN position VARCHAR(100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'department' AND table_schema = 'public') THEN
        ALTER TABLE user_profiles ADD COLUMN department VARCHAR(100);
    END IF;
END $$;

-- =====================================================
-- 2. Create helper functions
-- =====================================================

CREATE OR REPLACE FUNCTION auth.user_has_role(role_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = role_name
        AND ura.is_active = true
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION auth.user_lpk_mitra_id()
RETURNS uuid AS $$
DECLARE
    lpk_id uuid;
BEGIN
    SELECT COALESCE(ura.lpk_mitra_id, up.lpk_mitra_id) INTO lpk_id
    FROM user_profiles up
    LEFT JOIN user_role_assignments ura ON up.id = ura.user_id 
        AND ura.is_active = true 
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    WHERE up.id = auth.uid()
    LIMIT 1;
    
    RETURN lpk_id;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. Enable RLS on main tables
-- =====================================================

-- Enable RLS on user_profiles
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Enable RLS on other tables if they exist
DO $$
DECLARE
    tbl_name text;
    table_list text[] := ARRAY['siswa', 'lpk_mitra', 'kumiai', 'job_order', 'penempatan_siswa', 'dokumen_siswa'];
BEGIN
    FOREACH tbl_name IN ARRAY table_list
    LOOP
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE information_schema.tables.table_name = tbl_name AND information_schema.tables.table_schema = 'public') THEN
            EXECUTE format('ALTER TABLE %I ENABLE ROW LEVEL SECURITY', tbl_name);
            RAISE NOTICE 'Enabled RLS on table: %', tbl_name;
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- 4. Create basic RLS policies
-- =====================================================

-- User Profiles Policies
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Administrators can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Pimpinan can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Only administrators can modify profiles" ON user_profiles;

CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (id = auth.uid());
CREATE POLICY "Administrators can view all profiles" ON user_profiles FOR SELECT USING (auth.user_has_role('administrator'));
CREATE POLICY "Pimpinan can view all profiles" ON user_profiles FOR SELECT USING (auth.user_has_role('pimpinan'));
CREATE POLICY "Only administrators can modify profiles" ON user_profiles FOR ALL USING (auth.user_has_role('administrator'));

-- Basic policies for other tables (if they exist)
DO $$
BEGIN
    -- LPK Mitra policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lpk_mitra' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all lpk_mitra" ON lpk_mitra;
        DROP POLICY IF EXISTS "Other roles can view lpk_mitra" ON lpk_mitra;
        
        CREATE POLICY "Administrators can access all lpk_mitra" ON lpk_mitra FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can view lpk_mitra" ON lpk_mitra FOR SELECT USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('divisi_recruitment') OR 
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for lpk_mitra';
    END IF;
    
    -- Siswa policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'siswa' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all siswa" ON siswa;
        DROP POLICY IF EXISTS "Other roles can access siswa" ON siswa;
        
        CREATE POLICY "Administrators can access all siswa" ON siswa FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can access siswa" ON siswa FOR ALL USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('divisi_education') OR 
            auth.user_has_role('divisi_recruitment') OR 
            auth.user_has_role('pemberangkatan') OR
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for siswa';
    END IF;
    
    -- Job Order policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'job_order' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all job_order" ON job_order;
        DROP POLICY IF EXISTS "Other roles can access job_order" ON job_order;
        
        CREATE POLICY "Administrators can access all job_order" ON job_order FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can access job_order" ON job_order FOR SELECT USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('divisi_recruitment') OR 
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for job_order';
    END IF;
    
    -- Kumiai policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'kumiai' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all kumiai" ON kumiai;
        DROP POLICY IF EXISTS "Other roles can view kumiai" ON kumiai;
        
        CREATE POLICY "Administrators can access all kumiai" ON kumiai FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can view kumiai" ON kumiai FOR SELECT USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('divisi_recruitment') OR 
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for kumiai';
    END IF;
    
    -- Penempatan Siswa policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'penempatan_siswa' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all penempatan_siswa" ON penempatan_siswa;
        DROP POLICY IF EXISTS "Other roles can access penempatan_siswa" ON penempatan_siswa;
        
        CREATE POLICY "Administrators can access all penempatan_siswa" ON penempatan_siswa FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can access penempatan_siswa" ON penempatan_siswa FOR ALL USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('pemberangkatan') OR 
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for penempatan_siswa';
    END IF;
    
    -- Dokumen Siswa policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'dokumen_siswa' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS "Administrators can access all dokumen_siswa" ON dokumen_siswa;
        DROP POLICY IF EXISTS "Other roles can access dokumen_siswa" ON dokumen_siswa;
        
        CREATE POLICY "Administrators can access all dokumen_siswa" ON dokumen_siswa FOR ALL USING (auth.user_has_role('administrator'));
        CREATE POLICY "Other roles can access dokumen_siswa" ON dokumen_siswa FOR ALL USING (
            auth.user_has_role('lpk_mitra') OR 
            auth.user_has_role('pemberangkatan') OR 
            auth.user_has_role('pimpinan')
        );
        RAISE NOTICE 'Created basic RLS policies for dokumen_siswa';
    END IF;
END $$;

-- =====================================================
-- 5. Grant permissions
-- =====================================================

GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_has_role(text) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_lpk_mitra_id() TO authenticated;

-- =====================================================
-- 6. Success message
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Basic RLS setup completed successfully!';
    RAISE NOTICE 'This creates basic role-based access control without requiring specific foreign key columns';
    RAISE NOTICE 'You can enhance the policies later when all required columns are available';
    RAISE NOTICE 'Next step: Create admin user and test the system';
END $$;
