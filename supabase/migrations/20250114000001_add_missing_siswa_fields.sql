-- =====================================================
-- Add Missing Siswa Fields Migration
-- Complete form field support
-- =====================================================

-- Add missing basic fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nomor_wa VARCHAR(20);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS golongan_darah VARCHAR(5);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS riwayat_penyakit TEXT;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS alergi TEXT;

-- Add missing size fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ukuran_baju VARCHAR(10);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ukuran_celana INTEGER;

-- Add missing education fields  
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ipk DECIMAL(3,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS sertifikat_keahlian VARCHAR(255);

-- Add missing family work fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pekerjaan_ayah VARCHAR(100);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pekerjaan_ibu VARCHAR(100);

-- Add missing Japanese-specific fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pengalaman_organisasi TEXT;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS target_kerja_jepang TEXT;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS rencana_setelah_jepang TEXT;

-- Add missing LPK/language fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS level_bahasa_jepang VARCHAR(50);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS sertifikat_bahasa VARCHAR(100);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nilai_ujian_masuk INTEGER;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS lama_belajar_bulan INTEGER;

-- Add missing availability fields
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tanggal_ketersediaan DATE;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS keterangan_ketersediaan TEXT;

-- Rename existing fields to match form expectations (add aliases via computed columns)
-- We'll keep the original fields and add computed columns for compatibility

-- Add status fields that match the form
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ketersediaan VARCHAR(20) DEFAULT 'belum_siap';
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS status_verifikasi VARCHAR(30) DEFAULT 'belum_diverifikasi';

-- Update existing is_available to match ketersediaan
UPDATE siswa SET ketersediaan = CASE 
    WHEN is_available = true THEN 'siap'
    ELSE 'belum_siap' 
END WHERE ketersediaan IS NULL;

-- Update existing is_verified to match status_verifikasi  
UPDATE siswa SET status_verifikasi = CASE 
    WHEN is_verified = true THEN 'terverifikasi'
    ELSE 'belum_diverifikasi'
END WHERE status_verifikasi IS NULL;

-- Add field for enhanced form data
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nama_lengkap_jepang VARCHAR(255);

-- Update jp_nama to nama_lengkap_jepang if exists
UPDATE siswa SET nama_lengkap_jepang = jp_nama WHERE nama_lengkap_jepang IS NULL AND jp_nama IS NOT NULL;

-- Convert lama_belajar string to lama_belajar_bulan integer where possible
UPDATE siswa SET lama_belajar_bulan = CASE 
    WHEN lama_belajar ~ '^[0-9]+$' THEN lama_belajar::INTEGER
    ELSE NULL
END WHERE lama_belajar_bulan IS NULL;

-- Add new ENUM types for enhanced validation
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'marital_status_type') THEN
        CREATE TYPE marital_status_type AS ENUM ('lajang', 'menikah', 'bercerai', 'duda_janda');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'blood_type') THEN
        CREATE TYPE blood_type AS ENUM ('A', 'B', 'AB', 'O');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'availability_status_type') THEN
        CREATE TYPE availability_status_type AS ENUM ('siap', 'belum_siap', 'kondisional');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status_type') THEN
        CREATE TYPE verification_status_type AS ENUM ('belum_diverifikasi', 'sedang_diverifikasi', 'terverifikasi', 'ditolak');
    END IF;
END $$;

-- Add constraints for proper data validation
ALTER TABLE siswa ADD CONSTRAINT chk_golongan_darah CHECK (golongan_darah IN ('A', 'B', 'AB', 'O'));
ALTER TABLE siswa ADD CONSTRAINT chk_ketersediaan CHECK (ketersediaan IN ('siap', 'belum_siap', 'kondisional'));
ALTER TABLE siswa ADD CONSTRAINT chk_status_verifikasi CHECK (status_verifikasi IN ('belum_diverifikasi', 'sedang_diverifikasi', 'terverifikasi', 'ditolak'));
ALTER TABLE siswa ADD CONSTRAINT chk_ipk_range CHECK (ipk >= 0 AND ipk <= 4.0);
ALTER TABLE siswa ADD CONSTRAINT chk_nilai_ujian_range CHECK (nilai_ujian_masuk >= 0 AND nilai_ujian_masuk <= 100);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_siswa_ketersediaan ON siswa(ketersediaan);
CREATE INDEX IF NOT EXISTS idx_siswa_status_verifikasi ON siswa(status_verifikasi);
CREATE INDEX IF NOT EXISTS idx_siswa_level_bahasa ON siswa(level_bahasa_jepang);
CREATE INDEX IF NOT EXISTS idx_siswa_golongan_darah ON siswa(golongan_darah);

-- Add comments for documentation
COMMENT ON COLUMN siswa.nomor_wa IS 'Nomor WhatsApp siswa';
COMMENT ON COLUMN siswa.golongan_darah IS 'Golongan darah (A, B, AB, O)';
COMMENT ON COLUMN siswa.riwayat_penyakit IS 'Riwayat penyakit yang pernah diderita';
COMMENT ON COLUMN siswa.alergi IS 'Alergi makanan, obat, atau lainnya';
COMMENT ON COLUMN siswa.ukuran_baju IS 'Ukuran baju (XS, S, M, L, XL, XXL, XXXL)';
COMMENT ON COLUMN siswa.ukuran_celana IS 'Ukuran celana dalam cm';
COMMENT ON COLUMN siswa.ipk IS 'Indeks Prestasi Kumulatif (0.00-4.00)';
COMMENT ON COLUMN siswa.sertifikat_keahlian IS 'Sertifikat keahlian yang dimiliki';
COMMENT ON COLUMN siswa.pekerjaan_ayah IS 'Pekerjaan ayah kandung';
COMMENT ON COLUMN siswa.pekerjaan_ibu IS 'Pekerjaan ibu kandung';
COMMENT ON COLUMN siswa.pengalaman_organisasi IS 'Pengalaman berorganisasi';
COMMENT ON COLUMN siswa.target_kerja_jepang IS 'Target dan tujuan selama bekerja di Jepang';
COMMENT ON COLUMN siswa.rencana_setelah_jepang IS 'Rencana setelah kembali dari Jepang';
COMMENT ON COLUMN siswa.level_bahasa_jepang IS 'Level kemampuan bahasa Jepang (N5, N4, N3, N2, N1)';
COMMENT ON COLUMN siswa.sertifikat_bahasa IS 'Sertifikat bahasa Jepang yang dimiliki';
COMMENT ON COLUMN siswa.nilai_ujian_masuk IS 'Nilai ujian masuk LPK (0-100)';
COMMENT ON COLUMN siswa.lama_belajar_bulan IS 'Lama belajar di LPK dalam bulan';
COMMENT ON COLUMN siswa.ketersediaan IS 'Status ketersediaan siswa (siap, belum_siap, kondisional)';
COMMENT ON COLUMN siswa.tanggal_ketersediaan IS 'Tanggal mulai tersedia untuk ditempatkan';
COMMENT ON COLUMN siswa.keterangan_ketersediaan IS 'Keterangan tambahan tentang ketersediaan';
COMMENT ON COLUMN siswa.status_verifikasi IS 'Status verifikasi data siswa';
COMMENT ON COLUMN siswa.nama_lengkap_jepang IS 'Nama siswa dalam huruf Jepang (Hiragana/Katakana)';

-- Create a function to calculate profile completeness
CREATE OR REPLACE FUNCTION calculate_profile_completeness(siswa_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
    completeness_score INTEGER := 0;
    total_fields INTEGER := 50; -- Adjust based on total required fields
    siswa_record RECORD;
BEGIN
    SELECT * INTO siswa_record FROM siswa WHERE id = siswa_id_param;
    
    IF siswa_record IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Required fields (core data)
    IF siswa_record.nama_lengkap IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nik IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.tempat_lahir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.tanggal_lahir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.jenis_kelamin IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.agama IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.alamat_lengkap IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nomor_hp IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.pendidikan_terakhir IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    IF siswa_record.nama_sekolah IS NOT NULL THEN completeness_score := completeness_score + 2; END IF;
    
    -- High-value optional fields
    IF siswa_record.email IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.nomor_wa IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.tinggi_badan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.berat_badan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.golongan_darah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.ipk IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.hobi IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.tujuan_ke_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.level_bahasa_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.ketersediaan IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Additional fields for bonus points
    IF siswa_record.nama_lengkap_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.bakat_khusus IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.minat_kerja IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.target_kerja_jepang IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.sertifikat_keahlian IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Family data
    IF siswa_record.nama_ayah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.nama_ibu IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.pekerjaan_ayah IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.pekerjaan_ibu IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Health data
    IF siswa_record.riwayat_penyakit IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    IF siswa_record.alergi IS NOT NULL THEN completeness_score := completeness_score + 1; END IF;
    
    -- Check related data existence for bonus points
    IF EXISTS(SELECT 1 FROM siswa_pendidikan WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 2; 
    END IF;
    
    IF EXISTS(SELECT 1 FROM siswa_pengalaman_kerja WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 2; 
    END IF;
    
    IF EXISTS(SELECT 1 FROM siswa_keluarga WHERE siswa_id = siswa_id_param) THEN 
        completeness_score := completeness_score + 1; 
    END IF;
    
    -- Convert to percentage and cap at 100
    RETURN LEAST(ROUND((completeness_score::NUMERIC / total_fields) * 100), 100);
END;
$$ LANGUAGE plpgsql;

-- Add profile_completeness column if not exists
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS profile_completeness INTEGER DEFAULT 0;

-- Update all existing records with calculated completeness
UPDATE siswa SET profile_completeness = calculate_profile_completeness(id);

-- Create trigger to auto-update profile completeness on siswa changes
CREATE OR REPLACE FUNCTION trigger_update_profile_completeness()
RETURNS TRIGGER AS $$
BEGIN
    NEW.profile_completeness = calculate_profile_completeness(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_profile_completeness_on_siswa_change ON siswa;
CREATE TRIGGER update_profile_completeness_on_siswa_change
    BEFORE UPDATE ON siswa
    FOR EACH ROW EXECUTE FUNCTION trigger_update_profile_completeness();

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Missing siswa fields migration completed successfully!';
    RAISE NOTICE '📊 Added fields: nomor_wa, golongan_darah, riwayat_penyakit, alergi, ukuran_baju, ukuran_celana';
    RAISE NOTICE '🎓 Added education fields: ipk, sertifikat_keahlian';
    RAISE NOTICE '👨‍👩‍👧‍👦 Added family work fields: pekerjaan_ayah, pekerjaan_ibu';
    RAISE NOTICE '🇯🇵 Added Japanese fields: pengalaman_organisasi, target_kerja_jepang, rencana_setelah_jepang';
    RAISE NOTICE '📚 Added LPK fields: level_bahasa_jepang, sertifikat_bahasa, nilai_ujian_masuk, lama_belajar_bulan';
    RAISE NOTICE '✅ Added availability fields: ketersediaan, tanggal_ketersediaan, keterangan_ketersediaan, status_verifikasi';
    RAISE NOTICE '🔢 Added profile completeness calculation function';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Next: Update your application service to use the new fields!';
END $$; 