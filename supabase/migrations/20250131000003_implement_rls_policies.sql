-- =====================================================
-- Row Level Security (RLS) Policies Implementation
-- Dashboard Magang Jepang - Data Access Control
-- =====================================================

-- =====================================================
-- Enable RLS on all main tables
-- =====================================================

ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa ENABLE ROW LEVEL SECURITY;
ALTER TABLE lpk_mitra ENABLE ROW LEVEL SECURITY;
ALTER TABLE kumiai ENABLE ROW LEVEL SECURITY;
ALTER TABLE perusahaan_penerima ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_order ENABLE ROW LEVEL SECURITY;
ALTER TABLE penempatan_siswa ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_pendidikan ENABLE ROW LEVEL SECURITY;
ALTER TABLE dokumen_siswa ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- Helper function to check user role
-- =====================================================

CREATE OR REPLACE FUNCTION auth.user_has_role(role_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = role_name
        AND ura.is_active = true
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user's LPK Mitra ID
CREATE OR REPLACE FUNCTION auth.user_lpk_mitra_id()
RETURNS uuid AS $$
BEGIN
    RETURN (
        SELECT COALESCE(ura.lpk_mitra_id, up.lpk_mitra_id)
        FROM user_profiles up
        LEFT JOIN user_role_assignments ura ON up.id = ura.user_id 
            AND ura.is_active = true 
            AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
        WHERE up.id = auth.uid()
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- User Profiles RLS Policies
-- =====================================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (
    id = auth.uid()
);

-- Administrators can view all profiles
CREATE POLICY "Administrators can view all profiles" ON user_profiles FOR SELECT USING (
    auth.user_has_role('administrator')
);

-- Pimpinan can view all profiles (read-only)
CREATE POLICY "Pimpinan can view all profiles" ON user_profiles FOR SELECT USING (
    auth.user_has_role('pimpinan')
);

-- Only administrators can modify user profiles
CREATE POLICY "Only administrators can modify profiles" ON user_profiles FOR ALL USING (
    auth.user_has_role('administrator')
);

-- =====================================================
-- LPK Mitra RLS Policies
-- =====================================================

-- LPK Mitra users can only see their own LPK data
CREATE POLICY "LPK Mitra can view own data" ON lpk_mitra FOR SELECT USING (
    auth.user_has_role('lpk_mitra') AND id = auth.user_lpk_mitra_id()
);

-- Administrators can view all LPK Mitra data
CREATE POLICY "Administrators can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (
    auth.user_has_role('administrator')
);

-- Divisi Recruitment can view all LPK Mitra data
CREATE POLICY "Divisi Recruitment can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (
    auth.user_has_role('divisi_recruitment')
);

-- Pimpinan can view all LPK Mitra data
CREATE POLICY "Pimpinan can view all LPK Mitra" ON lpk_mitra FOR SELECT USING (
    auth.user_has_role('pimpinan')
);

-- Only administrators can modify LPK Mitra data
CREATE POLICY "Only administrators can modify LPK Mitra" ON lpk_mitra FOR ALL USING (
    auth.user_has_role('administrator')
);

-- =====================================================
-- Siswa RLS Policies
-- =====================================================

-- LPK Mitra users can only see students from their LPK
CREATE POLICY "LPK Mitra can view own students" ON siswa FOR SELECT USING (
    auth.user_has_role('lpk_mitra') AND lpk_mitra_id = auth.user_lpk_mitra_id()
);

-- LPK Mitra users can only modify students from their LPK
CREATE POLICY "LPK Mitra can modify own students" ON siswa FOR ALL USING (
    auth.user_has_role('lpk_mitra') AND lpk_mitra_id = auth.user_lpk_mitra_id()
);

-- Administrators can view all students
CREATE POLICY "Administrators can view all students" ON siswa FOR SELECT USING (
    auth.user_has_role('administrator')
);

-- Administrators can modify all students
CREATE POLICY "Administrators can modify all students" ON siswa FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Divisi Education can view and modify students (for education programs)
CREATE POLICY "Divisi Education can access students" ON siswa FOR ALL USING (
    auth.user_has_role('divisi_education')
);

-- Divisi Recruitment can view students
CREATE POLICY "Divisi Recruitment can view students" ON siswa FOR SELECT USING (
    auth.user_has_role('divisi_recruitment')
);

-- Pemberangkatan can view and modify students (for departure processing)
CREATE POLICY "Pemberangkatan can access students" ON siswa FOR ALL USING (
    auth.user_has_role('pemberangkatan')
);

-- Pimpinan can view all students
CREATE POLICY "Pimpinan can view all students" ON siswa FOR SELECT USING (
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Job Order RLS Policies
-- =====================================================

-- Administrators can access all job orders
CREATE POLICY "Administrators can access all job orders" ON job_order FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Divisi Recruitment can access all job orders
CREATE POLICY "Divisi Recruitment can access job orders" ON job_order FOR ALL USING (
    auth.user_has_role('divisi_recruitment')
);

-- Other roles can view job orders
CREATE POLICY "Other roles can view job orders" ON job_order FOR SELECT USING (
    auth.user_has_role('lpk_mitra') OR 
    auth.user_has_role('divisi_education') OR 
    auth.user_has_role('pemberangkatan') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Kumiai RLS Policies
-- =====================================================

-- Administrators can access all kumiai data
CREATE POLICY "Administrators can access all kumiai" ON kumiai FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Divisi Recruitment can access all kumiai data
CREATE POLICY "Divisi Recruitment can access kumiai" ON kumiai FOR ALL USING (
    auth.user_has_role('divisi_recruitment')
);

-- Other roles can view kumiai data
CREATE POLICY "Other roles can view kumiai" ON kumiai FOR SELECT USING (
    auth.user_has_role('lpk_mitra') OR 
    auth.user_has_role('divisi_education') OR 
    auth.user_has_role('pemberangkatan') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Perusahaan Penerima RLS Policies
-- =====================================================

-- Administrators can access all company data
CREATE POLICY "Administrators can access all companies" ON perusahaan_penerima FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Divisi Recruitment can access all company data
CREATE POLICY "Divisi Recruitment can access companies" ON perusahaan_penerima FOR ALL USING (
    auth.user_has_role('divisi_recruitment')
);

-- Pemberangkatan can view and modify company data
CREATE POLICY "Pemberangkatan can access companies" ON perusahaan_penerima FOR ALL USING (
    auth.user_has_role('pemberangkatan')
);

-- Other roles can view company data
CREATE POLICY "Other roles can view companies" ON perusahaan_penerima FOR SELECT USING (
    auth.user_has_role('lpk_mitra') OR 
    auth.user_has_role('divisi_education') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Program Pendidikan RLS Policies
-- =====================================================

-- Administrators can access all education programs
CREATE POLICY "Administrators can access all education programs" ON program_pendidikan FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Divisi Education can access all education programs
CREATE POLICY "Divisi Education can access education programs" ON program_pendidikan FOR ALL USING (
    auth.user_has_role('divisi_education')
);

-- Other roles can view education programs
CREATE POLICY "Other roles can view education programs" ON program_pendidikan FOR SELECT USING (
    auth.user_has_role('lpk_mitra') OR 
    auth.user_has_role('divisi_recruitment') OR 
    auth.user_has_role('pemberangkatan') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Penempatan Siswa RLS Policies
-- =====================================================

-- LPK Mitra can only see placements for their students
CREATE POLICY "LPK Mitra can view own student placements" ON penempatan_siswa FOR SELECT USING (
    auth.user_has_role('lpk_mitra') AND 
    EXISTS (
        SELECT 1 FROM siswa s 
        WHERE s.id = penempatan_siswa.siswa_id 
        AND s.lpk_mitra_id = auth.user_lpk_mitra_id()
    )
);

-- Administrators can access all placements
CREATE POLICY "Administrators can access all placements" ON penempatan_siswa FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Pemberangkatan can access all placements
CREATE POLICY "Pemberangkatan can access placements" ON penempatan_siswa FOR ALL USING (
    auth.user_has_role('pemberangkatan')
);

-- Other roles can view placements
CREATE POLICY "Other roles can view placements" ON penempatan_siswa FOR SELECT USING (
    auth.user_has_role('divisi_education') OR 
    auth.user_has_role('divisi_recruitment') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Dokumen Siswa RLS Policies
-- =====================================================

-- LPK Mitra can only access documents for their students
CREATE POLICY "LPK Mitra can access own student documents" ON dokumen_siswa FOR ALL USING (
    auth.user_has_role('lpk_mitra') AND 
    EXISTS (
        SELECT 1 FROM siswa s 
        WHERE s.id = dokumen_siswa.siswa_id 
        AND s.lpk_mitra_id = auth.user_lpk_mitra_id()
    )
);

-- Administrators can access all documents
CREATE POLICY "Administrators can access all documents" ON dokumen_siswa FOR ALL USING (
    auth.user_has_role('administrator')
);

-- Pemberangkatan can access all documents
CREATE POLICY "Pemberangkatan can access documents" ON dokumen_siswa FOR ALL USING (
    auth.user_has_role('pemberangkatan')
);

-- Other roles can view documents
CREATE POLICY "Other roles can view documents" ON dokumen_siswa FOR SELECT USING (
    auth.user_has_role('divisi_education') OR 
    auth.user_has_role('divisi_recruitment') OR
    auth.user_has_role('pimpinan')
);

-- =====================================================
-- Grant necessary permissions to authenticated users
-- =====================================================

GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_has_role(text) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_lpk_mitra_id() TO authenticated;
