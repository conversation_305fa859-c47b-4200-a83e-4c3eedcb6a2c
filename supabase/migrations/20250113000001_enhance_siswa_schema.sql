-- =====================================================
-- Enhanced Siswa Schema Migration
-- Based on Laravel Template Analysis
-- =====================================================

-- Add new columns to siswa table for comprehensive data
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS jp_nama VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tempat_lahir VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS umur INTEGER;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS agama VARCHAR(100);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tinggi_badan DECIMAL(5,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS berat_badan DECIMAL(5,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS mata_kanan VARCHAR(10);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS mata_kiri VARCHAR(10);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ukuran_sepatu DECIMAL(4,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ukuran_kepala DECIMAL(4,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ukuran_pinggang DECIMAL(5,2);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS merokok VARCHAR(50);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS minum_sake VARCHAR(50);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS penggunaan_tangan VARCHAR(20);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS buta_warna VARCHAR(20);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS ket_buta_warna VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS hobi VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS bakat_khusus VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS minat_kerja JSONB;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pengalaman_kerja JSONB;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS kelebihan JSONB;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS kekurangan VARCHAR(255);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tujuan_ke_jepang TEXT;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS target_menabung VARCHAR(100);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS tgl_masuk_lpk DATE;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS lama_belajar VARCHAR(100);
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS is_available BOOLEAN DEFAULT true;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false;
ALTER TABLE siswa ADD COLUMN IF NOT EXISTS foto VARCHAR(255);

-- Table untuk pendidikan siswa
CREATE TABLE IF NOT EXISTS siswa_pendidikan (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) ON DELETE CASCADE,
    jenjang VARCHAR(100) NOT NULL,
    nama_sekolah VARCHAR(255) NOT NULL,
    tahun_lulus INTEGER,
    nilai_rata_rata DECIMAL(4,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table untuk pengalaman kerja siswa
CREATE TABLE IF NOT EXISTS siswa_pengalaman_kerja (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) ON DELETE CASCADE,
    nama_perusahaan VARCHAR(255) NOT NULL,
    posisi VARCHAR(255) NOT NULL,
    tahun_mulai INTEGER,
    tahun_selesai INTEGER,
    deskripsi TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table untuk keluarga siswa
CREATE TABLE IF NOT EXISTS siswa_keluarga (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) ON DELETE CASCADE,
    nama VARCHAR(255) NOT NULL,
    hubungan VARCHAR(100) NOT NULL,
    umur INTEGER,
    pekerjaan VARCHAR(255),
    alamat TEXT,
    no_telp VARCHAR(20),
    tipe VARCHAR(20), -- 'indonesia' or 'jepang'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table untuk attachments/documents
CREATE TABLE IF NOT EXISTS siswa_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    siswa_id UUID REFERENCES siswa(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(100),
    file_size INTEGER,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_siswa_is_available ON siswa(is_available);
CREATE INDEX IF NOT EXISTS idx_siswa_is_verified ON siswa(is_verified);
CREATE INDEX IF NOT EXISTS idx_siswa_lpk_id ON siswa(lpk_id);
CREATE INDEX IF NOT EXISTS idx_siswa_nama_lengkap ON siswa(nama_lengkap);
CREATE INDEX IF NOT EXISTS idx_siswa_nik ON siswa(nik);
CREATE INDEX IF NOT EXISTS idx_siswa_pendidikan_siswa_id ON siswa_pendidikan(siswa_id);
CREATE INDEX IF NOT EXISTS idx_siswa_pengalaman_kerja_siswa_id ON siswa_pengalaman_kerja(siswa_id);
CREATE INDEX IF NOT EXISTS idx_siswa_keluarga_siswa_id ON siswa_keluarga(siswa_id);
CREATE INDEX IF NOT EXISTS idx_siswa_attachments_siswa_id ON siswa_attachments(siswa_id);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_siswa_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_siswa_pendidikan_updated_at 
    BEFORE UPDATE ON siswa_pendidikan 
    FOR EACH ROW EXECUTE FUNCTION update_siswa_updated_at();

CREATE TRIGGER update_siswa_pengalaman_kerja_updated_at 
    BEFORE UPDATE ON siswa_pengalaman_kerja 
    FOR EACH ROW EXECUTE FUNCTION update_siswa_updated_at();

CREATE TRIGGER update_siswa_keluarga_updated_at 
    BEFORE UPDATE ON siswa_keluarga 
    FOR EACH ROW EXECUTE FUNCTION update_siswa_updated_at();

-- Enable RLS for new tables
ALTER TABLE siswa_pendidikan ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa_pengalaman_kerja ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa_keluarga ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa_attachments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for new tables
CREATE POLICY "Allow all operations on siswa_pendidikan" ON siswa_pendidikan
    FOR ALL USING (true);

CREATE POLICY "Allow all operations on siswa_pengalaman_kerja" ON siswa_pengalaman_kerja
    FOR ALL USING (true);

CREATE POLICY "Allow all operations on siswa_keluarga" ON siswa_keluarga
    FOR ALL USING (true);

CREATE POLICY "Allow all operations on siswa_attachments" ON siswa_attachments
    FOR ALL USING (true);

-- Create view for comprehensive siswa data
CREATE OR REPLACE VIEW v_siswa_comprehensive AS
SELECT 
    s.*,
    lm.nama_lpk,
    lm.kota as lpk_kota,
    COUNT(sp.id) as total_pendidikan,
    COUNT(spk.id) as total_pengalaman_kerja,
    COUNT(sk.id) as total_keluarga,
    COUNT(sa.id) as total_attachments
FROM siswa s
LEFT JOIN lpk_mitra lm ON s.lpk_id = lm.id
LEFT JOIN siswa_pendidikan sp ON s.id = sp.siswa_id
LEFT JOIN siswa_pengalaman_kerja spk ON s.id = spk.siswa_id
LEFT JOIN siswa_keluarga sk ON s.id = sk.siswa_id
LEFT JOIN siswa_attachments sa ON s.id = sa.siswa_id
GROUP BY s.id, lm.nama_lpk, lm.kota;

-- Insert default values for existing siswa
UPDATE siswa SET 
    is_available = true,
    is_verified = false,
    created_at = COALESCE(created_at, NOW()),
    updated_at = COALESCE(updated_at, NOW())
WHERE is_available IS NULL OR is_verified IS NULL;

-- Add comments for documentation
COMMENT ON TABLE siswa_pendidikan IS 'Riwayat pendidikan siswa';
COMMENT ON TABLE siswa_pengalaman_kerja IS 'Riwayat pengalaman kerja siswa';
COMMENT ON TABLE siswa_keluarga IS 'Data keluarga siswa (Indonesia dan Jepang)';
COMMENT ON TABLE siswa_attachments IS 'Dokumen/attachment siswa';

COMMENT ON COLUMN siswa.jp_nama IS 'Nama dalam bahasa Jepang';
COMMENT ON COLUMN siswa.is_available IS 'Status ketersediaan siswa';
COMMENT ON COLUMN siswa.is_verified IS 'Status verifikasi data siswa';
COMMENT ON COLUMN siswa.minat_kerja IS 'Array minat kerja siswa';
COMMENT ON COLUMN siswa.kelebihan IS 'Array kelebihan siswa';
COMMENT ON COLUMN siswa_keluarga.tipe IS 'Tipe keluarga: indonesia atau jepang'; 