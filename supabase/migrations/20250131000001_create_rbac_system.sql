-- =====================================================
-- Role-Based Access Control (RBAC) System Migration
-- Dashboard Magang <PERSON> - User Access Management
-- =====================================================

-- Drop existing user_role enum and recreate with new roles
DROP TYPE IF EXISTS user_role CASCADE;
CREATE TYPE user_role AS ENUM (
    'administrator',
    'lpk_mitra', 
    'divisi_education',
    'divisi_recruitment', 
    'pemberangkatan',
    'pimpinan'
);

-- =====================================================
-- RBAC Tables
-- =====================================================

-- Roles table - Define available roles in the system
CREATE TABLE roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Permissions table - Define available permissions
CREATE TABLE permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL, -- e.g., 'siswa', 'lpk_mitra', 'job_order'
    action VARCHAR(20) NOT NULL, -- 'create', 'read', 'update', 'delete'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Role Permissions junction table
CREATE TABLE role_permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- User Role Assignments - Users can have multiple roles
CREATE TABLE user_role_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES auth.users(id),
    lpk_mitra_id UUID REFERENCES lpk_mitra(id), -- For LPK Mitra users to restrict data access
    is_active BOOLEAN DEFAULT true,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, role_id)
);

-- =====================================================
-- Update user_profiles table
-- =====================================================

-- Add new columns to user_profiles
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS position VARCHAR(100),
ADD COLUMN IF NOT EXISTS department VARCHAR(100),
ADD COLUMN IF NOT EXISTS lpk_mitra_id UUID REFERENCES lpk_mitra(id);

-- Update role column to use new enum (will be handled by data migration)
-- ALTER TABLE user_profiles ALTER COLUMN role TYPE user_role USING role::text::user_role;

-- =====================================================
-- Insert Default Roles
-- =====================================================

INSERT INTO roles (name, display_name, description) VALUES
('administrator', 'Administrator', 'Akses penuh ke semua fitur dan data sistem'),
('lpk_mitra', 'LPK Mitra', 'Akses terbatas hanya ke data yang diinputkan oleh LPK sendiri'),
('divisi_education', 'Divisi Education', 'Akses ke menu dan data pendidikan'),
('divisi_recruitment', 'Divisi Recruitment', 'Akses ke menu dan data recruitment'),
('pemberangkatan', 'Pemberangkatan', 'Akses ke menu pemberkasan dan pemberangkatan'),
('pimpinan', 'Pimpinan', 'Akses read-only ke semua laporan dan data untuk monitoring');

-- =====================================================
-- Insert Default Permissions
-- =====================================================

-- Dashboard permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('dashboard.read', 'Lihat Dashboard', 'dashboard', 'read');

-- Siswa permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('siswa.create', 'Tambah Data Siswa', 'siswa', 'create'),
('siswa.read', 'Lihat Data Siswa', 'siswa', 'read'),
('siswa.update', 'Edit Data Siswa', 'siswa', 'update'),
('siswa.delete', 'Hapus Data Siswa', 'siswa', 'delete');

-- LPK Mitra permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('lpk_mitra.create', 'Tambah Data LPK Mitra', 'lpk_mitra', 'create'),
('lpk_mitra.read', 'Lihat Data LPK Mitra', 'lpk_mitra', 'read'),
('lpk_mitra.update', 'Edit Data LPK Mitra', 'lpk_mitra', 'update'),
('lpk_mitra.delete', 'Hapus Data LPK Mitra', 'lpk_mitra', 'delete');

-- Job Order permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('job_order.create', 'Tambah Job Order', 'job_order', 'create'),
('job_order.read', 'Lihat Job Order', 'job_order', 'read'),
('job_order.update', 'Edit Job Order', 'job_order', 'update'),
('job_order.delete', 'Hapus Job Order', 'job_order', 'delete');

-- Kumiai permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('kumiai.create', 'Tambah Data Kumiai', 'kumiai', 'create'),
('kumiai.read', 'Lihat Data Kumiai', 'kumiai', 'read'),
('kumiai.update', 'Edit Data Kumiai', 'kumiai', 'update'),
('kumiai.delete', 'Hapus Data Kumiai', 'kumiai', 'delete');

-- Pendidikan permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('pendidikan.create', 'Tambah Program Pendidikan', 'pendidikan', 'create'),
('pendidikan.read', 'Lihat Program Pendidikan', 'pendidikan', 'read'),
('pendidikan.update', 'Edit Program Pendidikan', 'pendidikan', 'update'),
('pendidikan.delete', 'Hapus Program Pendidikan', 'pendidikan', 'delete');

-- Penempatan permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('penempatan.create', 'Tambah Data Penempatan', 'penempatan', 'create'),
('penempatan.read', 'Lihat Data Penempatan', 'penempatan', 'read'),
('penempatan.update', 'Edit Data Penempatan', 'penempatan', 'update'),
('penempatan.delete', 'Hapus Data Penempatan', 'penempatan', 'delete');

-- Dokumen permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('dokumen.create', 'Upload Dokumen', 'dokumen', 'create'),
('dokumen.read', 'Lihat Dokumen', 'dokumen', 'read'),
('dokumen.update', 'Edit Dokumen', 'dokumen', 'update'),
('dokumen.delete', 'Hapus Dokumen', 'dokumen', 'delete');

-- Pendaftaran permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('pendaftaran.create', 'Tambah Pendaftaran', 'pendaftaran', 'create'),
('pendaftaran.read', 'Lihat Pendaftaran', 'pendaftaran', 'read'),
('pendaftaran.update', 'Edit Pendaftaran', 'pendaftaran', 'update'),
('pendaftaran.delete', 'Hapus Pendaftaran', 'pendaftaran', 'delete');

-- User Management permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('users.create', 'Tambah User', 'users', 'create'),
('users.read', 'Lihat Data User', 'users', 'read'),
('users.update', 'Edit Data User', 'users', 'update'),
('users.delete', 'Hapus User', 'users', 'delete');

-- Role Management permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('roles.create', 'Tambah Role', 'roles', 'create'),
('roles.read', 'Lihat Data Role', 'roles', 'read'),
('roles.update', 'Edit Role & Permissions', 'roles', 'update'),
('roles.delete', 'Hapus Role', 'roles', 'delete');

-- Reports permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('reports.read', 'Lihat Laporan', 'reports', 'read'),
('reports.export', 'Export Laporan', 'reports', 'create');

-- =====================================================
-- Default Role Permissions Assignments
-- =====================================================

-- Administrator - Full access to everything
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'administrator';

-- LPK Mitra - Limited access to own data
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'lpk_mitra'
AND p.name IN (
    'dashboard.read',
    'siswa.create', 'siswa.read', 'siswa.update', 'siswa.delete',
    'pendaftaran.create', 'pendaftaran.read', 'pendaftaran.update', 'pendaftaran.delete',
    'dokumen.create', 'dokumen.read', 'dokumen.update', 'dokumen.delete'
);

-- Divisi Education - Education related permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'divisi_education'
AND p.name IN (
    'dashboard.read',
    'pendidikan.create', 'pendidikan.read', 'pendidikan.update', 'pendidikan.delete',
    'siswa.read', 'siswa.update',
    'reports.read'
);

-- Divisi Recruitment - Recruitment related permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'divisi_recruitment'
AND p.name IN (
    'dashboard.read',
    'job_order.create', 'job_order.read', 'job_order.update', 'job_order.delete',
    'kumiai.create', 'kumiai.read', 'kumiai.update', 'kumiai.delete',
    'lpk_mitra.read',
    'siswa.read',
    'reports.read'
);

-- Pemberangkatan - Departure and documentation permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'pemberangkatan'
AND p.name IN (
    'dashboard.read',
    'penempatan.create', 'penempatan.read', 'penempatan.update', 'penempatan.delete',
    'dokumen.create', 'dokumen.read', 'dokumen.update', 'dokumen.delete',
    'siswa.read', 'siswa.update',
    'reports.read'
);

-- Pimpinan - Read-only access to all reports and data
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'pimpinan'
AND p.module IN ('dashboard', 'reports')
AND p.action = 'read';

-- Add read permissions for all modules for Pimpinan
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'pimpinan'
AND p.action = 'read'
AND p.module NOT IN ('users', 'roles'); -- Exclude user/role management

-- =====================================================
-- Helper Functions
-- =====================================================

-- Function to get user permissions
CREATE OR REPLACE FUNCTION get_user_permissions(user_uuid UUID)
RETURNS TABLE(permission_name VARCHAR, module VARCHAR, action VARCHAR) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT p.name, p.module, p.action
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_role_assignments ura ON rp.role_id = ura.role_id
    WHERE ura.user_id = user_uuid
    AND ura.is_active = true
    AND (ura.expires_at IS NULL OR ura.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(user_uuid UUID, permission_name VARCHAR)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM get_user_permissions(user_uuid)
        WHERE permission_name = get_user_permissions.permission_name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- Indexes for Performance
-- =====================================================

CREATE INDEX idx_user_role_assignments_user_id ON user_role_assignments(user_id);
CREATE INDEX idx_user_role_assignments_role_id ON user_role_assignments(role_id);
CREATE INDEX idx_user_role_assignments_active ON user_role_assignments(is_active);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);

-- =====================================================
-- Row Level Security (RLS) Policies
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_role_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for roles table
CREATE POLICY "Users can view roles" ON roles FOR SELECT USING (true);
CREATE POLICY "Only admins can modify roles" ON roles FOR ALL USING (
    EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = 'administrator'
        AND ura.is_active = true
    )
);

-- RLS Policies for permissions table
CREATE POLICY "Users can view permissions" ON permissions FOR SELECT USING (true);
CREATE POLICY "Only admins can modify permissions" ON permissions FOR ALL USING (
    EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = 'administrator'
        AND ura.is_active = true
    )
);

-- RLS Policies for role_permissions table
CREATE POLICY "Users can view role permissions" ON role_permissions FOR SELECT USING (true);
CREATE POLICY "Only admins can modify role permissions" ON role_permissions FOR ALL USING (
    EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = 'administrator'
        AND ura.is_active = true
    )
);

-- RLS Policies for user_role_assignments table
CREATE POLICY "Users can view own role assignments" ON user_role_assignments FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = 'administrator'
        AND ura.is_active = true
    )
);

CREATE POLICY "Only admins can modify user role assignments" ON user_role_assignments FOR ALL USING (
    EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN roles r ON ura.role_id = r.id
        WHERE ura.user_id = auth.uid()
        AND r.name = 'administrator'
        AND ura.is_active = true
    )
);
