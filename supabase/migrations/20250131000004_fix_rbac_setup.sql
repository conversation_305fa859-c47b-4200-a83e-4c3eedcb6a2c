-- =====================================================
-- Fix RBAC Setup - Comprehensive Migration
-- Dashboard Magang <PERSON>pang - User Access Management
-- =====================================================

-- This migration ensures all RBAC components are properly set up
-- Run this if previous migrations had issues

-- =====================================================
-- 1. Ensure all required tables exist
-- =====================================================

-- Create roles table if not exists
CREATE TABLE IF NOT EXISTS roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create permissions table if not exists
CREATE TABLE IF NOT EXISTS permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create role_permissions table if not exists
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- Create user_role_assignments table if not exists
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES auth.users(id),
    lpk_mitra_id UUID REFERENCES lpk_mitra(id),
    is_active BOOLEAN DEFAULT true,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, role_id)
);

-- =====================================================
-- 2. Insert roles if not exists
-- =====================================================

INSERT INTO roles (name, display_name, description) VALUES
('administrator', 'Administrator', 'Akses penuh ke semua fitur dan data sistem'),
('lpk_mitra', 'LPK Mitra', 'Akses terbatas hanya ke data yang diinputkan oleh LPK sendiri'),
('divisi_education', 'Divisi Education', 'Akses ke menu dan data pendidikan'),
('divisi_recruitment', 'Divisi Recruitment', 'Akses ke menu dan data recruitment'),
('pemberangkatan', 'Pemberangkatan', 'Akses ke menu pemberkasan dan pemberangkatan'),
('pimpinan', 'Pimpinan', 'Akses read-only ke semua laporan dan data untuk monitoring')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 3. Insert permissions if not exists
-- =====================================================

-- Dashboard permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('dashboard.read', 'Lihat Dashboard', 'dashboard', 'read')
ON CONFLICT (name) DO NOTHING;

-- Siswa permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('siswa.create', 'Tambah Data Siswa', 'siswa', 'create'),
('siswa.read', 'Lihat Data Siswa', 'siswa', 'read'),
('siswa.update', 'Edit Data Siswa', 'siswa', 'update'),
('siswa.delete', 'Hapus Data Siswa', 'siswa', 'delete')
ON CONFLICT (name) DO NOTHING;

-- LPK Mitra permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('lpk_mitra.create', 'Tambah Data LPK Mitra', 'lpk_mitra', 'create'),
('lpk_mitra.read', 'Lihat Data LPK Mitra', 'lpk_mitra', 'read'),
('lpk_mitra.update', 'Edit Data LPK Mitra', 'lpk_mitra', 'update'),
('lpk_mitra.delete', 'Hapus Data LPK Mitra', 'lpk_mitra', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Job Order permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('job_order.create', 'Tambah Job Order', 'job_order', 'create'),
('job_order.read', 'Lihat Job Order', 'job_order', 'read'),
('job_order.update', 'Edit Job Order', 'job_order', 'update'),
('job_order.delete', 'Hapus Job Order', 'job_order', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Kumiai permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('kumiai.create', 'Tambah Data Kumiai', 'kumiai', 'create'),
('kumiai.read', 'Lihat Data Kumiai', 'kumiai', 'read'),
('kumiai.update', 'Edit Data Kumiai', 'kumiai', 'update'),
('kumiai.delete', 'Hapus Data Kumiai', 'kumiai', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Pendidikan permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('pendidikan.create', 'Tambah Program Pendidikan', 'pendidikan', 'create'),
('pendidikan.read', 'Lihat Program Pendidikan', 'pendidikan', 'read'),
('pendidikan.update', 'Edit Program Pendidikan', 'pendidikan', 'update'),
('pendidikan.delete', 'Hapus Program Pendidikan', 'pendidikan', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Penempatan permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('penempatan.create', 'Tambah Data Penempatan', 'penempatan', 'create'),
('penempatan.read', 'Lihat Data Penempatan', 'penempatan', 'read'),
('penempatan.update', 'Edit Data Penempatan', 'penempatan', 'update'),
('penempatan.delete', 'Hapus Data Penempatan', 'penempatan', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Dokumen permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('dokumen.create', 'Upload Dokumen', 'dokumen', 'create'),
('dokumen.read', 'Lihat Dokumen', 'dokumen', 'read'),
('dokumen.update', 'Edit Dokumen', 'dokumen', 'update'),
('dokumen.delete', 'Hapus Dokumen', 'dokumen', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Pendaftaran permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('pendaftaran.create', 'Tambah Pendaftaran', 'pendaftaran', 'create'),
('pendaftaran.read', 'Lihat Pendaftaran', 'pendaftaran', 'read'),
('pendaftaran.update', 'Edit Pendaftaran', 'pendaftaran', 'update'),
('pendaftaran.delete', 'Hapus Pendaftaran', 'pendaftaran', 'delete')
ON CONFLICT (name) DO NOTHING;

-- User Management permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('users.create', 'Tambah User', 'users', 'create'),
('users.read', 'Lihat Data User', 'users', 'read'),
('users.update', 'Edit Data User', 'users', 'update'),
('users.delete', 'Hapus User', 'users', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Role Management permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('roles.create', 'Tambah Role', 'roles', 'create'),
('roles.read', 'Lihat Data Role', 'roles', 'read'),
('roles.update', 'Edit Role & Permissions', 'roles', 'update'),
('roles.delete', 'Hapus Role', 'roles', 'delete')
ON CONFLICT (name) DO NOTHING;

-- Reports permissions
INSERT INTO permissions (name, display_name, module, action) VALUES
('reports.read', 'Lihat Laporan', 'reports', 'read'),
('reports.export', 'Export Laporan', 'reports', 'create')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 4. Setup default role permissions
-- =====================================================

-- Clear existing role permissions to avoid conflicts
DELETE FROM role_permissions;

-- Administrator - Full access to everything
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'administrator';

-- LPK Mitra - Limited access to own data
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'lpk_mitra' 
AND p.name IN (
    'dashboard.read',
    'siswa.create', 'siswa.read', 'siswa.update', 'siswa.delete',
    'pendaftaran.create', 'pendaftaran.read', 'pendaftaran.update', 'pendaftaran.delete',
    'dokumen.create', 'dokumen.read', 'dokumen.update', 'dokumen.delete'
);

-- Divisi Education - Education related permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'divisi_education' 
AND p.name IN (
    'dashboard.read',
    'pendidikan.create', 'pendidikan.read', 'pendidikan.update', 'pendidikan.delete',
    'siswa.read', 'siswa.update',
    'reports.read'
);

-- Divisi Recruitment - Recruitment related permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'divisi_recruitment' 
AND p.name IN (
    'dashboard.read',
    'job_order.create', 'job_order.read', 'job_order.update', 'job_order.delete',
    'kumiai.create', 'kumiai.read', 'kumiai.update', 'kumiai.delete',
    'lpk_mitra.read',
    'siswa.read',
    'reports.read'
);

-- Pemberangkatan - Departure and documentation permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'pemberangkatan' 
AND p.name IN (
    'dashboard.read',
    'penempatan.create', 'penempatan.read', 'penempatan.update', 'penempatan.delete',
    'dokumen.create', 'dokumen.read', 'dokumen.update', 'dokumen.delete',
    'siswa.read', 'siswa.update',
    'reports.read'
);

-- Pimpinan - Read-only access to all reports and data
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'pimpinan' 
AND (p.name LIKE '%.read' OR p.name = 'reports.export');

-- =====================================================
-- 5. Create helper functions
-- =====================================================

-- Function to get user permissions
CREATE OR REPLACE FUNCTION get_user_permissions(user_uuid UUID)
RETURNS TABLE(permission_name VARCHAR, module VARCHAR, action VARCHAR) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT p.name, p.module, p.action
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_role_assignments ura ON rp.role_id = ura.role_id
    WHERE ura.user_id = user_uuid 
    AND ura.is_active = true
    AND (ura.expires_at IS NULL OR ura.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(user_uuid UUID, permission_name VARCHAR)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM get_user_permissions(user_uuid) 
        WHERE permission_name = get_user_permissions.permission_name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. Success message
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ RBAC System setup completed successfully!';
    RAISE NOTICE 'Tables created: roles, permissions, role_permissions, user_role_assignments';
    RAISE NOTICE 'Default roles and permissions inserted';
    RAISE NOTICE 'Helper functions created';
    RAISE NOTICE 'Next step: Create admin user and assign administrator role';
END $$;
