# 🌱 Seed Data Guide

## 📋 Daftar Isi

- [Overview](#-overview)
- [Available Seed Data](#-available-seed-data)
- [Installation Steps](#-installation-steps)
- [Data Structure](#-data-structure)
- [Using with Supabase](#-using-with-supabase)
- [Customization](#-customization)
- [Troubleshooting](#-troubleshooting)

## 🎯 Overview

Seed data untuk Dashboard Sistem Magang Jepang telah dibuat berdasarkan file CSV yang berisi data real siswa magang. Data ini mencakup:

- **170+ siswa** dengan data lengkap
- **3 LPK** (Yutaka, LPK Dummy, LPK Central Java)
- **3 Kumiai** (Gokei, TIC, Osaka Workers)
- **4 Perusahaan** di berbagai kota Jepang
- **110+ penempatan** siswa yang sudah bekerja
- **Jenis dokumen** dan **program pendidikan** standar

## 📊 Available Seed Data

### 1. **Sample Data (Recommended untuk Development)**
- **File**: `scripts/seed-data-yutaka.sql`
- **Siswa**: 50 siswa
- **Penempatan**: 16 penempatan
- **Size**: ~50KB
- **Use Case**: Development, testing, demo

### 2. **Full Data (Production Ready)**
- **File**: `scripts/seed-data-full.sql`
- **Siswa**: 170+ siswa
- **Penempatan**: 110+ penempatan
- **Size**: ~200KB
- **Use Case**: Production, staging

### 3. **Base Migration**
- **File**: `supabase/migrations/20250111000002_insert_seed_data.sql`
- **Content**: Jenis dokumen, program pendidikan, views, functions
- **Use Case**: Essential data structure

## 🚀 Installation Steps

### Option 1: Using Supabase CLI (Recommended)

```bash
# 1. Ensure you're in the project directory
cd dashboard-magang-jepang

# 2. Apply base migration (if not already done)
supabase db push

# 3. Apply seed data migration
supabase db reset  # This will apply all migrations including seed data

# 4. Insert student data (choose one)
# For sample data:
psql $DATABASE_URL -f scripts/seed-data-yutaka.sql

# For full data:
psql $DATABASE_URL -f scripts/seed-data-full.sql
```

### Option 2: Using Supabase Dashboard

1. **Open Supabase Dashboard**
   - Go to your project dashboard
   - Navigate to SQL Editor

2. **Run Base Migration**
   ```sql
   -- Copy and paste content from:
   -- supabase/migrations/20250111000002_insert_seed_data.sql
   ```

3. **Insert Student Data**
   ```sql
   -- Copy and paste content from either:
   -- scripts/seed-data-yutaka.sql (sample)
   -- scripts/seed-data-full.sql (complete)
   ```

### Option 3: Using psql Command Line

```bash
# Connect to your Supabase database
psql "postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"

# Run the migrations
\i supabase/migrations/20250111000002_insert_seed_data.sql
\i scripts/seed-data-yutaka.sql
```

## 📋 Data Structure

### LPK Mitra
```json
{
  "Yutaka": {
    "alamat": "Jl. Raya Utama No. 123, Jakarta Pusat",
    "pimpinan": "Bapak Yutaka Tanaka",
    "kontak": "Ibu Sari Wijaya",
    "siswa": "~120 siswa (70% dari total)"
  },
  "LPK Dummy": {
    "alamat": "Jl. Pendidikan No. 456, Bandung",
    "siswa": "~25 siswa (15% dari total)"
  },
  "LPK Central Java": {
    "alamat": "Jl. Pemuda No. 789, Semarang",
    "siswa": "~25 siswa (15% dari total)"
  }
}
```

### Kumiai & Perusahaan
```json
{
  "Gokei Cloud Kyodo Kumiai": {
    "kode": "GOKEI",
    "lokasi": "Tokyo",
    "perusahaan": ["Tokyo Manufacturing", "Kyoto Electronics"]
  },
  "TIC Kyodo Kumiai": {
    "kode": "TIC", 
    "lokasi": "Tokyo",
    "perusahaan": ["Osaka Technical Industries"]
  },
  "Osaka Workers Kumiai": {
    "kode": "OWK",
    "lokasi": "Osaka", 
    "perusahaan": ["Nagoya Automotive Parts"]
  }
}
```

### Student Distribution
- **Gender**: ~60% Laki-laki, ~40% Perempuan
- **Age**: 20-30 tahun (mayoritas 22-25 tahun)
- **Education**: Mayoritas SMA/SMK
- **Status**: Semua approved untuk testing
- **Placement**: ~65% sudah ditempatkan dan bekerja

## 🔧 Using with Supabase

### 1. **Authentication Setup**
Setelah seed data diimport, buat user accounts:

```bash
# Via Supabase Dashboard > Authentication > Users
# Create admin user:
Email: <EMAIL>
Password: [secure-password]

# Create operator user:
Email: <EMAIL>  
Password: [secure-password]
```

### 2. **User Profiles Setup**
```sql
-- Link auth users to profiles
INSERT INTO user_profiles (id, username, full_name, role) VALUES 
('[auth-user-id]', 'admin', 'Administrator Yutaka', 'admin'),
('[auth-user-id]', 'operator', 'Operator Yutaka', 'operator');
```

### 3. **Test Dashboard Functionality**
```sql
-- Test dashboard statistics
SELECT * FROM v_dashboard_statistics;

-- Test monthly data
SELECT * FROM get_monthly_statistics(2024);

-- Test LPK performance
SELECT * FROM get_lpk_performance();
```

## 🎨 Customization

### Adding More Students
```javascript
// Use the script template
const newStudent = {
    id: generateUUID(),
    lpk_id: 'yutaka-lpk-id',
    nama_lengkap: 'Nama Siswa Baru',
    nik: '****************',
    // ... other fields
};
```

### Modifying LPK Data
```sql
-- Update LPK information
UPDATE lpk_mitra 
SET alamat_lengkap = 'Alamat baru yang lebih lengkap',
    nomor_telepon = '021-new-number'
WHERE nama_lpk = 'Yutaka';
```

### Adding New Perusahaan
```sql
INSERT INTO perusahaan_penerima (
    id, kumiai_id, nama_perusahaan, alamat_jepang,
    kota_jepang, prefektur, bidang_usaha, 
    kontak_person, nomor_telepon, status
) VALUES (
    uuid_generate_v4(),
    (SELECT id FROM kumiai WHERE kode_kumiai = 'GOKEI'),
    'New Company Ltd.',
    'New Address in Japan',
    'Tokyo', 'Tokyo', 'Technology',
    'Contact Person', '+81-3-0000-0000', 'aktif'
);
```

## 🔍 Verification Queries

### Check Data Integrity
```sql
-- Verify all foreign keys
SELECT 
    'siswa' as table_name,
    COUNT(*) as total,
    COUNT(lpk_id) as with_lpk
FROM siswa
UNION ALL
SELECT 
    'penempatan_siswa',
    COUNT(*),
    COUNT(siswa_id)
FROM penempatan_siswa;

-- Check LPK distribution
SELECT 
    l.nama_lpk,
    COUNT(s.id) as jumlah_siswa,
    COUNT(ps.id) as jumlah_penempatan
FROM lpk_mitra l
LEFT JOIN siswa s ON l.id = s.lpk_id
LEFT JOIN penempatan_siswa ps ON s.id = ps.siswa_id
GROUP BY l.id, l.nama_lpk
ORDER BY jumlah_siswa DESC;
```

### Performance Check
```sql
-- Check query performance
EXPLAIN ANALYZE SELECT * FROM v_student_placement_summary LIMIT 10;

-- Check indexes
SELECT schemaname, tablename, indexname, indexdef 
FROM pg_indexes 
WHERE tablename IN ('siswa', 'penempatan_siswa', 'lpk_mitra');
```

## 🐛 Troubleshooting

### Common Issues

#### 1. **Foreign Key Violations**
```sql
-- Check for orphaned records
SELECT s.id, s.nama_lengkap 
FROM siswa s 
LEFT JOIN lpk_mitra l ON s.lpk_id = l.id 
WHERE l.id IS NULL;
```

#### 2. **Duplicate NIK**
```sql
-- Find duplicate NIKs
SELECT nik, COUNT(*) 
FROM siswa 
GROUP BY nik 
HAVING COUNT(*) > 1;
```

#### 3. **Missing Data**
```sql
-- Check for missing required fields
SELECT COUNT(*) as missing_phone 
FROM siswa 
WHERE nomor_hp IS NULL OR nomor_hp = '';
```

### Reset Data
```sql
-- Clear all data (be careful!)
TRUNCATE TABLE penempatan_siswa CASCADE;
TRUNCATE TABLE siswa CASCADE;
TRUNCATE TABLE perusahaan_penerima CASCADE;
TRUNCATE TABLE lpk_mitra CASCADE;
TRUNCATE TABLE kumiai CASCADE;

-- Then re-run seed data scripts
```

## 📈 Next Steps

1. **Import seed data** using preferred method
2. **Create user accounts** in Supabase Auth
3. **Test dashboard** functionality
4. **Customize data** as needed for your use case
5. **Set up RLS policies** for production
6. **Configure backup** strategy

## 📞 Support

Jika mengalami masalah dengan seed data:

1. Check logs di Supabase Dashboard
2. Verify database schema dengan `\d table_name`
3. Test queries step by step
4. Check foreign key constraints
5. Verify RLS policies tidak memblokir data

---

**Generated from**: `seed data awal 2025.csv`  
**Last Updated**: 2025-01-11  
**Total Records**: 170+ siswa, 110+ penempatan
