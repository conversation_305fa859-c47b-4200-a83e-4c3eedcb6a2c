# 🚀 API Documentation

## 📋 Daftar Isi

- [Overview](#-overview)
- [Authentication](#-authentication)
- [Base URL](#-base-url)
- [Response Format](#-response-format)
- [Error Handling](#-error-handling)
- [Endpoints](#-endpoints)
- [Rate Limiting](#-rate-limiting)
- [Examples](#-examples)

## 🎯 Overview

API Dashboard Sistem Magang Jepang menyediakan RESTful endpoints untuk mengelola data siswa, LPK, Kumiai, job order, dan fitur lainnya. API ini dibangun menggunakan Next.js API Routes dengan TypeScript.

### Features
- **RESTful Design**: Mengikuti standar REST API
- **Type Safety**: Full TypeScript support
- **Validation**: Input validation dengan Zod
- **Error Handling**: Consistent error responses
- **Authentication**: JWT-based authentication
- **Rate Limiting**: Protection against abuse

## 🔐 Authentication

### JWT Token
API menggunakan JWT (JSON Web Token) untuk authentication. Token harus disertakan dalam header `Authorization`.

```http
Authorization: Bearer <your-jwt-token>
```

### Login Endpoint
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "admin",
      "full_name": "Administrator"
    }
  }
}
```

## 🌐 Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## 📊 Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  }
}
```

## ❌ Error Handling

### HTTP Status Codes
- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Error Codes
- `VALIDATION_ERROR` - Input validation failed
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `DUPLICATE_ENTRY` - Duplicate data
- `INTERNAL_ERROR` - Server error

## 📚 Endpoints

### 👥 Siswa (Students)

#### Get All Students
```http
GET /api/siswa?page=1&limit=10&search=john&lpk_id=1&status=approved
```

**Query Parameters:**
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 10, max: 100)
- `search` (optional) - Search by name or NIK
- `lpk_id` (optional) - Filter by LPK ID
- `status` (optional) - Filter by status

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "nama_lengkap": "John Doe",
      "nik": "1234567890123456",
      "email": "<EMAIL>",
      "status_pendaftaran": "approved",
      "lpk": {
        "id": 1,
        "nama_lpk": "LPK Maju Jaya"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "totalPages": 5
  }
}
```

#### Get Student by ID
```http
GET /api/siswa/[id]
```

#### Create New Student
```http
POST /api/siswa
Content-Type: application/json

{
  "lpk_id": 1,
  "nama_lengkap": "John Doe",
  "nik": "1234567890123456",
  "tempat_lahir": "Jakarta",
  "tanggal_lahir": "1990-01-01",
  "jenis_kelamin": "L",
  "agama": "Islam",
  "alamat_lengkap": "Jl. Merdeka No. 123",
  "kelurahan": "Merdeka",
  "kecamatan": "Pusat",
  "kota_kabupaten": "Jakarta",
  "provinsi": "DKI Jakarta",
  "nomor_hp": "081234567890",
  "email": "<EMAIL>",
  "pendidikan_terakhir": "SMA",
  "nama_sekolah": "SMA Negeri 1",
  "tahun_lulus": 2008
}
```

#### Update Student
```http
PUT /api/siswa/[id]
Content-Type: application/json

{
  "nama_lengkap": "John Doe Updated",
  "email": "<EMAIL>"
}
```

#### Delete Student
```http
DELETE /api/siswa/[id]
```

### 🏢 LPK Mitra

#### Get All LPK
```http
GET /api/lpk?page=1&limit=10&search=maju&status=aktif
```

#### Create New LPK
```http
POST /api/lpk
Content-Type: application/json

{
  "nama_lpk": "LPK Maju Jaya",
  "alamat_lengkap": "Jl. Pendidikan No. 456",
  "kota": "Bandung",
  "provinsi": "Jawa Barat",
  "nama_pimpinan": "Budi Santoso",
  "kontak_person": "Siti Aminah",
  "nomor_telepon": "022-1234567",
  "email": "<EMAIL>",
  "website": "https://lpkmaju.com"
}
```

### 🇯🇵 Kumiai

#### Get All Kumiai
```http
GET /api/kumiai?page=1&limit=10
```

#### Create New Kumiai
```http
POST /api/kumiai
Content-Type: application/json

{
  "nama_kumiai": "Tokyo Workers Cooperative",
  "kode_kumiai": "TWC001",
  "alamat_jepang": "1-1-1 Shibuya, Shibuya-ku",
  "kota_jepang": "Tokyo",
  "prefektur": "Tokyo",
  "kontak_person": "Tanaka San",
  "nomor_telepon": "+81-3-1234-5678",
  "email": "<EMAIL>"
}
```

### 💼 Job Order

#### Get All Job Orders
```http
GET /api/job-order?page=1&limit=10&status=published&perusahaan_id=1
```

#### Create New Job Order
```http
POST /api/job-order
Content-Type: application/json

{
  "perusahaan_id": 1,
  "kumiai_id": 1,
  "judul_pekerjaan": "Factory Worker",
  "deskripsi_pekerjaan": "Manufacturing assembly line work",
  "posisi": "Production Operator",
  "bidang_kerja": "Manufacturing",
  "jenis_kelamin": "L/P",
  "usia_min": 20,
  "usia_max": 35,
  "pendidikan_min": "SMA",
  "gaji_pokok": 180000,
  "tunjangan": 20000,
  "jumlah_kuota": 10,
  "tanggal_buka": "2024-01-01",
  "tanggal_tutup": "2024-03-01"
}
```

### 📊 Dashboard

#### Get Dashboard Statistics
```http
GET /api/dashboard/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_siswa": 524,
    "siswa_aktif_jepang": 245,
    "siswa_menunggu": 89,
    "perusahaan_mitra": 127,
    "lpk_aktif": 45,
    "kumiai_aktif": 23
  }
}
```

#### Get Monthly Trends
```http
GET /api/dashboard/trends?year=2024
```

### 📄 Documents

#### Upload Document
```http
POST /api/dokumen/upload
Content-Type: multipart/form-data

{
  "siswa_id": 1,
  "jenis_dokumen_id": 1,
  "file": [File object]
}
```

#### Get Student Documents
```http
GET /api/dokumen/siswa/[siswa_id]
```

## ⚡ Rate Limiting

API mengimplementasikan rate limiting untuk mencegah abuse:

- **Authenticated Users**: 1000 requests per hour
- **Unauthenticated**: 100 requests per hour
- **Upload Endpoints**: 50 requests per hour

Rate limit headers:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 📝 Examples

### JavaScript/TypeScript Client
```typescript
// API Client class
class MagangJepangAPI {
  private baseURL = 'http://localhost:3000/api';
  private token: string | null = null;

  async login(email: string, password: string) {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    const data = await response.json();
    if (data.success) {
      this.token = data.data.token;
    }
    return data;
  }

  async getSiswa(params?: { page?: number; limit?: number; search?: string }) {
    const url = new URL(`${this.baseURL}/siswa`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.set(key, value.toString());
      });
    }

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }
}

// Usage
const api = new MagangJepangAPI();
await api.login('<EMAIL>', 'password');
const siswa = await api.getSiswa({ page: 1, limit: 10 });
```

### cURL Examples
```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Get students
curl -X GET "http://localhost:3000/api/siswa?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Create student
curl -X POST http://localhost:3000/api/siswa \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"nama_lengkap":"John Doe","nik":"1234567890123456",...}'
```

---

**Last Updated**: 2025-01-11  
**API Version**: 1.0.0
