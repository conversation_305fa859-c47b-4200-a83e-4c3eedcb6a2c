# 🔄 Git Workflow Guide

## 📋 Daftar Isi

- [Git Configuration](#-git-configuration)
- [Branch Strategy](#-branch-strategy)
- [Commit Guidelines](#-commit-guidelines)
- [Pull Request Workflow](#-pull-request-workflow)
- [Release Process](#-release-process)
- [Hotfix Process](#-hotfix-process)
- [Best Practices](#-best-practices)

## ⚙️ Git Configuration

### Initial Setup
```bash
# Set your identity
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Set default branch name
git config --global init.defaultBranch main

# Set pull strategy
git config --global pull.rebase false

# Enable helpful colorization
git config --global color.ui auto

# Set default editor
git config --global core.editor "code --wait"  # VS Code
# git config --global core.editor "nano"       # Nano
```

### Project-Specific Configuration
```bash
# Navigate to project directory
cd /path/to/dashboard-magang-jepang

# Set project-specific config (optional)
git config user.name "Your Project Name"
git config user.email "<EMAIL>"
```

## 🌿 Branch Strategy

Kami menggunakan **Git Flow** yang disederhanakan:

### Main Branches
- **`main`** - Production-ready code
- **`develop`** - Integration branch untuk development

### Supporting Branches
- **`feature/*`** - New features
- **`bugfix/*`** - Bug fixes
- **`hotfix/*`** - Critical production fixes
- **`release/*`** - Release preparation

### Branch Naming Convention
```bash
# Features
feature/user-authentication
feature/siswa-management
feature/dashboard-analytics

# Bug fixes
bugfix/login-validation
bugfix/chart-rendering

# Hotfixes
hotfix/security-patch
hotfix/critical-bug-fix

# Releases
release/v1.0.0
release/v1.1.0
```

## 📝 Commit Guidelines

### Commit Message Format
Menggunakan **Conventional Commits**:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks
- **perf**: Performance improvements
- **ci**: CI/CD changes

### Examples
```bash
# Feature
git commit -m "feat(auth): add user login functionality"

# Bug fix
git commit -m "fix(dashboard): resolve chart data loading issue"

# Documentation
git commit -m "docs: update API documentation for siswa endpoints"

# Breaking change
git commit -m "feat(database)!: migrate to Supabase

BREAKING CHANGE: database connection strings have changed"
```

### Commit Best Practices
- Use present tense ("add feature" not "added feature")
- Use imperative mood ("move cursor to..." not "moves cursor to...")
- Limit first line to 72 characters
- Reference issues and pull requests when applicable
- Include breaking change information

## 🔄 Pull Request Workflow

### 1. Create Feature Branch
```bash
# Update develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/new-feature-name

# Work on your feature...
git add .
git commit -m "feat: implement new feature"

# Push branch
git push origin feature/new-feature-name
```

### 2. Create Pull Request
1. Go to GitHub repository
2. Click "New Pull Request"
3. Select `develop` as base branch
4. Select your feature branch as compare branch
5. Fill out PR template:

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## How Has This Been Tested?
- [ ] Unit tests
- [ ] Integration tests
- [ ] Manual testing

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

### 3. Code Review Process
- At least 1 approval required
- All CI checks must pass
- No merge conflicts
- Documentation updated if needed

### 4. Merge Strategy
```bash
# Squash and merge for features
# Merge commit for releases
# Rebase and merge for small fixes
```

## 🚀 Release Process

### 1. Prepare Release
```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# Update version numbers
# Update CHANGELOG.md
# Final testing

git add .
git commit -m "chore(release): prepare v1.0.0"
git push origin release/v1.0.0
```

### 2. Create Release PR
- Create PR from `release/v1.0.0` to `main`
- Review and test thoroughly
- Merge to main

### 3. Tag Release
```bash
git checkout main
git pull origin main
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0
```

### 4. Merge Back to Develop
```bash
git checkout develop
git merge main
git push origin develop
```

## 🔥 Hotfix Process

### 1. Create Hotfix Branch
```bash
# Create hotfix from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-fix

# Fix the issue
git add .
git commit -m "fix: resolve critical security issue"
git push origin hotfix/critical-fix
```

### 2. Deploy Hotfix
- Create PR to `main`
- Fast-track review and merge
- Tag new version
- Deploy to production

### 3. Merge to Develop
```bash
git checkout develop
git merge main
git push origin develop
```

## ✅ Best Practices

### Daily Workflow
```bash
# Start of day - update your branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature

# Work and commit regularly
git add .
git commit -m "feat: add initial implementation"

# Push regularly
git push origin feature/your-feature

# End of day - create PR if ready
```

### Before Committing
```bash
# Check what you're committing
git status
git diff

# Stage specific files
git add specific-file.ts
git add .

# Commit with meaningful message
git commit -m "feat(component): add user profile component"
```

### Keeping Branch Updated
```bash
# Rebase your feature branch with latest develop
git checkout feature/your-feature
git fetch origin
git rebase origin/develop

# Resolve conflicts if any
git add .
git rebase --continue

# Force push (only for feature branches)
git push --force-with-lease origin feature/your-feature
```

### Cleaning Up
```bash
# Delete merged feature branch locally
git branch -d feature/completed-feature

# Delete remote branch
git push origin --delete feature/completed-feature

# Clean up tracking branches
git remote prune origin
```

## 🛠 Useful Git Commands

### Information
```bash
# View commit history
git log --oneline --graph --decorate

# View changes
git diff
git diff --staged

# View branch information
git branch -a
git remote -v
```

### Undoing Changes
```bash
# Undo last commit (keep changes)
git reset --soft HEAD~1

# Undo last commit (discard changes)
git reset --hard HEAD~1

# Undo specific file
git checkout -- filename.ts

# Undo staged changes
git reset HEAD filename.ts
```

### Stashing
```bash
# Stash current changes
git stash

# Stash with message
git stash save "work in progress on feature X"

# List stashes
git stash list

# Apply stash
git stash apply
git stash pop

# Drop stash
git stash drop
```

## 🔧 Git Hooks

### Pre-commit Hook
Create `.git/hooks/pre-commit`:

```bash
#!/bin/sh
# Run linting and tests before commit
npm run lint
npm run test
```

### Commit Message Hook
Create `.git/hooks/commit-msg`:

```bash
#!/bin/sh
# Validate commit message format
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "Invalid commit message format!"
    echo "Use: type(scope): description"
    exit 1
fi
```

---

**Repository**: https://github.com/tabahsetyoaji/dashboard-jepang.git  
**Last Updated**: 2025-01-11
