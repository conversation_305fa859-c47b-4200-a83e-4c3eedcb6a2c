# 📊 Excel Import Implementation Guide

## 🎯 Overview

Fitur Excel Import memungkinkan pengguna untuk mengimport data siswa secara massal dari file Excel (.xlsx/.xls). Sistem ini dilengkapi dengan:

- **Auto-Detection**: Deteksi otomatis format kolom Excel
- **Auto-Create Entities**: Membuat LPK, Kumiai, dan <PERSON> yang belum ada
- **Data Validation**: Validasi lengkap dengan error reporting
- **Preview & Confirmation**: Preview data sebelum import final
- **Progress Tracking**: Real-time progress monitoring
- **Error Handling**: Robust error handling dan recovery

## 🏗️ Architecture

### 1. Type Definitions (`lib/types/excel-import.ts`)

```typescript
// Core data structure for Excel rows
interface ExcelRowData {
  // Personal Data (Required)
  nama_lengkap: string
  nik: string
  jenis_kelamin: 'L' | 'P'
  tempat_lahir: string
  tanggal_lahir: string | Date
  agama: string
  
  // Address (Required)
  alamat_lengkap: string
  kelurahan: string
  kecamatan: string
  kota_kabupaten: string
  provinsi: string
  
  // Contact (Required)
  nomor_hp: string
  email?: string
  
  // Education (Required)
  pendidikan_terakhir: 'SD' | 'SMP' | 'SMA' | 'SMK' | 'D3' | 'S1'
  nama_sekolah: string
  tahun_lulus: number
  
  // LPK Information (Required for LPK, Optional details)
  nama_lpk: string
  alamat_lpk?: string
  kota_lpk?: string
  // ... more LPK fields
  
  // Kumiai Information (Optional)
  nama_kumiai?: string
  kode_kumiai?: string
  // ... more Kumiai fields
  
  // Perusahaan Information (Optional)
  nama_perusahaan?: string
  // ... more Perusahaan fields
  
  // Placement Information (Optional)
  tanggal_penempatan?: string | Date
  // ... more placement fields
}

// Column mapping for auto-detection
interface ExcelColumnMapping {
  [excelColumn: string]: keyof ExcelRowData
}

// Validation results
interface ImportValidationError {
  row: number
  column: string
  field: keyof ExcelRowData
  value: any
  error: string
  severity: 'error' | 'warning'
}

// Import results and statistics
interface ImportResult {
  success: boolean
  totalRows: number
  successfulImports: number
  errors: ImportValidationError[]
  warnings: ImportValidationError[]
  createdEntities: {
    lpk: number
    kumiai: number
    perusahaan: number
    siswa: number
    penempatan: number
  }
  message: string
}
```

### 2. Service Layer (`lib/services/excel-import.service.ts`)

#### Core Methods

```typescript
class ExcelImportService {
  // Read Excel file and convert to JSON
  static async readExcelFile(file: File): Promise<{headers: string[], data: any[]}>
  
  // Auto-detect column mapping
  static detectColumnMapping(headers: string[]): ExcelColumnMapping
  
  // Convert raw data to typed format
  static convertToExcelRowData(headers: string[], rows: any[], mapping: ExcelColumnMapping): ExcelRowData[]
  
  // Validate Excel data
  static validateExcelData(data: ExcelRowData[]): ImportValidationError[]
  
  // Create preview for user confirmation
  static async createPreviewData(file: File): Promise<ImportPreviewData>
  
  // Auto-create entities
  static async findOrCreateLPK(lpkData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult>
  static async findOrCreateKumiai(kumiaiData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult | null>
  static async findOrCreatePerusahaan(perusahaanData: Partial<ExcelRowData>, kumiaiId?: string): Promise<EntityAutoCreateResult | null>
  
  // Execute final import
  static async importData(previewData: ImportPreviewData): Promise<ImportResult>
}
```

#### Auto-Detection Logic

```typescript
// Predefined column mappings for common formats
const DEFAULT_COLUMN_MAPPINGS = {
  standard: {
    'Nama Lengkap': 'nama_lengkap',
    'NIK': 'nik',
    'Jenis Kelamin': 'jenis_kelamin',
    // ... more mappings
  },
  english: {
    'Full Name': 'nama_lengkap',
    'National ID': 'nik',
    'Gender': 'jenis_kelamin',
    // ... more mappings
  }
}

// Auto-detection calculates best score match
private static calculateMappingScore(headers: string[], mapping: ExcelColumnMapping): number {
  let score = 0
  headers.forEach(header => {
    if (mapping[header]) {
      score += 1
      if (REQUIRED_FIELDS.includes(mapping[header])) {
        score += 2 // Extra weight for required fields
      }
    }
  })
  return score
}
```

### 3. UI Components

#### Excel Import Modal (`app/siswa/components/excel-import-modal.tsx`)

**Features:**
- Multi-step process (Upload → Preview → Import → Results)
- Drag & drop file upload
- Excel template download with sample data
- Data validation visualization
- Progress tracking
- Error reporting

### Excel Template

The system provides a comprehensive Excel template (.xlsx) that includes:

#### Template Structure (35+ columns):
1. **Personal Data** (14 columns):
   - nama_lengkap, nik, tanggal_lahir, tempat_lahir, jenis_kelamin
   - agama, alamat, provinsi, kota_kabupaten, kecamatan, kelurahan
   - kode_pos, nomor_telepon, email

2. **Education Data** (4 columns):
   - pendidikan_terakhir, nama_sekolah, jurusan, tahun_lulus

3. **Family Data** (7 columns):
   - nama_ayah, nama_ibu, nomor_telepon_orangtua, alamat_orangtua
   - pekerjaan_ayah, pekerjaan_ibu, penghasilan_orangtua

4. **LPK Information** (3 columns - REQUIRED):
   - nama_lpk, alamat_lpk, nomor_telepon_lpk

5. **Kumiai Information** (3 columns - OPTIONAL):
   - nama_kumiai, alamat_kumiai, nomor_telepon_kumiai

6. **Perusahaan Information** (3 columns - OPTIONAL):
   - nama_perusahaan, alamat_perusahaan, nomor_telepon_perusahaan

7. **Penempatan Information** (4 columns - OPTIONAL):
   - prefecture, kota_penempatan, tanggal_mulai_penempatan, status_penempatan

#### Sample Data
Template includes 3 complete sample rows showing:
- Various education levels (SMA, SMK)
- Different prefectures (Tokyo, Osaka, Kyoto)
- Complete LPK, Kumiai, and company information
- Active placement status examples

The template is generated dynamically using the xlsx library and formatted for easy reading with proper column widths.

**Flow:**
1. **Upload Tab**: File selection + template download
2. **Preview Tab**: Data preview + validation errors + statistics
3. **Result Tab**: Import progress + final results + detailed reporting

```typescript
function ExcelImportModal({ open, onOpenChange, onImportComplete }) {
  const [file, setFile] = useState<File | null>(null)
  const [previewData, setPreviewData] = useState<ImportPreviewData | null>(null)
  const [processingStatus, setProcessingStatus] = useState<ImportProcessingStatus>({
    status: 'idle',
    progress: 0,
    currentStep: '',
    processedRows: 0,
    totalRows: 0,
    errors: []
  })
  
  // File processing flow
  const handleFileSelect = async (selectedFile: File) => {
    const preview = await ExcelImportService.createPreviewData(selectedFile)
    setPreviewData(preview)
  }
  
  const handleImport = async () => {
    const result = await ExcelImportService.importData(previewData)
    setImportResult(result)
  }
}
```

## 🔧 Implementation Details

### 1. Data Validation

#### Field Validation Rules
```typescript
const REQUIRED_FIELDS = [
  'nama_lengkap', 'nik', 'jenis_kelamin', 'tempat_lahir', 
  'tanggal_lahir', 'agama', 'alamat_lengkap', 'kelurahan',
  'kecamatan', 'kota_kabupaten', 'provinsi', 'nomor_hp',
  'pendidikan_terakhir', 'nama_sekolah', 'tahun_lulus', 'nama_lpk'
]

// NIK validation (16 digits)
if (row.nik && !/^\d{16}$/.test(String(row.nik))) {
  errors.push({
    row: rowNumber,
    field: 'nik',
    error: 'NIK harus 16 digit angka',
    severity: 'error'
  })
}

// Phone number validation
if (row.nomor_hp && !/^(\+62|62|0)[0-9]{8,12}$/.test(String(row.nomor_hp).replace(/[\s-]/g, ''))) {
  errors.push({
    row: rowNumber,
    field: 'nomor_hp',
    error: 'Format nomor HP tidak valid',
    severity: 'warning'
  })
}
```

#### Data Normalization
```typescript
// Gender normalization
if (field === 'jenis_kelamin') {
  const gender = String(value).toUpperCase()
  if (gender === 'LAKI-LAKI' || gender === 'L' || gender === 'MALE') return 'L'
  if (gender === 'PEREMPUAN' || gender === 'P' || gender === 'FEMALE') return 'P'
  return value
}

// Education level normalization
if (field === 'pendidikan_terakhir') {
  const education = String(value).toUpperCase()
  if (education.includes('SMA') || education.includes('SMU')) return 'SMA'
  if (education.includes('SMK') || education.includes('STM')) return 'SMK'
  // ... more mappings
}

// Date handling
if (field.includes('tanggal_') || field === 'tanggal_lahir') {
  if (typeof value === 'number') {
    // Excel date serial number
    const date = XLSX.SSF.parse_date_code(value)
    return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`
  }
}
```

### 2. Auto-Create Entities

#### LPK Auto-Creation
```typescript
static async findOrCreateLPK(lpkData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult> {
  const { nama_lpk, alamat_lpk, kota_lpk, provinsi_lpk, kontak_person_lpk, nomor_telepon_lpk, email_lpk } = lpkData
  
  // Try to find existing LPK by name (case-insensitive)
  const { data: existingLPK } = await supabase
    .from('lpk_mitra')
    .select('id, nama_lpk')
    .ilike('nama_lpk', nama_lpk)
    .single()

  if (existingLPK) {
    return { id: existingLPK.id, name: existingLPK.nama_lpk, isNew: false }
  }

  // Create new LPK with provided or default values
  const newLPK = {
    nama_lpk,
    alamat_lengkap: alamat_lpk || 'Alamat tidak tersedia',
    kota: kota_lpk || 'Kota tidak tersedia',
    provinsi: provinsi_lpk || 'Provinsi tidak tersedia',
    nama_pimpinan: 'Belum diisi',
    kontak_person: kontak_person_lpk || 'Belum diisi',
    nomor_telepon: nomor_telepon_lpk || '-',
    email: email_lpk || '',
    status: 'aktif',
    tanggal_kerjasama: '',
    catatan: 'Auto-created dari import Excel'
  }

  const { data: createdLPK } = await supabase
    .from('lpk_mitra')
    .insert(newLPK)
    .select('id, nama_lpk')
    .single()

  return { id: createdLPK.id, name: createdLPK.nama_lpk, isNew: true }
}
```

#### Kumiai Auto-Creation (Optional)
```typescript
static async findOrCreateKumiai(kumiaiData: Partial<ExcelRowData>): Promise<EntityAutoCreateResult | null> {
  const { nama_kumiai, kode_kumiai, alamat_jepang, kota_jepang, prefektur } = kumiaiData
  
  if (!nama_kumiai && !kode_kumiai) {
    return null // Kumiai is optional
  }

  // Try to find by code first, then by name
  let query = supabase.from('kumiai').select('id, nama_kumiai')
  
  if (kode_kumiai) {
    query = query.eq('kode_kumiai', kode_kumiai)
  } else {
    query = query.ilike('nama_kumiai', nama_kumiai!)
  }

  const { data: existingKumiai } = await query.single()

  if (existingKumiai) {
    return { id: existingKumiai.id, name: existingKumiai.nama_kumiai, isNew: false }
  }

  // Create new Kumiai
  const newKumiai = {
    nama_kumiai: nama_kumiai || 'Kumiai dari Import',
    kode_kumiai: kode_kumiai || `IMPORT_${Date.now()}`,
    alamat_jepang: alamat_jepang || 'Alamat Jepang tidak tersedia',
    kota_jepang: kota_jepang || 'Kota tidak tersedia',
    prefektur: prefektur || 'Prefektur tidak tersedia',
    kontak_person: 'Belum diisi',
    nomor_telepon: '-',
    email: '',
    status: 'aktif',
    keterangan: 'Auto-created dari import Excel'
  }

  const { data: createdKumiai } = await supabase
    .from('kumiai')
    .insert(newKumiai)
    .select('id, nama_kumiai')
    .single()

  return { id: createdKumiai.id, name: createdKumiai.nama_kumiai, isNew: true }
}
```

### 3. Import Process Flow

```typescript
static async importData(previewData: ImportPreviewData): Promise<ImportResult> {
  const { data, errors } = previewData
  
  // Only process valid rows (no critical errors)
  const validRows = data.filter((_, index) => {
    const rowNumber = index + 2
    return !errors.some(e => e.row === rowNumber && e.severity === 'error')
  })

  // Track created entities to avoid duplicates
  const processedEntities = {
    lpk: new Map<string, string>(),
    kumiai: new Map<string, string>(),
    perusahaan: new Map<string, string>()
  }

  const result: ImportResult = {
    success: false,
    totalRows: data.length,
    successfulImports: 0,
    errors: [...errors],
    warnings: [],
    createdEntities: { lpk: 0, kumiai: 0, perusahaan: 0, siswa: 0, penempatan: 0 },
    message: ''
  }

  for (const rowData of validRows) {
    try {
      // 1. Find or create LPK
      const lpkResult = await this.findOrCreateLPK(rowData)
      if (lpkResult.isNew && !processedEntities.lpk.has(lpkResult.name)) {
        result.createdEntities.lpk++
        processedEntities.lpk.set(lpkResult.name, lpkResult.id)
      }

      // 2. Find or create Kumiai (optional)
      let kumiaiResult = null
      if (rowData.nama_kumiai || rowData.kode_kumiai) {
        kumiaiResult = await this.findOrCreateKumiai(rowData)
        if (kumiaiResult?.isNew && !processedEntities.kumiai.has(kumiaiResult.name)) {
          result.createdEntities.kumiai++
          processedEntities.kumiai.set(kumiaiResult.name, kumiaiResult.id)
        }
      }

      // 3. Find or create Perusahaan (optional, requires Kumiai)
      let perusahaanResult = null
      if (rowData.nama_perusahaan && kumiaiResult) {
        perusahaanResult = await this.findOrCreatePerusahaan(rowData, kumiaiResult.id)
        if (perusahaanResult?.isNew && !processedEntities.perusahaan.has(perusahaanResult.name)) {
          result.createdEntities.perusahaan++
          processedEntities.perusahaan.set(perusahaanResult.name, perusahaanResult.id)
        }
      }

      // 4. Create Siswa
      const siswaData = {
        lpk_id: lpkResult.id,
        nama_lengkap: rowData.nama_lengkap,
        nik: rowData.nik,
        // ... all required fields
        status_pendaftaran: rowData.status_pendaftaran || 'approved',
        tanggal_daftar: rowData.tanggal_daftar || new Date().toISOString().split('T')[0]
      }

      const { data: createdSiswa } = await supabase
        .from('siswa')
        .insert(siswaData)
        .select('id')
        .single()

      result.createdEntities.siswa++

      // 5. Create Penempatan (optional)
      if (rowData.tanggal_penempatan && perusahaanResult && kumiaiResult) {
        const penempatanData = {
          siswa_id: createdSiswa.id,
          perusahaan_id: perusahaanResult.id,
          kumiai_id: kumiaiResult.id,
          tanggal_penempatan: rowData.tanggal_penempatan,
          tanggal_keberangkatan: rowData.tanggal_keberangkatan,
          tanggal_kepulangan: rowData.tanggal_kepulangan,
          status_penempatan: rowData.status_penempatan || 'ditempatkan',
          posisi_kerja: rowData.posisi_kerja || 'Staff',
          gaji_aktual: rowData.gaji_aktual || 0,
          alamat_kerja: rowData.alamat_perusahaan || 'Alamat kerja tidak tersedia'
        }

        const { error: penempatanError } = await supabase
          .from('penempatan_siswa')
          .insert(penempatanData)

        if (!penempatanError) {
          result.createdEntities.penempatan++
        }
      }

      result.successfulImports++
    } catch (error) {
      result.errors.push({
        row: data.indexOf(rowData) + 2,
        column: 'general',
        field: 'nama_lengkap',
        value: rowData.nama_lengkap,
        error: `Error memproses baris: ${error}`,
        severity: 'error'
      })
    }
  }

  result.success = result.successfulImports > 0
  result.message = `Import selesai: ${result.successfulImports}/${result.totalRows} data berhasil diimport`

  return result
}
```

## 📝 Usage Guide

### 1. Template CSV Format

#### Required Columns (Minimum):
```
Nama Lengkap | NIK | Jenis Kelamin | Tempat Lahir | Tanggal Lahir | Agama | 
Alamat Lengkap | Kelurahan | Kecamatan | Kota/Kabupaten | Provinsi | 
Nomor HP | Pendidikan Terakhir | Nama Sekolah | Tahun Lulus
```

**Note**: Template CSV hanya berisi data dasar siswa. Jika tidak ada informasi LPK dalam file, sistem akan menggunakan "LPK Import Default" yang dibuat otomatis.

#### Optional Columns:
```
Email | Jurusan | Nama Ayah | Nama Ibu | Alamat Keluarga | Nomor HP Keluarga |
Alamat LPK | Kota LPK | Provinsi LPK | Kontak Person LPK | Nomor Telepon LPK | Email LPK |
Nama Kumiai | Kode Kumiai | Alamat Jepang | Kota Jepang | Prefektur | 
Kontak Person Kumiai | Nomor Telepon Kumiai | Email Kumiai |
Nama Perusahaan | Alamat Perusahaan | Kota Perusahaan | Prefektur Perusahaan | 
Bidang Usaha | Kontak Person Perusahaan | Nomor Telepon Perusahaan | Email Perusahaan |
Tanggal Penempatan | Tanggal Keberangkatan | Tanggal Kepulangan | 
Status Penempatan | Posisi Kerja | Gaji Aktual | Status Pendaftaran | 
Tanggal Daftar | Catatan
```

### 2. Data Format Guidelines

#### Dates
- Format: `YYYY-MM-DD` or Excel date format
- Examples: `1995-01-01`, `2023-12-25`

#### Gender
- Valid values: `L`, `P`, `Laki-laki`, `Perempuan`, `Male`, `Female`

#### Education Level
- Valid values: `SD`, `SMP`, `SMA`, `SMK`, `D3`, `S1`
- Also accepts: `SMU`, `SLTA`, `STM`, `Diploma`, `Sarjana`

#### Phone Numbers
- Format: Indonesia phone numbers
- Examples: `081234567890`, `+6281234567890`, `6281234567890`

#### NIK
- Must be exactly 16 digits
- Example: `1234567890123456`

### 3. Import Process

1. **Upload File**: Select .xlsx or .xls file
2. **Auto-Detection**: System detects column mapping
3. **Preview & Validation**: Review data and errors
4. **Confirmation**: Confirm valid rows for import
5. **Processing**: Real-time progress tracking
6. **Results**: Detailed import report

### 4. Error Handling

#### Error Types:
- **Error**: Critical issues that prevent import
- **Warning**: Non-critical issues (data still imported)

#### Common Errors:
- Missing required fields
- Invalid NIK format (not 16 digits)
- Invalid gender values
- Invalid education level

#### Common Warnings:
- Invalid phone number format
- Invalid email format
- Invalid graduation year

### 5. Auto-Create Logic

#### LPK (Required):
- If LPK name provided: matched by name (case-insensitive) or created if doesn't exist
- If no LPK name provided: uses "LPK Import Default" (created automatically if doesn't exist)
- Uses provided details or defaults

#### Kumiai (Optional):
- Only created if `nama_kumiai` or `kode_kumiai` provided
- Matched by code first, then name
- Auto-generates code if not provided

#### Perusahaan (Optional):
- Only created if `nama_perusahaan` provided AND Kumiai exists
- Matched by name (case-insensitive)
- Requires Kumiai relationship

#### Penempatan (Optional):
- Only created if `tanggal_penempatan` provided AND both Perusahaan and Kumiai exist
- Links Siswa to Perusahaan through Kumiai

## 🛠️ Customization

### 1. Adding New Column Mappings

```typescript
// Add to DEFAULT_COLUMN_MAPPINGS in excel-import.ts
const CUSTOM_MAPPING = {
  'Custom Column Name': 'target_field_name',
  'Another Column': 'another_field'
}

DEFAULT_COLUMN_MAPPINGS.custom = CUSTOM_MAPPING
```

### 2. Adding New Validation Rules

```typescript
// Add to validateExcelData method
if (row.custom_field && !customValidationRule(row.custom_field)) {
  errors.push({
    row: rowNumber,
    column: 'custom_field',
    field: 'custom_field',
    value: row.custom_field,
    error: 'Custom validation error message',
    severity: 'error'
  })
}
```

### 3. Extending Data Normalization

```typescript
// Add to convertCellValue method
if (field === 'custom_field') {
  const normalized = normalizeCustomValue(value)
  return normalized
}
```

## 🚀 Performance Considerations

### 1. Large File Handling
- Chunk processing for files > 1000 rows
- Memory management for large datasets
- Progress tracking for user feedback

### 2. Database Optimization
- Batch inserts where possible
- Entity caching to avoid duplicate lookups
- Transaction management for data consistency

### 3. Error Recovery
- Partial import success (import valid rows even if some fail)
- Detailed error reporting for troubleshooting
- Resume capability for interrupted imports

## 🔒 Security Considerations

### 1. File Validation
- File type checking (.xlsx/.xls only)
- File size limits
- Malicious file protection

### 2. Data Sanitization
- Input validation and sanitization
- SQL injection prevention
- XSS protection for displayed data

### 3. Access Control
- User permission checking
- Audit logging of import operations
- Data privacy compliance

## 📈 Future Enhancements

### 1. Advanced Features
- Custom field mapping interface
- Import templates management
- Scheduled/automated imports
- Data transformation rules

### 2. Performance Improvements
- Background processing
- Import queue management
- Distributed processing
- Caching mechanisms

### 3. Integration
- API endpoints for programmatic import
- Webhook notifications
- External system integration
- Real-time sync capabilities

---

## 🎯 Quick Start

1. **Prepare Excel File**: Use the template or match the required format
2. **Access Import**: Click "Import Excel" button in Siswa page
3. **Upload & Preview**: Upload file and review validation results
4. **Execute Import**: Confirm and start the import process
5. **Review Results**: Check import statistics and handle any errors

For detailed troubleshooting and advanced usage, refer to the specific sections above. 