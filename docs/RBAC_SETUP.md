# 🔐 RBAC System Setup Guide

## Overview

Sistem Role-Based Access Control (RBAC) untuk Dashboard Magang Jepang telah diimplementasikan dengan 6 level akses yang berbeda:

1. **Administrator** - Akses penuh ke semua fitur dan data sistem
2. **LPK Mitra** - <PERSON><PERSON> bisa akses data yang diinputkan, tidak bisa akses data LPK mitra lain
3. **Divisi Education** - Akses data menu pendidikan
4. **Divisi Recruitment** - Akses menu recruitment
5. **Pemberangkatan** - Akses menu pemberkasan dan pemberangkatan
6. **Pimpinan** - Melihat laporan data semua (read-only)

## Database Setup

### 1. Run Migrations

**Option A: Automatic (Recommended)**
```bash
# Apply all migrations at once
supabase db push
```

**Option B: Manual (if you encounter issues)**
```bash
# Run the comprehensive fix migration
supabase migration up --file 20250131000004_fix_rbac_setup.sql

# Then run RLS policies
supabase migration up --file 20250131000003_implement_rls_policies.sql
```

**If you get "relation does not exist" errors:**
```bash
# Reset and start fresh
supabase db reset

# Then apply all migrations
supabase db push
```

### 2. Verify Tables Created

Pastikan tabel-tabel berikut telah dibuat:
- `roles`
- `permissions`
- `role_permissions`
- `user_role_assignments`
- Updated `user_profiles`

### 3. Check Default Data

Verifikasi data default telah diinsert:
- 6 roles dengan permissions yang sesuai
- Permissions untuk semua modules
- Role-permission assignments

## Initial Admin Setup

### 1. Create Admin User via Supabase Dashboard

1. Buka Supabase Dashboard > Authentication > Users
2. Create new user dengan email dan password
3. Copy User ID

### 2. Insert Admin Profile

```sql
-- Insert admin profile
INSERT INTO user_profiles (id, full_name, username, is_active)
VALUES ('USER_ID_FROM_STEP_1', 'Administrator', 'admin', true);

-- Assign administrator role
INSERT INTO user_role_assignments (user_id, role_id, is_active)
SELECT 'USER_ID_FROM_STEP_1', id, true
FROM roles WHERE name = 'administrator';
```

## User Management

### Creating Users

1. **Via Supabase Dashboard** (Recommended for initial setup):
   - Create user in Authentication > Users
   - Insert profile in `user_profiles` table
   - Assign roles via `user_role_assignments`

2. **Via Application** (After admin setup):
   - Use User Management page
   - Admin can create users and assign roles
   - Automatic profile creation and role assignment

### Role Assignment

```sql
-- Assign role to user
SELECT assign_user_role(
    'user_id'::uuid,
    'role_name',
    'assigned_by_user_id'::uuid,
    'lpk_mitra_id'::uuid, -- Optional, for LPK Mitra users
    null -- expires_at, optional
);

-- Revoke role from user
SELECT revoke_user_role('user_id'::uuid, 'role_name');
```

## Permission System

### Module Permissions

Setiap module memiliki 4 jenis permission:
- `create` - Tambah data
- `read` - Lihat data
- `update` - Edit data
- `delete` - Hapus data

### Available Modules

- `dashboard` - Dashboard utama
- `siswa` - Data siswa
- `lpk_mitra` - Data LPK Mitra
- `job_order` - Job orders
- `kumiai` - Data Kumiai
- `pendidikan` - Program pendidikan
- `penempatan` - Data penempatan
- `dokumen` - Dokumen siswa
- `pendaftaran` - Pendaftaran siswa
- `users` - User management
- `roles` - Role management
- `reports` - Laporan

### Default Role Permissions

#### Administrator
- Full access to all modules and actions

#### LPK Mitra
- `dashboard.read`
- `siswa.*` (CRUD for own students only)
- `pendaftaran.*` (CRUD for own registrations)
- `dokumen.*` (CRUD for own documents)

#### Divisi Education
- `dashboard.read`
- `pendidikan.*` (CRUD)
- `siswa.read`, `siswa.update`
- `reports.read`

#### Divisi Recruitment
- `dashboard.read`
- `job_order.*` (CRUD)
- `kumiai.*` (CRUD)
- `lpk_mitra.read`
- `siswa.read`
- `reports.read`

#### Pemberangkatan
- `dashboard.read`
- `penempatan.*` (CRUD)
- `dokumen.*` (CRUD)
- `siswa.read`, `siswa.update`
- `reports.read`

#### Pimpinan
- Read-only access to all data modules
- `reports.read`, `reports.export`

## Row Level Security (RLS)

### LPK Mitra Data Isolation

LPK Mitra users hanya dapat mengakses:
- Siswa dari LPK mereka sendiri
- Dokumen dari siswa LPK mereka
- Penempatan dari siswa LPK mereka

### Implementation

RLS policies menggunakan helper functions:
- `auth.user_has_role(role_name)` - Check user role
- `auth.user_lpk_mitra_id()` - Get user's LPK Mitra ID

## Frontend Integration

### Authentication Context

```tsx
import { useAuth } from '@/lib/auth/auth-context'

function MyComponent() {
  const { hasPermission, hasRole, userProfile } = useAuth()
  
  // Check permission
  if (hasPermission('siswa', 'create')) {
    // Show create button
  }
  
  // Check role
  if (hasRole('administrator')) {
    // Show admin features
  }
}
```

### Permission Guards

```tsx
import { PermissionGuard } from '@/components/auth/permission-guard'

<PermissionGuard module="siswa" action="create">
  <Button>Tambah Siswa</Button>
</PermissionGuard>
```

### Route Protection

```tsx
import { withAuth } from '@/lib/auth/auth-context'

function ProtectedPage() {
  return <div>Protected content</div>
}

export default withAuth(ProtectedPage, { module: 'siswa', action: 'read' })
```

## Testing Checklist

### ✅ Database Setup
- [ ] Migrations applied successfully
- [ ] All tables created
- [ ] Default roles and permissions inserted
- [ ] RLS policies enabled

### ✅ User Management
- [ ] Admin user created and can login
- [ ] Can create new users
- [ ] Can assign/revoke roles
- [ ] User profile updates work

### ✅ Role Management
- [ ] Can view roles and permissions
- [ ] Can edit role permissions
- [ ] Permission matrix works
- [ ] Role assignments reflect in UI

### ✅ Access Control
- [ ] Navigation shows only permitted items
- [ ] Pages redirect if no permission
- [ ] CRUD operations respect permissions
- [ ] LPK Mitra sees only own data

### ✅ Data Isolation
- [ ] LPK Mitra A cannot see LPK Mitra B data
- [ ] RLS policies working correctly
- [ ] Admin can see all data
- [ ] Pimpinan has read-only access

## Troubleshooting

### Migration Issues

1. **"relation does not exist" error**
   ```bash
   # Reset database and start fresh
   supabase db reset
   supabase db push

   # Or run the fix migration
   supabase migration up --file 20250131000004_fix_rbac_setup.sql
   ```

2. **Migration order issues**
   ```bash
   # Check migration status
   supabase migration list

   # Apply specific migration
   supabase migration up --file filename.sql
   ```

### Common Issues

1. **User cannot login**
   - Check if user exists in auth.users
   - Verify user_profiles entry exists
   - Check if user has active role assignments

2. **Permission denied errors**
   - Verify RLS policies are enabled
   - Check user role assignments
   - Ensure permissions are correctly assigned to roles

3. **LPK Mitra seeing wrong data**
   - Check lpk_mitra_id in user_role_assignments
   - Verify RLS helper functions work
   - Test auth.user_lpk_mitra_id() function

### Debug Queries

```sql
-- Check user roles
SELECT * FROM v_user_roles WHERE id = 'user_id';

-- Check user permissions
SELECT * FROM v_user_permissions WHERE user_id = 'user_id';

-- Test RLS functions
SELECT auth.user_has_role('administrator');
SELECT auth.user_lpk_mitra_id();
```

## Security Considerations

1. **Password Policy**: Implement strong password requirements
2. **Session Management**: Configure appropriate JWT expiry
3. **Audit Trail**: Consider adding audit logs for sensitive operations
4. **Data Encryption**: Ensure sensitive data is encrypted at rest
5. **Regular Reviews**: Periodically review user roles and permissions

## Next Steps

1. Implement audit logging
2. Add password reset functionality
3. Create user activity monitoring
4. Add bulk user import/export
5. Implement advanced permission conditions
