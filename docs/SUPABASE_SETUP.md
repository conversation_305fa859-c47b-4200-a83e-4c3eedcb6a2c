# 🚀 Supabase Setup Guide

## 📋 Daftar Isi

- [Overview](#-overview)
- [Project Setup](#-project-setup)
- [Database Schema](#-database-schema)
- [Authentication Setup](#-authentication-setup)
- [Row Level Security](#-row-level-security)
- [Storage Setup](#-storage-setup)
- [Edge Functions](#-edge-functions)
- [Local Development](#-local-development)
- [Production Deployment](#-production-deployment)

## 🎯 Overview

Supabase menyediakan backend-as-a-service yang lengkap untuk Dashboard Sistem Magang Jepang, termasuk:

- **PostgreSQL Database** - Database utama dengan real-time subscriptions
- **Authentication** - Built-in auth dengan berbagai provider
- **Storage** - File storage untuk dokumen siswa
- **Edge Functions** - Serverless functions untuk business logic
- **Row Level Security** - Fine-grained access control
- **Auto-generated APIs** - REST dan GraphQL APIs

## 🏗 Project Setup

### 1. Create Supabase Project

1. **Buka Supabase Dashboard**
   - Kunjungi [https://supabase.com](https://supabase.com)
   - Sign up atau login dengan GitHub

2. **Create New Project**
   ```
   Organization: [Pilih atau buat baru]
   Project Name: Dashboard Magang Jepang
   Database Password: [Generate strong password - SIMPAN INI!]
   Region: Southeast Asia (Singapore)
   Pricing Plan: Free (untuk development) / Pro (untuk production)
   ```

3. **Wait for Project Creation**
   - Proses setup ~2-3 menit
   - Project akan tersedia di dashboard

### 2. Get Project Credentials

Dari Supabase Dashboard > Settings > API:

```bash
# Project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co

# Anon Key (public, safe untuk client-side)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Service Role Key (private, server-side only)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Database URL (dari Settings > Database)
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
```

## 🗄 Database Schema

### 1. Install Supabase CLI

```bash
# Install CLI
npm install -g supabase

# Login
supabase login

# Initialize project
supabase init

# Link to remote project
supabase link --project-ref your-project-ref
```

### 2. Create Migration Files

```bash
# Create initial migration
supabase migration new create_initial_schema
```

### 3. Database Schema (PostgreSQL)

Edit file `supabase/migrations/[timestamp]_create_initial_schema.sql`:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'operator', 'lpk_admin', 'viewer');
CREATE TYPE gender_type AS ENUM ('L', 'P');
CREATE TYPE education_level AS ENUM ('SD', 'SMP', 'SMA', 'SMK', 'D3', 'S1');
CREATE TYPE status_type AS ENUM ('aktif', 'nonaktif', 'suspended');

-- User Profiles (extends Supabase auth.users)
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    role user_role DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- LPK Mitra
CREATE TABLE lpk_mitra (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_lpk VARCHAR(200) NOT NULL,
    alamat_lengkap TEXT NOT NULL,
    kota VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    nama_pimpinan VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    website VARCHAR(200),
    status status_type DEFAULT 'aktif',
    tanggal_kerjasama DATE,
    catatan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Kumiai
CREATE TABLE kumiai (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nama_kumiai VARCHAR(200) NOT NULL,
    kode_kumiai VARCHAR(50) UNIQUE NOT NULL,
    alamat_jepang TEXT NOT NULL,
    kota_jepang VARCHAR(100) NOT NULL,
    prefektur VARCHAR(100) NOT NULL,
    kontak_person VARCHAR(100) NOT NULL,
    nomor_telepon VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    website VARCHAR(200),
    status status_type DEFAULT 'aktif',
    keterangan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Siswa
CREATE TABLE siswa (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    lpk_id UUID REFERENCES lpk_mitra(id) NOT NULL,
    
    -- Personal Data
    nama_lengkap VARCHAR(200) NOT NULL,
    nik VARCHAR(20) UNIQUE NOT NULL,
    tempat_lahir VARCHAR(100) NOT NULL,
    tanggal_lahir DATE NOT NULL,
    jenis_kelamin gender_type NOT NULL,
    agama VARCHAR(50) NOT NULL,
    
    -- Address
    alamat_lengkap TEXT NOT NULL,
    kelurahan VARCHAR(100) NOT NULL,
    kecamatan VARCHAR(100) NOT NULL,
    kota_kabupaten VARCHAR(100) NOT NULL,
    provinsi VARCHAR(100) NOT NULL,
    kode_pos VARCHAR(10),
    
    -- Contact
    nomor_hp VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    
    -- Education
    pendidikan_terakhir education_level NOT NULL,
    nama_sekolah VARCHAR(200) NOT NULL,
    tahun_lulus INTEGER NOT NULL,
    jurusan VARCHAR(100),
    
    -- Status
    status_pendaftaran VARCHAR(20) DEFAULT 'draft',
    tanggal_daftar DATE NOT NULL DEFAULT CURRENT_DATE,
    catatan TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_siswa_lpk_id ON siswa(lpk_id);
CREATE INDEX idx_siswa_nik ON siswa(nik);
CREATE INDEX idx_siswa_status ON siswa(status_pendaftaran);
CREATE INDEX idx_lpk_status ON lpk_mitra(status);
CREATE INDEX idx_kumiai_status ON kumiai(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lpk_mitra_updated_at BEFORE UPDATE ON lpk_mitra FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kumiai_updated_at BEFORE UPDATE ON kumiai FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_siswa_updated_at BEFORE UPDATE ON siswa FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 4. Apply Migration

```bash
# Push to remote database
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > types/supabase.ts
```

## 🔐 Authentication Setup

### 1. Configure Auth Providers

Dari Supabase Dashboard > Authentication > Providers:

```bash
# Email/Password (default enabled)
# Google OAuth (optional)
# GitHub OAuth (optional)
```

### 2. Auth Configuration

```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Server-side client
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)
```

### 3. Auth Hooks

```typescript
// hooks/useAuth.ts
import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return { user, loading }
}
```

## 🛡 Row Level Security (RLS)

### 1. Enable RLS

```sql
-- Enable RLS for all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE lpk_mitra ENABLE ROW LEVEL SECURITY;
ALTER TABLE kumiai ENABLE ROW LEVEL SECURITY;
ALTER TABLE siswa ENABLE ROW LEVEL SECURITY;
```

### 2. Create RLS Policies

```sql
-- User Profiles Policies
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admin can view all profiles
CREATE POLICY "Admins can view all profiles" ON user_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Siswa Policies
CREATE POLICY "Authenticated users can view siswa" ON siswa
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'operator', 'lpk_admin')
        )
    );

CREATE POLICY "Operators can manage siswa" ON siswa
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'operator')
        )
    );

-- LPK Admin can only manage their own students
CREATE POLICY "LPK admin can manage own students" ON siswa
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles up
            JOIN lpk_mitra lm ON lm.id = siswa.lpk_id
            WHERE up.id = auth.uid() 
            AND up.role = 'lpk_admin'
            -- Add logic to link user to specific LPK
        )
    );
```

## 📁 Storage Setup

### 1. Create Storage Bucket

```sql
-- Create documents bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('documents', 'documents', false);
```

### 2. Storage Policies

```sql
-- Upload policy
CREATE POLICY "Authenticated users can upload documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'documents' 
        AND auth.role() = 'authenticated'
    );

-- Download policy
CREATE POLICY "Users can view documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'documents' 
        AND auth.role() = 'authenticated'
    );

-- Delete policy (admin only)
CREATE POLICY "Admins can delete documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'documents' 
        AND EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

### 3. File Upload Helper

```typescript
// lib/storage.ts
import { supabase } from './supabase'

export async function uploadFile(
  file: File, 
  path: string, 
  bucket: string = 'documents'
) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file)

  if (error) throw error
  return data
}

export async function getFileUrl(
  path: string, 
  bucket: string = 'documents'
) {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)

  return data.publicUrl
}
```

## ⚡ Edge Functions

### 1. Create Edge Function

```bash
# Create function
supabase functions new send-notification

# Deploy function
supabase functions deploy send-notification
```

### 2. Example Edge Function

```typescript
// supabase/functions/send-notification/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (req) => {
  try {
    const { email, message, type } = await req.json()
    
    // Send email notification logic here
    // Using Deno's built-in fetch or email service
    
    return new Response(
      JSON.stringify({ success: true }),
      { headers: { "Content-Type": "application/json" } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    )
  }
})
```

## 🏠 Local Development

### 1. Start Local Supabase

```bash
# Start local development stack
supabase start

# This will start:
# - PostgreSQL database
# - Auth server
# - Storage server
# - Edge Functions runtime
```

### 2. Local Environment

```bash
# .env.local for development
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-local-service-role-key
```

### 3. Reset Local Database

```bash
# Reset to clean state
supabase db reset

# Apply migrations
supabase db push
```

## 🚀 Production Deployment

### 1. Environment Variables

```bash
# Production environment
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
```

### 2. Database Migrations

```bash
# Deploy migrations to production
supabase db push --linked

# Generate production types
supabase gen types typescript --linked > types/supabase.ts
```

### 3. Monitoring

- **Dashboard**: Monitor usage di Supabase Dashboard
- **Logs**: Real-time logs untuk database dan functions
- **Metrics**: Performance metrics dan usage statistics

---

**Last Updated**: 2025-01-11  
**Supabase Version**: Latest
